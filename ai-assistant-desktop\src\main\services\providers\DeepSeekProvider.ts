import fetch from 'node-fetch';
import { <PERSON><PERSON><PERSON><PERSON>ider } from '../LLMProviderInterface';
import type { 
  LLMProviderConfig, 
  LLMResponse, 
  StreamingLLMResponse, 
  Message 
} from '@shared/types';
import { DEFAULT_MODELS } from '@shared/constants';

export class DeepSeekProvider extends BaseLLMProvider {
  name = 'deepseek';
  supportedModels = DEFAULT_MODELS.deepseek as unknown as string[];

  async chat(
    messages: Message[], 
    config: LLMProviderConfig,
    systemPrompt?: string
  ): Promise<LLMResponse> {
    const url = config.baseUrl || 'https://api.deepseek.com/v1/chat/completions';
    const headers = this.buildHeaders(config);
    const formattedMessages = this.formatMessages(messages, systemPrompt);

    const body = {
      model: config.model,
      messages: formattedMessages,
      max_tokens: config.maxTokens || 4096,
      temperature: config.temperature || 0.7,
      stream: false,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`DeepSeek API error: ${error.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const choice = data.choices[0];

      return {
        content: choice.message.content,
        model: config.model,
        usage: data.usage && {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens,
        },
        finishReason: choice.finish_reason,
      };
    } catch (error: any) {
      throw new Error(`DeepSeek provider error: ${error.message}`);
    }
  }

  async* streamChat(
    messages: Message[], 
    config: LLMProviderConfig,
    systemPrompt?: string,
    onChunk?: (chunk: StreamingLLMResponse) => void
  ): AsyncGenerator<StreamingLLMResponse, void, unknown> {
    const url = config.baseUrl || 'https://api.deepseek.com/v1/chat/completions';
    const headers = this.buildHeaders(config);
    const formattedMessages = this.formatMessages(messages, systemPrompt);

    const body = {
      model: config.model,
      messages: formattedMessages,
      max_tokens: config.maxTokens || 4096,
      temperature: config.temperature || 0.7,
      stream: true,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`DeepSeek API error: ${error.error?.message || 'Unknown error'}`);
      }

      const reader = (response.body as any)?.getReader();
      if (!reader) {
        throw new Error('Failed to get stream reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          const finalChunk: StreamingLLMResponse = {
            id: 'final',
            content: '',
            delta: '',
            done: true,
            model: config.model,
          };
          if (onChunk) onChunk(finalChunk);
          yield finalChunk;
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed || !trimmed.startsWith('data: ')) continue;
          
          const data = trimmed.slice(6);
          if (data === '[DONE]') continue;

          try {
            const parsed = JSON.parse(data);
            const delta = parsed.choices?.[0]?.delta?.content;
            
            if (delta) {
              const chunk: StreamingLLMResponse = {
                id: parsed.id || 'chunk',
                content: delta,
                delta: delta,
                done: false,
                model: config.model,
              };
              if (onChunk) onChunk(chunk);
              yield chunk;
            }
          } catch (parseError) {
            console.error('Error parsing SSE data:', parseError);
          }
        }
      }
    } catch (error: any) {
      throw new Error(`DeepSeek streaming error: ${error.message}`);
    }
  }

  countTokens(text: string, model: string): number {
    // DeepSeek uses similar tokenization to OpenAI
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  validateConfig(config: LLMProviderConfig): boolean {
    return super.validateConfig(config) && config.provider === 'deepseek';
  }
}