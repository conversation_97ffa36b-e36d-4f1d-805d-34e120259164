import * as crypto from 'crypto-js';
import type { AppSettings, LLMProviderConfig } from '@shared/types';
import { DEFAULT_SETTINGS } from '@shared/constants';
import { DatabaseService } from '../database/DatabaseService';

export class SettingsService {
  private readonly ENCRYPTION_KEY = 'ai-assistant-encryption-key';
  private cachedSettings: AppSettings | null = null;

  constructor(private databaseService: DatabaseService) {}

  async initialize(): Promise<void> {
    // Load settings from database
    await this.loadSettings();
  }

  async getSettings(): Promise<AppSettings> {
    if (!this.cachedSettings) {
      await this.loadSettings();
    }
    return this.cachedSettings!;
  }

  async updateSettings(updates: Partial<AppSettings>): Promise<void> {
    const currentSettings = await this.getSettings();
    const newSettings = this.deepMerge(currentSettings, updates);

    // Encrypt sensitive data before storing
    const settingsToStore = this.encryptSensitiveData(newSettings);
    
    await this.databaseService.updateSettings(settingsToStore);
    this.cachedSettings = newSettings;
  }

  async getSetting<K extends keyof AppSettings>(key: K): Promise<AppSettings[K]> {
    const settings = await this.getSettings();
    return settings[key];
  }

  async setSetting<K extends keyof AppSettings>(
    key: K, 
    value: AppSettings[K]
  ): Promise<void> {
    await this.updateSettings({ [key]: value } as Partial<AppSettings>);
  }

  async resetSettings(): Promise<void> {
    await this.databaseService.updateSettings(DEFAULT_SETTINGS);
    this.cachedSettings = { ...DEFAULT_SETTINGS };
  }

  // LLM Provider specific methods
  async getLLMConfig(): Promise<LLMProviderConfig> {
    const settings = await this.getSettings();
    return settings.llm;
  }

  async updateLLMConfig(config: Partial<LLMProviderConfig>): Promise<void> {
    const currentLLM = await this.getLLMConfig();
    const updatedLLM = { ...currentLLM, ...config };
    await this.updateSettings({ llm: updatedLLM });
  }

  async setAPIKey(provider: string, apiKey: string): Promise<void> {
    const currentLLM = await this.getLLMConfig();
    if (currentLLM.provider === provider) {
      await this.updateLLMConfig({ apiKey });
    }
  }

  async getAPIKey(provider?: string): Promise<string | undefined> {
    const llmConfig = await this.getLLMConfig();
    if (!provider || llmConfig.provider === provider) {
      return llmConfig.apiKey;
    }
    return undefined;
  }

  // UI Settings
  async getTheme(): Promise<'light' | 'dark' | 'system'> {
    const settings = await this.getSettings();
    return settings.ui.theme;
  }

  async setTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      ui: { ...settings.ui, theme }
    });
  }

  async getFontSize(): Promise<'small' | 'medium' | 'large'> {
    const settings = await this.getSettings();
    return settings.ui.fontSize;
  }

  async setFontSize(fontSize: 'small' | 'medium' | 'large'): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      ui: { ...settings.ui, fontSize }
    });
  }

  async getCompactMode(): Promise<boolean> {
    const settings = await this.getSettings();
    return settings.ui.compactMode;
  }

  async setCompactMode(compactMode: boolean): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      ui: { ...settings.ui, compactMode }
    });
  }

  // Agent Settings
  async getExecutionMode(): Promise<'confirm' | 'yolo'> {
    const settings = await this.getSettings();
    return settings.agent.defaultExecutionMode;
  }

  async setExecutionMode(mode: 'confirm' | 'yolo'): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      agent: { ...settings.agent, defaultExecutionMode: mode }
    });
  }

  async getAutoSaveConversations(): Promise<boolean> {
    const settings = await this.getSettings();
    return settings.agent.autoSaveConversations;
  }

  async setAutoSaveConversations(autoSave: boolean): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      agent: { ...settings.agent, autoSaveConversations: autoSave }
    });
  }

  async getMaxContextLength(): Promise<number> {
    const settings = await this.getSettings();
    return settings.agent.maxContextLength;
  }

  async setMaxContextLength(maxLength: number): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      agent: { ...settings.agent, maxContextLength: maxLength }
    });
  }

  // Tool Settings
  async getEnabledTools(): Promise<string[]> {
    const settings = await this.getSettings();
    return settings.tools.enabledTools;
  }

  async setEnabledTools(tools: string[]): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      tools: { ...settings.tools, enabledTools: tools }
    });
  }

  async enableTool(toolName: string): Promise<void> {
    const currentTools = await this.getEnabledTools();
    if (!currentTools.includes(toolName)) {
      await this.setEnabledTools([...currentTools, toolName]);
    }
  }

  async disableTool(toolName: string): Promise<void> {
    const currentTools = await this.getEnabledTools();
    const filteredTools = currentTools.filter(tool => tool !== toolName);
    await this.setEnabledTools(filteredTools);
  }

  async getToolSettings(): Promise<Record<string, any>> {
    const settings = await this.getSettings();
    return settings.tools.toolSettings;
  }

  async setToolSettings(toolSettings: Record<string, any>): Promise<void> {
    const settings = await this.getSettings();
    await this.updateSettings({
      tools: { ...settings.tools, toolSettings }
    });
  }

  async getToolSetting(toolName: string): Promise<any> {
    const toolSettings = await this.getToolSettings();
    return toolSettings[toolName];
  }

  async setToolSetting(toolName: string, setting: any): Promise<void> {
    const currentToolSettings = await this.getToolSettings();
    await this.setToolSettings({
      ...currentToolSettings,
      [toolName]: setting,
    });
  }

  // Import/Export functionality
  async exportSettings(): Promise<string> {
    const settings = await this.getSettings();
    // Remove sensitive data from export
    const exportData = {
      ...settings,
      llm: {
        ...settings.llm,
        apiKey: '', // Don't export API keys
      },
    };
    return JSON.stringify(exportData, null, 2);
  }

  async importSettings(settingsJson: string): Promise<void> {
    try {
      const importedSettings = JSON.parse(settingsJson) as Partial<AppSettings>;
      
      // Validate imported settings structure
      if (!this.validateSettingsStructure(importedSettings)) {
        throw new Error('Invalid settings format');
      }

      // Preserve current API key if not provided in import
      const currentSettings = await this.getSettings();
      if (!importedSettings.llm?.apiKey && currentSettings.llm.apiKey) {
        importedSettings.llm = {
          ...currentSettings.llm,
          ...(importedSettings.llm || {}),
          apiKey: currentSettings.llm.apiKey,
        };
      }

      await this.updateSettings(importedSettings);
    } catch (error: any) {
      throw new Error(`Failed to import settings: ${error.message}`);
    }
  }

  // Validation methods
  validateLLMConfig(config: LLMProviderConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.provider) {
      errors.push('LLM provider is required');
    } else if (!['openai', 'anthropic', 'deepseek'].includes(config.provider)) {
      errors.push('Invalid LLM provider');
    }

    if (!config.model || config.model.trim() === '') {
      errors.push('LLM model is required');
    }

    if (!config.apiKey || config.apiKey.trim() === '') {
      errors.push('API key is required');
    }

    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 128000)) {
      errors.push('Max tokens must be between 1 and 128000');
    }

    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsFromDb = this.databaseService.getSettings();
      const decryptedSettings = this.decryptSensitiveData(settingsFromDb);
      this.cachedSettings = decryptedSettings;
    } catch (error) {
      console.error('Failed to load settings, using defaults:', error);
      this.cachedSettings = { ...DEFAULT_SETTINGS };
    }
  }

  private validateSettingsStructure(settings: any): boolean {
    try {
      // Basic structure validation
      if (typeof settings !== 'object' || settings === null) {
        return false;
      }

      // Validate top-level keys
      const requiredKeys = ['llm', 'ui', 'agent', 'tools'];
      for (const key of requiredKeys) {
        if (!(key in settings) || typeof settings[key] !== 'object') {
          return false;
        }
      }

      // Validate LLM config
      if (settings.llm) {
        const llmKeys = ['provider', 'model'];
        for (const key of llmKeys) {
          if (key in settings.llm && typeof settings.llm[key] !== 'string') {
            return false;
          }
        }
      }

      // Validate UI config
      if (settings.ui) {
        if ('theme' in settings.ui && !['light', 'dark', 'system'].includes(settings.ui.theme)) {
          return false;
        }
        if ('fontSize' in settings.ui && !['small', 'medium', 'large'].includes(settings.ui.fontSize)) {
          return false;
        }
        if ('compactMode' in settings.ui && typeof settings.ui.compactMode !== 'boolean') {
          return false;
        }
      }

      // Validate agent config
      if (settings.agent) {
        if ('defaultExecutionMode' in settings.agent && 
            !['confirm', 'yolo'].includes(settings.agent.defaultExecutionMode)) {
          return false;
        }
        if ('autoSaveConversations' in settings.agent && 
            typeof settings.agent.autoSaveConversations !== 'boolean') {
          return false;
        }
        if ('maxContextLength' in settings.agent && 
            (typeof settings.agent.maxContextLength !== 'number' || settings.agent.maxContextLength < 1)) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private encryptSensitiveData(settings: AppSettings): AppSettings {
    const encrypted = { ...settings };
    
    // Encrypt API key
    if (encrypted.llm.apiKey) {
      encrypted.llm.apiKey = this.encrypt(encrypted.llm.apiKey);
    }

    return encrypted;
  }

  private decryptSensitiveData(settings: AppSettings): AppSettings {
    const decrypted = { ...settings };
    
    // Decrypt API key
    if (decrypted.llm.apiKey) {
      try {
        decrypted.llm.apiKey = this.decrypt(decrypted.llm.apiKey);
      } catch (error) {
        console.warn('Failed to decrypt API key, using empty string');
        decrypted.llm.apiKey = '';
      }
    }

    return decrypted;
  }

  private encrypt(text: string): string {
    return crypto.AES.encrypt(text, this.ENCRYPTION_KEY).toString();
  }

  private decrypt(encryptedText: string): string {
    const bytes = crypto.AES.decrypt(encryptedText, this.ENCRYPTION_KEY);
    return bytes.toString(crypto.enc.Utf8);
  }

  private deepMerge<T>(target: T, source: Partial<T>): T {
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = (result as any)[key];
        
        if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue) &&
            targetValue && typeof targetValue === 'object' && !Array.isArray(targetValue)) {
          // Recursively merge objects
          (result as any)[key] = this.deepMerge(targetValue, sourceValue);
        } else {
          // Direct assignment for primitives and arrays
          (result as any)[key] = sourceValue;
        }
      }
    }
    
    return result;
  }

  // Event emitters for settings changes (optional)
  private listeners: Map<string, Set<(value: any) => void>> = new Map();

  onSettingChanged<K extends keyof AppSettings>(
    key: K,
    callback: (value: AppSettings[K]) => void
  ): () => void {
    const keyStr = key as string;
    if (!this.listeners.has(keyStr)) {
      this.listeners.set(keyStr, new Set());
    }
    
    this.listeners.get(keyStr)!.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(keyStr)?.delete(callback);
    };
  }

  private emitSettingChanged<K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ): void {
    const keyStr = key as string;
    const callbacks = this.listeners.get(keyStr);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(value);
        } catch (error) {
          console.error('Error in settings change callback:', error);
        }
      });
    }
  }
}