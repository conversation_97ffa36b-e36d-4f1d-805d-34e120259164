export declare const useDebounce: <T>(value: T, delay: number) => T;
export declare const useLocalStorage: <T>(key: string, initialValue: T) => [T, (value: T | ((val: T) => T)) => void];
export declare const useClipboard: () => {
    copied: boolean;
    copyToClipboard: (text: string) => Promise<boolean>;
    readFromClipboard: () => Promise<string | null>;
};
export declare const useOnlineStatus: () => boolean;
export declare const useWindowSize: () => {
    width: number;
    height: number;
};
export declare const usePrevious: <T>(value: T) => T | undefined;
export declare const useInterval: (callback: () => void, delay: number | null) => void;
export declare const useTimeout: (callback: () => void, delay: number | null) => void;
export declare const useAsync: <T, E = string>(asyncFunction: () => Promise<T>, immediate?: boolean) => {
    execute: () => Promise<void>;
    status: "error" | "success" | "idle" | "pending";
    value: T | null;
    error: E | null;
};
export declare const useTheme: () => {
    theme: "light" | "dark";
    systemTheme: "light" | "dark";
    isDark: boolean;
};
export declare const useFocusTrap: (isActive: boolean) => import("react").RefObject<HTMLElement>;
export declare const useOutsideClick: (callback: () => void) => import("react").RefObject<HTMLElement>;
export declare const useScrollPosition: () => {
    x: number;
    y: number;
};
//# sourceMappingURL=useUtils.d.ts.map