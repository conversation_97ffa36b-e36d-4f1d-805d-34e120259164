import React from 'react';
interface ConfirmationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    onCancel?: () => void;
    title: string;
    message: string;
    type?: 'info' | 'warning' | 'error' | 'success';
    confirmText?: string;
    cancelText?: string;
    details?: string;
    showDetails?: boolean;
    dangerous?: boolean;
    loading?: boolean;
}
declare const ConfirmationModal: React.FC<ConfirmationModalProps>;
export default ConfirmationModal;
//# sourceMappingURL=ConfirmationModal.d.ts.map