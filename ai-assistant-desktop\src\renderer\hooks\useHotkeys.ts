import React, { useEffect, useCallback } from 'react';
import { useAppStore } from '@renderer/store/appStore';
import { useConversations, useWindowControls } from './useElectronAPI';

interface HotkeyConfig {
  key: string;
  ctrlKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: () => void;
  description: string;
  global?: boolean;
}

export const useHotkeys = () => {
  const {
    currentConversation,
    settingsOpen,
    sidebarOpen,
    setSettingsOpen,
    setSidebarOpen,
    setCurrentView,
  } = useAppStore();
  
  const { createConversation } = useConversations();
  const { minimize, maximize, close } = useWindowControls();

  const hotkeys: HotkeyConfig[] = [
    // Navigation
    {
      key: 'n',
      ctrlKey: true,
      action: () => {
        createConversation('New Conversation');
      },
      description: 'New conversation',
    },
    {
      key: ',',
      ctrlKey: true,
      action: () => {
        setSettingsOpen(!settingsOpen);
      },
      description: 'Toggle settings',
    },
    {
      key: 'b',
      ctrlKey: true,
      action: () => {
        setSidebarOpen(!sidebarOpen);
      },
      description: 'Toggle sidebar',
    },
    {
      key: '1',
      ctrlKey: true,
      action: () => {
        setCurrentView('chat');
      },
      description: 'Switch to chat view',
    },
    {
      key: '2',
      ctrlKey: true,
      action: () => {
        setCurrentView('logs');
      },
      description: 'Switch to logs view',
    },

    // Window controls
    {
      key: 'w',
      ctrlKey: true,
      action: () => {
        close();
      },
      description: 'Close window',
    },
    {
      key: 'm',
      ctrlKey: true,
      action: () => {
        minimize();
      },
      description: 'Minimize window',
    },
    {
      key: 'F11',
      action: () => {
        maximize();
      },
      description: 'Toggle fullscreen',
    },

    // Quick actions
    {
      key: 'Escape',
      action: () => {
        if (settingsOpen) {
          setSettingsOpen(false);
        }
      },
      description: 'Close modals/settings',
    },
    {
      key: 'Enter',
      ctrlKey: true,
      action: () => {
        // Submit current message if in chat
        const textarea = document.querySelector('textarea[placeholder*="message"]') as HTMLTextAreaElement;
        if (textarea && textarea.value.trim()) {
          const form = textarea.closest('form');
          if (form) {
            form.requestSubmit();
          }
        }
      },
      description: 'Send message',
    },
    {
      key: 'k',
      ctrlKey: true,
      action: () => {
        // Focus search or command palette
        const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      },
      description: 'Focus search',
    },

    // Developer shortcuts
    {
      key: 'F12',
      action: () => {
        // Toggle developer tools - not implemented yet
        console.log('Developer tools toggle not implemented');
      },
      description: 'Toggle developer tools',
    },
    {
      key: 'r',
      ctrlKey: true,
      action: () => {
        // Reload the app
        window.location.reload();
      },
      description: 'Reload application',
    },
  ];

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const matchingHotkey = hotkeys.find(hotkey => {
      return (
        hotkey.key.toLowerCase() === event.key.toLowerCase() &&
        !!hotkey.ctrlKey === event.ctrlKey &&
        !!hotkey.metaKey === event.metaKey &&
        !!hotkey.shiftKey === event.shiftKey &&
        !!hotkey.altKey === event.altKey
      );
    });

    if (matchingHotkey) {
      event.preventDefault();
      event.stopPropagation();
      matchingHotkey.action();
    }
  }, [hotkeys, settingsOpen, sidebarOpen, createConversation, setSettingsOpen, setSidebarOpen, setCurrentView, close, minimize, maximize]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return { hotkeys };
};

// Hook for keyboard shortcuts help
export const useKeyboardShortcutsHelp = () => {
  const { hotkeys } = useHotkeys();

  const formatShortcut = (hotkey: HotkeyConfig) => {
    const parts: string[] = [];
    
    if (hotkey.ctrlKey) parts.push('Ctrl');
    if (hotkey.metaKey) parts.push('Cmd');
    if (hotkey.shiftKey) parts.push('Shift');
    if (hotkey.altKey) parts.push('Alt');
    
    parts.push(hotkey.key);
    
    return parts.join(' + ');
  };

  const groupedHotkeys = {
    Navigation: hotkeys.filter(h => ['n', ',', 'b', '1', '2'].includes(h.key)),
    'Window Controls': hotkeys.filter(h => ['w', 'm', 'F11'].includes(h.key)),
    'Quick Actions': hotkeys.filter(h => ['Escape', 'Enter', 'k'].includes(h.key)),
    Developer: hotkeys.filter(h => ['F12', 'r'].includes(h.key)),
  };

  return {
    hotkeys,
    groupedHotkeys,
    formatShortcut,
  };
};

// Component for displaying keyboard shortcuts
export const KeyboardShortcutsHelp: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { groupedHotkeys, formatShortcut } = useKeyboardShortcutsHelp();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg shadow-2xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-100">Keyboard Shortcuts</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-200 transition-colors"
            >
              ×
            </button>
          </div>
        </div>
        
        <div className="p-6 space-y-6">
          {Object.entries(groupedHotkeys).map(([category, shortcuts]) => (
            <div key={category}>
              <h3 className="text-lg font-medium text-gray-200 mb-3">{category}</h3>
              <div className="space-y-2">
                {shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <span className="text-gray-300">{shortcut.description}</span>
                    <kbd className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-sm text-gray-200 font-mono">
                      {formatShortcut(shortcut)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
