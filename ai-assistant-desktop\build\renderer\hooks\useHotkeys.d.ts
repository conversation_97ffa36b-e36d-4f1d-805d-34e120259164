import React from 'react';
interface HotkeyConfig {
    key: string;
    ctrlKey?: boolean;
    metaKey?: boolean;
    shiftKey?: boolean;
    altKey?: boolean;
    action: () => void;
    description: string;
    global?: boolean;
}
export declare const useHotkeys: () => {
    hotkeys: HotkeyConfig[];
};
export declare const useKeyboardShortcutsHelp: () => {
    hotkeys: HotkeyConfig[];
    groupedHotkeys: {
        Navigation: HotkeyConfig[];
        'Window Controls': HotkeyConfig[];
        'Quick Actions': HotkeyConfig[];
        Developer: HotkeyConfig[];
    };
    formatShortcut: (hotkey: HotkeyConfig) => string;
};
export declare const KeyboardShortcutsHelp: React.FC<{
    onClose: () => void;
}>;
export {};
//# sourceMappingURL=useHotkeys.d.ts.map