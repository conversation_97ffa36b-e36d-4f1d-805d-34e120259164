import { useCallback, useEffect, useState } from 'react';
import type { APIResponse, AppSettings, Conversation, SystemInfo, ToolResult } from '@shared/types';
import { useAppStore } from '@renderer/store/appStore';

// Hook for accessing the Electron API
export const useElectronAPI = () => {
  const api = window.electronAPI;
  
  if (!api) {
    throw new Error('Electron API not available. Make sure the app is running in Electron.');
  }
  
  return api;
};

// Hook for settings management
export const useSettings = () => {
  const api = useElectronAPI();
  const { setSettings, setError, setLoading } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  const loadSettings = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await api.settings.get();
      if (response.success) {
        setSettings(response.data as AppSettings);
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load settings');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [api.settings, setSettings, setError]);

  const updateSettings = useCallback(async (updates: Partial<AppSettings>) => {
    setIsLoading(true);
    try {
      const response = await api.settings.set(updates);
      if (response.success) {
        await loadSettings(); // Reload settings after update
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to update settings');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [api.settings, loadSettings, setError]);

  const resetSettings = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await api.settings.reset();
      if (response.success) {
        await loadSettings(); // Reload settings after reset
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to reset settings');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [api.settings, loadSettings, setError]);

  return {
    loadSettings,
    updateSettings,
    resetSettings,
    isLoading,
  };
};

// Hook for conversation management
export const useConversations = () => {
  const api = useElectronAPI();
  const { setConversations, setError } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  const loadConversations = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await api.conversations.list();
      if (response.success) {
        setConversations(response.data as Conversation[]);
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load conversations');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [api.conversations, setConversations, setError]);

  const createConversation = useCallback(async (title: string) => {
    try {
      const response = await api.conversations.create({ title });
      if (response.success) {
        await loadConversations(); // Reload conversations
        return response.data as Conversation;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to create conversation');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    }
  }, [api.conversations, loadConversations, setError]);

  const deleteConversation = useCallback(async (id: string) => {
    try {
      const response = await api.conversations.delete({ id });
      if (response.success) {
        await loadConversations(); // Reload conversations
        return true;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to delete conversation');
        return false;
      }
    } catch (error: any) {
      setError(error.message);
      return false;
    }
  }, [api.conversations, loadConversations, setError]);

  return {
    loadConversations,
    createConversation,
    deleteConversation,
    isLoading,
  };
};

// Hook for system information
export const useSystemInfo = () => {
  const api = useElectronAPI();
  const { setSystemInfo, setError } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  const loadSystemInfo = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await api.system.info();
      if (response.success) {
        setSystemInfo(response.data as SystemInfo);
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load system information');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [api.system, setSystemInfo, setError]);

  return {
    loadSystemInfo,
    isLoading,
  };
};

// Hook for LLM communication
export const useLLM = () => {
  const api = useElectronAPI();
  const { 
    setStreaming, 
    setStreamingMessage, 
    appendStreamingMessage, 
    clearStreamingMessage,
    setError 
  } = useAppStore();

  const sendMessage = useCallback(async (
    messages: any[], 
    systemPrompt?: string
  ): Promise<string | null> => {
    try {
      const response = await api.llm.chat({ messages, systemPrompt });
      if (response.success) {
        return response.data.content;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to send message');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    }
  }, [api.llm, setError]);

  const streamMessage = useCallback(async (
    messages: any[], 
    systemPrompt?: string,
    onChunk?: (chunk: string) => void
  ): Promise<void> => {
    setStreaming(true);
    clearStreamingMessage();
    
    try {
      const response = await api.llm.stream({ messages, systemPrompt });
      if (response.success) {
        // Handle streaming response
        // Note: In a real implementation, this would need to handle the streaming protocol
        console.log('Streaming response:', response.data);
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to stream message');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setStreaming(false);
    }
  }, [api.llm, setStreaming, clearStreamingMessage, setError]);

  return {
    sendMessage,
    streamMessage,
  };
};

// Hook for agent operations
export const useAgent = () => {
  const api = useElectronAPI();
  const { 
    setCurrentPlan, 
    setProcessing, 
    addToolResult, 
    setError 
  } = useAppStore();

  const createPlan = useCallback(async (conversationId: string, executionMode?: 'confirm' | 'yolo') => {
    setProcessing(true);
    try {
      const response = await api.agent.plan({ conversationId, executionMode });
      if (response.success) {
        setCurrentPlan(response.data.plan);
        return response.data.plan;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to create plan');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    } finally {
      setProcessing(false);
    }
  }, [api.agent, setCurrentPlan, setProcessing, setError]);

  const executePlan = useCallback(async (conversationId: string, stepId?: string) => {
    setProcessing(true);
    try {
      const response = await api.agent.execute({ conversationId, stepId });
      if (response.success) {
        const results = response.data.results as ToolResult[];
        if (results) {
          results.forEach(result => addToolResult(result));
        }
        return response.data;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to execute plan');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    } finally {
      setProcessing(false);
    }
  }, [api.agent, addToolResult, setProcessing, setError]);

  const confirmExecution = useCallback(async (conversationId: string, approved: boolean) => {
    try {
      const response = await api.agent.confirm({ conversationId, approved });
      if (response.success) {
        return true;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to confirm execution');
        return false;
      }
    } catch (error: any) {
      setError(error.message);
      return false;
    }
  }, [api.agent, setError]);

  return {
    createPlan,
    executePlan,
    confirmExecution,
  };
};

// Hook for tools
export const useTools = () => {
  const api = useElectronAPI();
  const { setError } = useAppStore();
  const [availableTools, setAvailableTools] = useState<any[]>([]);

  const loadTools = useCallback(async () => {
    try {
      const response = await api.tools.list();
      if (response.success) {
        setAvailableTools(response.data);
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load tools');
      }
    } catch (error: any) {
      setError(error.message);
    }
  }, [api.tools, setError]);

  const executeTool = useCallback(async (name: string, args: Record<string, any>) => {
    try {
      const response = await api.tools.execute({ name, arguments: args });
      if (response.success) {
        return response.data as ToolResult;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Tool execution failed');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    }
  }, [api.tools, setError]);

  useEffect(() => {
    loadTools();
  }, [loadTools]);

  return {
    availableTools,
    loadTools,
    executeTool,
  };
};

// Hook for file system operations
export const useFileSystem = () => {
  const api = useElectronAPI();
  const { setError } = useAppStore();

  const readFile = useCallback(async (path: string) => {
    try {
      const response = await api.fs.read({ path });
      if (response.success) {
        return response.data;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to read file');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    }
  }, [api.fs, setError]);

  const writeFile = useCallback(async (path: string, content: string) => {
    try {
      const response = await api.fs.write({ path, content });
      if (response.success) {
        return true;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to write file');
        return false;
      }
    } catch (error: any) {
      setError(error.message);
      return false;
    }
  }, [api.fs, setError]);

  const listDirectory = useCallback(async (path: string) => {
    try {
      const response = await api.fs.list({ path });
      if (response.success) {
        return response.data;
      } else {
        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to list directory');
        return null;
      }
    } catch (error: any) {
      setError(error.message);
      return null;
    }
  }, [api.fs, setError]);

  return {
    readFile,
    writeFile,
    listDirectory,
  };
};

// Hook for window controls
export const useWindowControls = () => {
  const api = useElectronAPI();

  const minimize = useCallback(() => {
    api.window.minimize().catch(console.error);
  }, [api.window]);

  const maximize = useCallback(() => {
    api.window.maximize().catch(console.error);
  }, [api.window]);

  const close = useCallback(() => {
    api.window.close().catch(console.error);
  }, [api.window]);

  return {
    minimize,
    maximize,
    close,
  };
};

// Hook for YOLO mode detection
export const useIsYoloMode = () => {
  const { settings } = useAppStore();
  return settings?.agent?.defaultExecutionMode === 'yolo';
};

// Hook for keyboard shortcuts
export const useKeyboardShortcuts = () => {
  const { currentConversation, settingsOpen, sidebarOpen } = useAppStore();
  const { createConversation } = useConversations();
  const { minimize, maximize, close } = useWindowControls();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + N: New conversation
      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        createConversation('New Conversation');
      }

      // Ctrl/Cmd + ,: Open settings
      if ((event.ctrlKey || event.metaKey) && event.key === ',') {
        event.preventDefault();
        useAppStore.getState().setSettingsOpen(!settingsOpen);
      }

      // Ctrl/Cmd + B: Toggle sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        useAppStore.getState().setSidebarOpen(!sidebarOpen);
      }

      // Ctrl/Cmd + W: Close window
      if ((event.ctrlKey || event.metaKey) && event.key === 'w') {
        event.preventDefault();
        close();
      }

      // Ctrl/Cmd + M: Minimize window
      if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
        event.preventDefault();
        minimize();
      }

      // F11: Toggle maximize
      if (event.key === 'F11') {
        event.preventDefault();
        maximize();
      }

      // Escape: Close modals/settings
      if (event.key === 'Escape') {
        if (settingsOpen) {
          useAppStore.getState().setSettingsOpen(false);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [createConversation, settingsOpen, sidebarOpen, close, minimize, maximize]);
};

// Hook for streaming message handling
export const useStreamingMessage = () => {
  const { isStreaming, streamingMessage } = useAppStore();
  const [currentMessage, setCurrentMessage] = useState('');

  useEffect(() => {
    if (isStreaming && streamingMessage) {
      setCurrentMessage(streamingMessage);
    } else if (!isStreaming) {
      setCurrentMessage('');
    }
  }, [isStreaming, streamingMessage]);

  return {
    isStreaming,
    currentMessage,
  };
};

// Hook for diff viewing
export const useDiffViewer = () => {
  const [diffData, setDiffData] = useState<{
    oldContent: string;
    newContent: string;
    fileName: string;
  } | null>(null);

  const showDiff = useCallback((oldContent: string, newContent: string, fileName: string) => {
    setDiffData({ oldContent, newContent, fileName });
  }, []);

  const hideDiff = useCallback(() => {
    setDiffData(null);
  }, []);

  return {
    diffData,
    showDiff,
    hideDiff,
  };
};

// Hook for toast notifications
export const useToast = () => {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    duration?: number;
  }>>([]);

  const showToast = useCallback((
    type: 'success' | 'error' | 'warning' | 'info',
    message: string,
    duration = 5000
  ) => {
    const id = Math.random().toString(36).substr(2, 9);
    const toast = { id, type, message, duration };

    setToasts(prev => [...prev, toast]);

    if (duration > 0) {
      setTimeout(() => {
        setToasts(prev => prev.filter(t => t.id !== id));
      }, duration);
    }
  }, []);

  const hideToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  const clearToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return {
    toasts,
    showToast,
    hideToast,
    clearToasts,
  };
};