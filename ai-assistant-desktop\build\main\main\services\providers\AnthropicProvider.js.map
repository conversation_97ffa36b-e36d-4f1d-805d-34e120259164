{"version": 3, "file": "AnthropicProvider.js", "sourceRoot": "", "sources": ["../../../../../src/main/services/providers/AnthropicProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAA+B;AAC/B,kEAA0D;AAO1D,iDAAmD;AAEnD,MAAa,iBAAkB,SAAQ,sCAAe;IAAtD;;QACE,SAAI,GAAG,WAAW,CAAC;QACnB,oBAAe,GAAG,0BAAc,CAAC,SAAgC,CAAC;IAoLpE,CAAC;IAlLW,YAAY,CAAC,MAAyB;QAC9C,OAAO;YACL,cAAc,EAAE,kBAAkB;YAClC,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,mBAAmB,EAAE,YAAY;SAClC,CAAC;IACJ,CAAC;IAES,cAAc,CAAC,QAAmB,EAAE,YAAqB;QACjE,MAAM,iBAAiB,GAAG,QAAQ;aAC/B,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC;aACpC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACX,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;YACrD,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,QAAQ,EAAE,iBAAiB;YAC3B,MAAM,EAAE,YAAY;SACrB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAmB,EACnB,MAAyB,EACzB,YAAqB;QAErB,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,IAAI,uCAAuC,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE5F,MAAM,IAAI,GAAQ;YAChB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,QAAQ,EAAE,iBAAiB;YAC3B,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;YACpC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;SACvC,CAAC;QAEF,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,MAAM;gBACd,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC,CAAC;YACrF,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAE1C,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE;gBACpC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI;oBACnB,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;oBACrC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;oBAC1C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;iBAChE;gBACD,YAAY,EAAE,IAAI,CAAC,WAAW;aAC/B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,KAAK,CAAA,CAAE,UAAU,CACf,QAAmB,EACnB,MAAyB,EACzB,YAAqB,EACrB,OAA+C;QAE/C,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,IAAI,uCAAuC,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE5F,MAAM,IAAI,GAAQ;YAChB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,QAAQ,EAAE,iBAAiB;YAC3B,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;YACpC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;YACtC,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,MAAM;gBACd,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC,CAAC;YACrF,CAAC;YAED,MAAM,MAAM,GAAI,QAAQ,CAAC,IAAY,EAAE,SAAS,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAE5C,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,UAAU,GAAyB;wBACvC,EAAE,EAAE,OAAO;wBACX,OAAO,EAAE,EAAE;wBACX,KAAK,EAAE,EAAE;wBACT,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,MAAM,CAAC,KAAK;qBACpB,CAAC;oBACF,IAAI,OAAO;wBAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACjC,MAAM,UAAU,CAAC;oBACjB,MAAM;gBACR,CAAC;gBAED,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC5B,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;wBAAE,SAAS;oBAExD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC9B,IAAI,IAAI,KAAK,QAAQ;wBAAE,SAAS;oBAEhC,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAEhC,IAAI,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;4BAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC;4BAEjC,IAAI,KAAK,EAAE,CAAC;gCACV,MAAM,KAAK,GAAyB;oCAClC,EAAE,EAAE,MAAM,CAAC,EAAE,IAAI,OAAO;oCACxB,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,KAAK;oCACZ,IAAI,EAAE,KAAK;oCACX,KAAK,EAAE,MAAM,CAAC,KAAK;iCACpB,CAAC;gCACF,IAAI,OAAO;oCAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gCAC5B,MAAM,KAAK,CAAC;4BACd,CAAC;wBACH,CAAC;oBACH,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;oBACvD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,KAAa;QACrC,mDAAmD;QACnD,sEAAsE;QACtE,6CAA6C;QAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,cAAc,CAAC,MAAyB;QACtC,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC;IACzE,CAAC;CACF;AAtLD,8CAsLC"}