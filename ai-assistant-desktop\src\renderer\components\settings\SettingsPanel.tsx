import React, { useState, useEffect } from 'react';
import { useAppStore } from '@renderer/store/appStore';
import { Settings, X, Save, RotateCcw, Eye, EyeOff, AlertTriangle, CheckCircle } from 'lucide-react';
import { useSettings } from '@renderer/hooks/useElectronAPI';
import { DEFAULT_MODELS } from '@shared/constants';
import type { AppSettings } from '@shared/types';

const SettingsPanel: React.FC = () => {
  const { settings, setSettingsOpen } = useAppStore();
  const { updateSettings, resetSettings, isLoading } = useSettings();
  const [localSettings, setLocalSettings] = useState<AppSettings | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  useEffect(() => {
    if (settings) {
      setLocalSettings({ ...settings });
    }
  }, [settings]);

  useEffect(() => {
    if (localSettings && settings) {
      const changed = JSON.stringify(localSettings) !== JSON.stringify(settings);
      setHasChanges(changed);
    }
  }, [localSettings, settings]);

  const handleSave = async () => {
    if (!localSettings || !hasChanges) return;

    setSaveStatus('saving');
    try {
      await updateSettings(localSettings);
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const handleReset = async () => {
    if (window.confirm('Are you sure you want to reset all settings to defaults?')) {
      setSaveStatus('saving');
      try {
        await resetSettings();
        setSaveStatus('saved');
        setTimeout(() => setSaveStatus('idle'), 2000);
      } catch (error) {
        setSaveStatus('error');
        setTimeout(() => setSaveStatus('idle'), 3000);
      }
    }
  };

  const updateLocalSetting = (path: string, value: any) => {
    if (!localSettings) return;

    const keys = path.split('.');
    const newSettings = { ...localSettings };
    let current: any = newSettings;

    for (let i = 0; i < keys.length - 1; i++) {
      current[keys[i]] = { ...current[keys[i]] };
      current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
    setLocalSettings(newSettings);
  };

  if (!localSettings) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-900">
      {/* Header */}
      <div className="h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-6">
        <div className="flex items-center space-x-3">
          <Settings className="w-5 h-5 text-gray-400" />
          <h1 className="text-lg font-semibold text-gray-100">Settings</h1>
        </div>
        <div className="flex items-center space-x-3">
          {saveStatus === 'saved' && (
            <div className="flex items-center space-x-2 text-green-400">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Saved</span>
            </div>
          )}
          {saveStatus === 'error' && (
            <div className="flex items-center space-x-2 text-red-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">Error saving</span>
            </div>
          )}
          {hasChanges && (
            <>
              <button
                onClick={handleReset}
                disabled={isLoading || saveStatus === 'saving'}
                className="px-3 py-1 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50"
              >
                <RotateCcw className="w-4 h-4 mr-1 inline" />
                Reset
              </button>
              <button
                onClick={handleSave}
                disabled={isLoading || saveStatus === 'saving'}
                className="px-3 py-1 text-sm bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {saveStatus === 'saving' ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Saving...</span>
                  </div>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-1 inline" />
                    Save
                  </>
                )}
              </button>
            </>
          )}
          <button
            onClick={() => setSettingsOpen(false)}
            className="p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* LLM Settings */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-100 mb-4">Language Model</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Provider</label>
                <select
                  value={localSettings.llm.provider}
                  onChange={(e) => updateLocalSetting('llm.provider', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="deepseek">DeepSeek</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Model</label>
                <select
                  value={localSettings.llm.model}
                  onChange={(e) => updateLocalSetting('llm.model', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                >
                  {Object.entries(DEFAULT_MODELS[localSettings.llm.provider as keyof typeof DEFAULT_MODELS] || {}).map(([model, label]) => (
                    <option key={model} value={model}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">API Key</label>
                <div className="relative">
                  <input
                    type={showApiKey ? 'text' : 'password'}
                    value={localSettings.llm.apiKey}
                    onChange={(e) => updateLocalSetting('llm.apiKey', e.target.value)}
                    className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                    placeholder="Enter your API key"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200"
                  >
                    {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Tokens</label>
                  <input
                    type="number"
                    value={localSettings.llm.maxTokens}
                    onChange={(e) => updateLocalSetting('llm.maxTokens', parseInt(e.target.value))}
                    min="1"
                    max="32000"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Temperature</label>
                  <input
                    type="number"
                    value={localSettings.llm.temperature}
                    onChange={(e) => updateLocalSetting('llm.temperature', parseFloat(e.target.value))}
                    min="0"
                    max="2"
                    step="0.1"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* UI Settings */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-100 mb-4">Interface</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Theme</label>
                <select
                  value={localSettings.ui.theme}
                  onChange={(e) => updateLocalSetting('ui.theme', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                >
                  <option value="system">System</option>
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Font Size</label>
                <select
                  value={localSettings.ui.fontSize}
                  onChange={(e) => updateLocalSetting('ui.fontSize', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="compactMode"
                  checked={localSettings.ui.compactMode}
                  onChange={(e) => updateLocalSetting('ui.compactMode', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500"
                />
                <label htmlFor="compactMode" className="ml-2 text-sm text-gray-300">
                  Compact mode
                </label>
              </div>
            </div>
          </div>

          {/* Agent Settings */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-100 mb-4">Agent Behavior</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Execution Mode</label>
                <select
                  value={localSettings.agent.defaultExecutionMode}
                  onChange={(e) => updateLocalSetting('agent.defaultExecutionMode', e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                >
                  <option value="confirm">Ask for confirmation</option>
                  <option value="yolo">Execute automatically (YOLO mode)</option>
                </select>
                {localSettings.agent.defaultExecutionMode === 'yolo' && (
                  <div className="mt-2 p-3 bg-red-900 bg-opacity-30 border border-red-500 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-4 h-4 text-red-400 flex-shrink-0" />
                      <p className="text-sm text-red-300">
                        YOLO mode allows the agent to execute commands without confirmation. Use with caution.
                      </p>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoSave"
                  checked={localSettings.agent.autoSaveConversations}
                  onChange={(e) => updateLocalSetting('agent.autoSaveConversations', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500"
                />
                <label htmlFor="autoSave" className="ml-2 text-sm text-gray-300">
                  Auto-save conversations
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Max Context Length</label>
                <input
                  type="number"
                  value={localSettings.agent.maxContextLength}
                  onChange={(e) => updateLocalSetting('agent.maxContextLength', parseInt(e.target.value))}
                  min="1000"
                  max="128000"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum number of tokens to keep in conversation context
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;