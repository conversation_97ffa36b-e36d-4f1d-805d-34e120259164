export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
interface LogEntry {
    timestamp: string;
    level: LogLevel;
    message: string;
    data?: any;
    stack?: string;
}
declare class Logger {
    private logDir;
    private logFile;
    private maxLogSize;
    private maxLogFiles;
    constructor();
    private ensureLogDirectory;
    private formatLogEntry;
    private writeToFile;
    private rotateLogFiles;
    debug(message: string, data?: any): void;
    info(message: string, data?: any): void;
    warn(message: string, data?: any): void;
    error(message: string, error?: Error | any): void;
    getRecentLogs(count?: number): LogEntry[];
    clearLogs(): void;
    getLogFiles(): string[];
    getLogDirectorySize(): number;
}
export declare const logger: Logger;
export declare class AppError extends Error {
    readonly code: string;
    readonly statusCode: number;
    readonly isOperational: boolean;
    constructor(message: string, code?: string, statusCode?: number, isOperational?: boolean);
}
export declare class ValidationError extends AppError {
    constructor(message: string, field?: string);
}
export declare class NotFoundError extends AppError {
    constructor(resource: string);
}
export declare class UnauthorizedError extends AppError {
    constructor(message?: string);
}
export declare class RateLimitError extends AppError {
    constructor(message?: string);
}
export declare class NetworkError extends AppError {
    constructor(message?: string);
}
export declare const handleError: (error: Error | AppError, context?: string) => void;
export declare const asyncErrorHandler: <T extends any[], R>(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>;
export declare class PerformanceMonitor {
    private static timers;
    static start(label: string): void;
    static end(label: string): number;
    static measure<T>(label: string, fn: () => T): T;
    static measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T>;
}
export {};
//# sourceMappingURL=logger.d.ts.map