import type { AppSettings } from './types';
export declare const APP_NAME = "AI Assistant";
export declare const APP_VERSION = "1.0.0";
export declare const APP_DESCRIPTION = "Desktop AI Assistant with Multi-LLM Support";
export declare const DB_CONSTANTS: {
    DATABASE_NAME: string;
    BACKUP_INTERVAL: number;
    CLEANUP_INTERVAL: number;
    MAX_CONVERSATION_AGE: number;
    MAX_CACHE_SIZE: number;
    VACUUM_INTERVAL: number;
};
export declare const LLM_PROVIDERS: {
    readonly OPENAI: "openai";
    readonly ANTHROPIC: "anthropic";
    readonly DEEPSEEK: "deepseek";
};
export declare const DEFAULT_MODELS: {
    readonly openai: {
        readonly 'gpt-4': "GPT-4";
        readonly 'gpt-4-turbo': "GPT-4 Turbo";
        readonly 'gpt-3.5-turbo': "GPT-3.5 Turbo";
    };
    readonly anthropic: {
        readonly 'claude-3-opus-20240229': "Claude 3 Opus";
        readonly 'claude-3-sonnet-20240229': "Claude 3 Sonnet";
        readonly 'claude-3-haiku-20240307': "Claude 3 Haiku";
    };
    readonly deepseek: {
        readonly 'deepseek-chat': "DeepSeek Chat";
        readonly 'deepseek-coder': "DeepSeek Coder";
    };
};
export declare const API_ENDPOINTS: {
    readonly openai: "https://api.openai.com/v1";
    readonly anthropic: "https://api.anthropic.com/v1";
    readonly deepseek: "https://api.deepseek.com/v1";
};
export declare const TOKEN_LIMITS: {
    readonly 'gpt-4': 8192;
    readonly 'gpt-4-turbo': 128000;
    readonly 'gpt-3.5-turbo': 4096;
    readonly 'claude-3-opus-20240229': 200000;
    readonly 'claude-3-sonnet-20240229': 200000;
    readonly 'claude-3-haiku-20240307': 200000;
    readonly 'deepseek-chat': 32768;
    readonly 'deepseek-coder': 32768;
};
export declare const UI_CONSTANTS: {
    readonly SIDEBAR_WIDTH: 280;
    readonly SIDEBAR_COLLAPSED_WIDTH: 60;
    readonly HEADER_HEIGHT: 60;
    readonly MESSAGE_MAX_WIDTH: 800;
    readonly ANIMATION_DURATION: 200;
    readonly DEBOUNCE_DELAY: 300;
    readonly TOAST_DURATION: 5000;
    readonly TYPING_INDICATOR_DELAY: 1000;
};
export declare const KEYBOARD_SHORTCUTS: {
    readonly NEW_CONVERSATION: "Ctrl+N";
    readonly TOGGLE_SIDEBAR: "Ctrl+B";
    readonly TOGGLE_SETTINGS: "Ctrl+,";
    readonly FOCUS_INPUT: "Ctrl+Enter";
    readonly CLOSE_MODAL: "Escape";
    readonly TOGGLE_FULLSCREEN: "F11";
    readonly TOGGLE_DEVTOOLS: "F12";
    readonly RELOAD_APP: "Ctrl+Shift+R";
};
export declare const EXECUTION_MODES: {
    readonly CONFIRM: "confirm";
    readonly YOLO: "yolo";
};
export declare const TOOL_CATEGORIES: {
    readonly FILE_SYSTEM: "file_system";
    readonly SHELL: "shell";
    readonly SEARCH: "search";
    readonly NETWORK: "network";
    readonly SYSTEM: "system";
};
export declare const FILE_TYPES: {
    readonly TEXT: readonly ["txt", "md", "json", "xml", "yaml", "yml", "toml", "ini", "cfg", "conf"];
    readonly CODE: readonly ["js", "ts", "jsx", "tsx", "py", "java", "cpp", "c", "h", "php", "rb", "go", "rs", "swift"];
    readonly WEB: readonly ["html", "css", "scss", "sass", "less"];
    readonly DATA: readonly ["csv", "tsv", "json", "xml", "sql"];
    readonly IMAGE: readonly ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"];
    readonly DOCUMENT: readonly ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];
    readonly ARCHIVE: readonly ["zip", "rar", "7z", "tar", "gz", "bz2"];
};
export declare const DEFAULT_SETTINGS: AppSettings;
export declare const ERROR_MESSAGES: {
    readonly NETWORK_ERROR: "Network connection failed. Please check your internet connection.";
    readonly API_KEY_MISSING: "API key is required for the selected provider.";
    readonly API_KEY_INVALID: "Invalid API key. Please check your credentials.";
    readonly RATE_LIMIT_EXCEEDED: "Rate limit exceeded. Please try again later.";
    readonly MODEL_NOT_AVAILABLE: "Selected model is not available.";
    readonly TOOL_EXECUTION_FAILED: "Tool execution failed. Please try again.";
    readonly FILE_NOT_FOUND: "File not found.";
    readonly PERMISSION_DENIED: "Permission denied.";
    readonly INVALID_INPUT: "Invalid input provided.";
    readonly UNKNOWN_ERROR: "An unknown error occurred.";
};
export declare const SUCCESS_MESSAGES: {
    readonly SETTINGS_SAVED: "Settings saved successfully.";
    readonly CONVERSATION_CREATED: "New conversation created.";
    readonly CONVERSATION_DELETED: "Conversation deleted.";
    readonly FILE_SAVED: "File saved successfully.";
    readonly TOOL_EXECUTED: "Tool executed successfully.";
    readonly API_KEY_VALIDATED: "API key validated successfully.";
};
export declare const VALIDATION_RULES: {
    readonly API_KEY_MIN_LENGTH: 10;
    readonly CONVERSATION_TITLE_MAX_LENGTH: 100;
    readonly MESSAGE_MAX_LENGTH: 10000;
    readonly FILENAME_MAX_LENGTH: 255;
    readonly MAX_FILE_SIZE: number;
    readonly MAX_CONCURRENT_REQUESTS: 5;
};
export declare const FEATURE_FLAGS: {
    readonly ENABLE_VOICE_INPUT: false;
    readonly ENABLE_VOICE_OUTPUT: false;
    readonly ENABLE_PLUGINS: false;
    readonly ENABLE_CUSTOM_TOOLS: false;
    readonly ENABLE_COLLABORATION: false;
    readonly ENABLE_CLOUD_SYNC: false;
};
export declare const DEV_CONSTANTS: {
    readonly MOCK_DELAY: 1000;
    readonly DEBUG_LOGGING: true;
    readonly HOT_RELOAD: true;
    readonly DEVTOOLS_ENABLED: true;
};
export declare const PROD_CONSTANTS: {
    readonly TELEMETRY_ENDPOINT: "https://telemetry.example.com";
    readonly UPDATE_CHECK_INTERVAL: number;
    readonly CRASH_REPORT_ENDPOINT: "https://crashes.example.com";
    readonly ANALYTICS_ENDPOINT: "https://analytics.example.com";
};
export declare const REGEX_PATTERNS: {
    readonly EMAIL: RegExp;
    readonly URL: RegExp;
    readonly API_KEY: RegExp;
    readonly FILENAME: RegExp;
    readonly VERSION: RegExp;
};
export declare const COLOR_THEMES: {
    readonly DARK: {
        readonly primary: "#0ea5e9";
        readonly secondary: "#64748b";
        readonly background: "#0f172a";
        readonly surface: "#1e293b";
        readonly text: "#f8fafc";
        readonly textSecondary: "#cbd5e1";
        readonly border: "#334155";
        readonly success: "#22c55e";
        readonly warning: "#f59e0b";
        readonly error: "#ef4444";
    };
    readonly LIGHT: {
        readonly primary: "#0ea5e9";
        readonly secondary: "#64748b";
        readonly background: "#ffffff";
        readonly surface: "#f8fafc";
        readonly text: "#0f172a";
        readonly textSecondary: "#475569";
        readonly border: "#e2e8f0";
        readonly success: "#22c55e";
        readonly warning: "#f59e0b";
        readonly error: "#ef4444";
    };
};
export type LLMProvider = typeof LLM_PROVIDERS[keyof typeof LLM_PROVIDERS];
export type ExecutionMode = typeof EXECUTION_MODES[keyof typeof EXECUTION_MODES];
export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];
//# sourceMappingURL=constants.d.ts.map