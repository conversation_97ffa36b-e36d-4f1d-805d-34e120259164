import React, { useEffect } from 'react';
import { useAppStore } from './store/appStore';
import { useSettings, useConversations, useSystemInfo, useToast } from './hooks/useElectronAPI';
// import { useHotkeys } from './hooks/useHotkeys';
import TitleBar from './components/ui/TitleBar';
import Sidebar from './components/ui/Sidebar';
import ChatInterface from './components/chat/ChatInterface';
import SettingsPanel from './components/settings/SettingsPanel';
import LoadingScreen from './components/ui/LoadingScreen';
import ErrorToast from './components/ui/ErrorToast';
import ToastContainer from './components/ui/ToastContainer';
import YoloModeIndicator from './components/ui/YoloModeIndicator';
import { useIsYoloMode } from './store/appStore';

const App: React.FC = () => {
  const {
    currentView,
    sidebarOpen,
    settingsOpen,
    loading,
    error,
    settings,
  } = useAppStore();

  const { loadSettings } = useSettings();
  const { loadConversations } = useConversations();
  const { loadSystemInfo } = useSystemInfo();
  const { toasts, hideToast } = useToast();
  const isYoloMode = useIsYoloMode();

  // Initialize hotkeys
  // useHotkeys();

  // Initialize the app on mount
  useEffect(() => {
    const initialize = async () => {
      try {
        // Load initial data
        await Promise.all([
          loadSettings(),
          loadConversations(),
          loadSystemInfo(),
        ]);
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initialize();
  }, [loadSettings, loadConversations, loadSystemInfo]);

  // Show loading screen during initialization
  if (loading || !settings) {
    return <LoadingScreen />;
  }

  return (
    <div className={`h-screen w-screen flex flex-col bg-gray-900 text-gray-100 ${isYoloMode ? 'yolo-mode' : ''}`}>
      {/* Title Bar */}
      <TitleBar />
      
      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {sidebarOpen && (
          <div className="w-80 flex-shrink-0 border-r border-gray-700">
            <Sidebar />
          </div>
        )}
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col relative">
          {currentView === 'chat' && <ChatInterface />}
          {currentView === 'settings' && <SettingsPanel />}
          {currentView === 'logs' && <LogsPanel />}
          
          {/* Settings Overlay */}
          {settingsOpen && (
            <div className="absolute inset-0 z-50 bg-black/50 backdrop-blur-sm">
              <div className="h-full flex items-center justify-center p-4">
                <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-auto">
                  <SettingsPanel />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Error Toast */}
      {error && <ErrorToast message={error} />}

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onRemove={hideToast} />

      {/* YOLO Mode Indicator */}
      <div className="fixed bottom-4 right-4 z-40">
        <YoloModeIndicator />
      </div>
    </div>
  );
};

// Placeholder component for logs panel
const LogsPanel: React.FC = () => {
  return (
    <div className="flex-1 p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-100 mb-6">System Logs</h1>
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
          <div className="font-mono text-sm text-gray-300 space-y-1">
            <div className="text-success-400">[INFO] Application started successfully</div>
            <div className="text-primary-400">[DEBUG] Loading settings...</div>
            <div className="text-success-400">[INFO] Settings loaded</div>
            <div className="text-primary-400">[DEBUG] Loading conversations...</div>
            <div className="text-success-400">[INFO] Conversations loaded</div>
            <div className="text-warning-400">[WARN] No API key configured</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;