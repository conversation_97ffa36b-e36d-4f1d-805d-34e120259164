<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;">
  <title>AI Assistant Desktop</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', system-ui, -apple-system, sans-serif;
      background-color: #18181b;
      color: #fafafa;
      overflow: hidden;
      height: 100vh;
      width: 100vw;
    }
    
    #root {
      height: 100vh;
      width: 100vw;
      display: flex;
      flex-direction: column;
    }
    
    /* Loading spinner */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      background: #18181b;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #27272a;
      border-top: 3px solid #0ea5e9;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #27272a;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #52525b;
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #71717a;
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="loading">
      <div class="spinner"></div>
    </div>
  </div>
<script defer src="vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_getU-107cdf.bundle.js"></script><script defer src="main.bundle.js"></script></body>
</html>