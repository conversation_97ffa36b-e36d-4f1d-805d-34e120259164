import type { ToolSchema } from '@shared/types';
export declare const category: "file_system";
export declare const schema: ToolSchema;
export declare function execute(args: {
    path: string;
    encoding?: string;
    maxSize?: number;
}): Promise<{
    content: string;
    size: number;
    mimeType?: string;
    encoding: string;
    lastModified: string;
    absolutePath: string;
}>;
export declare function readFileChunked(args: {
    path: string;
    chunkSize?: number;
    startOffset?: number;
    endOffset?: number;
}): Promise<{
    content: string;
    totalSize: number;
    chunkStart: number;
    chunkEnd: number;
    hasMore: boolean;
}>;
//# sourceMappingURL=read_file.d.ts.map