import '@testing-library/jest-dom';
export declare const createMockConversation: (overrides?: {}) => {
    id: string;
    title: string;
    messages: never[];
    createdAt: string;
    updatedAt: string;
};
export declare const createMockMessage: (overrides?: {}) => {
    id: string;
    role: "user";
    content: string;
    timestamp: string;
};
export declare const createMockSettings: (overrides?: {}) => {
    llm: {
        provider: string;
        model: string;
        temperature: number;
        maxTokens: number;
        topP: number;
        frequencyPenalty: number;
        presencePenalty: number;
        apiKeys: {
            openai: string;
            anthropic: string;
            deepseek: string;
        };
    };
    ui: {
        theme: string;
        sidebarOpen: boolean;
        fontSize: string;
        fontFamily: string;
        compactMode: boolean;
        showLineNumbers: boolean;
        wordWrap: boolean;
        animations: boolean;
    };
    agent: {
        defaultExecutionMode: string;
        autoSave: boolean;
        confirmDestructiveActions: boolean;
        maxConcurrentTools: number;
        toolTimeout: number;
        enableLogging: boolean;
        logLevel: string;
    };
    system: {
        autoUpdate: boolean;
        telemetry: boolean;
        crashReporting: boolean;
        hardwareAcceleration: boolean;
        startMinimized: boolean;
        minimizeToTray: boolean;
        closeToTray: boolean;
    };
    shortcuts: {
        newConversation: string;
        toggleSidebar: string;
        toggleSettings: string;
        focusInput: string;
        closeModal: string;
    };
    advanced: {
        debugMode: boolean;
        experimentalFeatures: boolean;
        customPrompts: never[];
        toolWhitelist: never[];
        toolBlacklist: never[];
        maxHistorySize: number;
        contextWindow: number;
    };
};
export declare const createMockAgentPlan: (overrides?: {}) => {
    id: string;
    title: string;
    description: string;
    requires_confirmation: boolean;
    steps: {
        id: string;
        title: string;
        description: string;
        tool: string;
        parameters: {
            path: string;
            content: string;
        };
    }[];
};
export declare const createMockToolResult: (overrides?: {}) => {
    success: boolean;
    output: string;
    data: {};
};
//# sourceMappingURL=setup.d.ts.map