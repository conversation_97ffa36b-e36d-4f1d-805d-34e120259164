import fetch from 'node-fetch';
import { <PERSON><PERSON><PERSON><PERSON>ider } from '../LLMProviderInterface';
import type { 
  LLMProviderConfig, 
  LLMResponse, 
  StreamingLLMResponse, 
  Message 
} from '@shared/types';
import { DEFAULT_MODELS } from '@shared/constants';

export class AnthropicProvider extends BaseLLMProvider {
  name = 'anthropic';
  supportedModels = DEFAULT_MODELS.anthropic as unknown as string[];

  protected buildHeaders(config: LLMProviderConfig): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'x-api-key': config.apiKey,
      'anthropic-version': '2023-06-01',
    };
  }

  protected formatMessages(messages: Message[], systemPrompt?: string): any {
    const anthropicMessages = messages
      .filter(msg => msg.role !== 'system')
      .map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content,
      }));

    return {
      messages: anthropicMessages,
      system: systemPrompt,
    };
  }

  async chat(
    messages: Message[], 
    config: LLMProviderConfig,
    systemPrompt?: string
  ): Promise<LLMResponse> {
    const url = config.baseUrl || 'https://api.anthropic.com/v1/messages';
    const headers = this.buildHeaders(config);
    const { messages: formattedMessages, system } = this.formatMessages(messages, systemPrompt);

    const body: any = {
      model: config.model,
      messages: formattedMessages,
      max_tokens: config.maxTokens || 4096,
      temperature: config.temperature || 0.7,
    };

    if (system) {
      body.system = system;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Anthropic API error: ${error.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;

      return {
        content: data.content[0]?.text || '',
        model: config.model,
        usage: data.usage && {
          promptTokens: data.usage.input_tokens,
          completionTokens: data.usage.output_tokens,
          totalTokens: data.usage.input_tokens + data.usage.output_tokens,
        },
        finishReason: data.stop_reason,
      };
    } catch (error: any) {
      throw new Error(`Anthropic provider error: ${error.message}`);
    }
  }

  async* streamChat(
    messages: Message[], 
    config: LLMProviderConfig,
    systemPrompt?: string,
    onChunk?: (chunk: StreamingLLMResponse) => void
  ): AsyncGenerator<StreamingLLMResponse, void, unknown> {
    const url = config.baseUrl || 'https://api.anthropic.com/v1/messages';
    const headers = this.buildHeaders(config);
    const { messages: formattedMessages, system } = this.formatMessages(messages, systemPrompt);

    const body: any = {
      model: config.model,
      messages: formattedMessages,
      max_tokens: config.maxTokens || 4096,
      temperature: config.temperature || 0.7,
      stream: true,
    };

    if (system) {
      body.system = system;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Anthropic API error: ${error.error?.message || 'Unknown error'}`);
      }

      const reader = (response.body as any)?.getReader();
      if (!reader) {
        throw new Error('Failed to get stream reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          const finalChunk: StreamingLLMResponse = {
            id: 'final',
            content: '',
            delta: '',
            done: true,
            model: config.model,
          };
          if (onChunk) onChunk(finalChunk);
          yield finalChunk;
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed || !trimmed.startsWith('data: ')) continue;
          
          const data = trimmed.slice(6);
          if (data === '[DONE]') continue;

          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'content_block_delta') {
              const delta = parsed.delta?.text;
              
              if (delta) {
                const chunk: StreamingLLMResponse = {
                  id: parsed.id || 'chunk',
                  content: delta,
                  delta: delta,
                  done: false,
                  model: config.model,
                };
                if (onChunk) onChunk(chunk);
                yield chunk;
              }
            }
          } catch (parseError) {
            console.error('Error parsing SSE data:', parseError);
          }
        }
      }
    } catch (error: any) {
      throw new Error(`Anthropic streaming error: ${error.message}`);
    }
  }

  countTokens(text: string, model: string): number {
    // Anthropic uses a different tokenization approach
    // This is a rough estimation since Anthropic doesn't provide tiktoken
    // 1 token ≈ 3.5 characters for Claude models
    return Math.ceil(text.length / 3.5);
  }

  validateConfig(config: LLMProviderConfig): boolean {
    return super.validateConfig(config) && config.provider === 'anthropic';
  }
}