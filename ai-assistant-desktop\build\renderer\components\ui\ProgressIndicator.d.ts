import React from 'react';
interface Step {
    id: string;
    title: string;
    description?: string;
    status: 'pending' | 'running' | 'completed' | 'error';
}
interface ProgressIndicatorProps {
    steps: Step[];
    currentStepId?: string;
    orientation?: 'horizontal' | 'vertical';
    showDescriptions?: boolean;
    className?: string;
}
declare const ProgressIndicator: React.FC<ProgressIndicatorProps>;
export default ProgressIndicator;
//# sourceMappingURL=ProgressIndicator.d.ts.map