export declare const formatFileSize: (bytes: number) => string;
export declare const formatRelativeTime: (date: Date | string) => string;
export declare const formatDuration: (milliseconds: number) => string;
export declare const truncateText: (text: string, maxLength: number) => string;
export declare const generateId: () => string;
export declare const isValidEmail: (email: string) => boolean;
export declare const isValidUrl: (url: string) => boolean;
export declare const deepClone: <T>(obj: T) => T;
export declare const debounce: <T extends (...args: any[]) => any>(func: T, wait: number) => ((...args: Parameters<T>) => void);
export declare const throttle: <T extends (...args: any[]) => any>(func: T, limit: number) => ((...args: Parameters<T>) => void);
export declare const capitalize: (str: string) => string;
export declare const camelToKebab: (str: string) => string;
export declare const kebabToCamel: (str: string) => string;
export declare const isEmpty: (obj: any) => boolean;
export declare const getNestedProperty: (obj: any, path: string) => any;
export declare const setNestedProperty: (obj: any, path: string, value: any) => void;
export declare const isEqual: (a: any, b: any) => boolean;
export declare const formatNumber: (num: number) => string;
export declare const clamp: (num: number, min: number, max: number) => number;
export declare const generateRandomColor: () => string;
export declare const sleep: (ms: number) => Promise<void>;
export declare const retry: <T>(fn: () => Promise<T>, maxAttempts?: number, baseDelay?: number) => Promise<T>;
export declare const safeJsonParse: <T>(json: string, fallback: T) => T;
export declare const safeJsonStringify: (obj: any, fallback?: string) => string;
export declare const isDevelopment: () => boolean;
export declare const isElectron: () => boolean;
export declare const getPlatform: () => "windows" | "macos" | "linux" | "unknown";
export declare const formatCode: (code: string, language?: string) => string;
export declare const getFileExtension: (filename: string) => string;
export declare const getFileNameWithoutExtension: (filename: string) => string;
export declare const isImageFile: (filename: string) => boolean;
export declare const isTextFile: (filename: string) => boolean;
//# sourceMappingURL=index.d.ts.map