/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./src/renderer/styles/globals.css":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./src/renderer/styles/globals.css ***!
  \*************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/sourceMaps.js */ "./node_modules/css-loader/dist/runtime/sourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/getUrl.js */ "./node_modules/css-loader/dist/runtime/getUrl.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__);
// Imports



var ___CSS_LOADER_URL_IMPORT_0___ = new URL(/* asset import */ __webpack_require__(/*! data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%2371717a%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e */ "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%2371717a%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e"), __webpack_require__.b);
var ___CSS_LOADER_URL_IMPORT_1___ = new URL(/* asset import */ __webpack_require__(/*! data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e */ "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e"), __webpack_require__.b);
var ___CSS_LOADER_URL_IMPORT_2___ = new URL(/* asset import */ __webpack_require__(/*! data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e */ "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e"), __webpack_require__.b);
var ___CSS_LOADER_URL_IMPORT_3___ = new URL(/* asset import */ __webpack_require__(/*! data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e */ "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e"), __webpack_require__.b);
var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
___CSS_LOADER_EXPORT___.push([module.id, "@import url(https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap);"]);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_0___);
var ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_1___);
var ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_2___);
var ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_3___);
// Module
___CSS_LOADER_EXPORT___.push([module.id, `*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e4e4e7; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured \`sans\` font-family by default.
5. Use the user's configured \`sans\` font-feature-settings by default.
6. Use the user's configured \`sans\` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Inter, system-ui, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from \`html\` so users can set them as a class directly on the \`html\` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured \`mono\` font-family by default.
2. Use the user's configured \`mono\` font-feature-settings by default.
3. Use the user's configured \`mono\` font-variation-settings by default.
4. Correct the odd \`em\` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: JetBrains Mono, Consolas, monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent \`sub\` and \`sup\` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional \`:invalid\` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to \`inherit\` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #a1a1aa; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #a1a1aa; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements \`display: block\` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add \`vertical-align: middle\` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #71717a;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #71717a;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #71717a;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_0___});
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #71717a;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_1___});
}

@media (forced-colors: active)  {

  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_2___});
}

@media (forced-colors: active)  {

  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_3___});
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {

  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}
  * {
    border-color: #e4e4e7;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

  /* Custom scrollbar styles */
  * {
    scrollbar-width: thin;
    scrollbar-color: #52525b #27272a;
  }

  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  *::-webkit-scrollbar-track {
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}

  *::-webkit-scrollbar-thumb {
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}

  *::-webkit-scrollbar-thumb:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
}

  *::-webkit-scrollbar-corner {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}
.\\!container {
  width: 100% !important;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .\\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .\\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .\\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .\\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .\\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}
.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}
.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}
.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}
.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}
.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}
.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\\201C""\\201D""\\2018""\\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}
.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}
.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}
.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows), 0 3px 0 var(--tw-prose-kbd-shadows);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "\`";
}
.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "\`";
}
.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}
.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}
.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}
.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}
.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}
.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}
.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}
.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: rgb(17, 24, 39 / 10%);
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: rgb(255, 255, 255 / 10%);
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}
.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}
.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}
.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}
.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}
.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose-sm {
  font-size: 0.875rem;
  line-height: 1.7142857;
}
.prose-sm :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}
.prose-sm :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  line-height: 1.5555556;
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-sm :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.1111111em;
}
.prose-sm :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.1428571em;
  margin-top: 0;
  margin-bottom: 0.8em;
  line-height: 1.2;
}
.prose-sm :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.4285714em;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}
.prose-sm :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  margin-top: 1.5555556em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}
.prose-sm :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.4285714em;
  margin-bottom: 0.5714286em;
  line-height: 1.4285714;
}
.prose-sm :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-sm :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  border-radius: 0.3125rem;
  padding-top: 0.1428571em;
  padding-inline-end: 0.3571429em;
  padding-bottom: 0.1428571em;
  padding-inline-start: 0.3571429em;
}
.prose-sm :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
}
.prose-sm :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}
.prose-sm :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}
.prose-sm :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.6666667;
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  border-radius: 0.25rem;
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}
.prose-sm :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}
.prose-sm :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}
.prose-sm :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  margin-bottom: 0.2857143em;
}
.prose-sm :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}
.prose-sm :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}
.prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}
.prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}
.prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}
.prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}
.prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}
.prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}
.prose-sm :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}
.prose-sm :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}
.prose-sm :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  padding-inline-start: 1.5714286em;
}
.prose-sm :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8571429em;
  margin-bottom: 2.8571429em;
}
.prose-sm :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.5;
}
.prose-sm :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}
.prose-sm :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose-sm :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}
.prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}
.prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}
.prose-sm :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-sm :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.3333333;
  margin-top: 0.6666667em;
}
.prose-sm :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose-invert {
  --tw-prose-body: var(--tw-prose-invert-body);
  --tw-prose-headings: var(--tw-prose-invert-headings);
  --tw-prose-lead: var(--tw-prose-invert-lead);
  --tw-prose-links: var(--tw-prose-invert-links);
  --tw-prose-bold: var(--tw-prose-invert-bold);
  --tw-prose-counters: var(--tw-prose-invert-counters);
  --tw-prose-bullets: var(--tw-prose-invert-bullets);
  --tw-prose-hr: var(--tw-prose-invert-hr);
  --tw-prose-quotes: var(--tw-prose-invert-quotes);
  --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);
  --tw-prose-captions: var(--tw-prose-invert-captions);
  --tw-prose-kbd: var(--tw-prose-invert-kbd);
  --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);
  --tw-prose-code: var(--tw-prose-invert-code);
  --tw-prose-pre-code: var(--tw-prose-invert-pre-code);
  --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);
  --tw-prose-th-borders: var(--tw-prose-invert-th-borders);
  --tw-prose-td-borders: var(--tw-prose-invert-td-borders);
}
/* Button variants */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.btn:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #18181b;
}
.btn:disabled {
  pointer-events: none;
  opacity: 0.5;
}
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.btn-primary:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #18181b;
}
.btn-primary:disabled {
  pointer-events: none;
  opacity: 0.5;
}
.btn-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));
}
.btn-primary:active {
  --tw-bg-opacity: 1;
  background-color: rgb(7 89 133 / var(--tw-bg-opacity, 1));
}
.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.btn-secondary:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #18181b;
}
.btn-secondary:disabled {
  pointer-events: none;
  opacity: 0.5;
}
.btn-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}
.btn-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}
.btn-secondary:active {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}
.btn-sm {
  height: 2rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
}
.btn-md {
  height: 2.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
/* Input styles */
.\\!input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}
.\\!input::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.\\!input::placeholder {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.\\!input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #18181b;
}
.\\!input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}
.input::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.input::placeholder {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #18181b;
}
.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.textarea {
  display: flex;
  min-height: 80px;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}
.textarea::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.textarea::placeholder {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.textarea:focus {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #18181b;
}
.textarea:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
/* Card styles */
/* Badge styles */
/* Loading states */
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-width: 2px;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
  --tw-border-opacity: 1;
  border-top-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}
@keyframes loading-dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }
/* Animations */
.slide-down {
    animation: slide-down 0.3s ease-out;
  }
@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
@keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
@keyframes slide-down {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
/* Syntax highlighting for code blocks */
/* Diff viewer styles */
/* Message bubble styles */
.message-bubble {
  max-width: 80%;
  overflow-wrap: break-word;
  border-radius: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
/* Execution mode indicator */
.yolo-mode {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.yolo-mode::before {
    content: '';
    pointer-events: none;
    position: absolute;
    inset: 0px;
  }
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.yolo-mode::before {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  border-radius: 0.5rem;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
/* Tool result styles */
/* Context menu styles */
.context-menu {
  position: absolute;
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
  padding: 0.25rem;
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.context-menu-item {
  position: relative;
  display: flex;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  align-items: center;
  border-radius: 0.125rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.context-menu-item:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}
.context-menu-item:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.bottom-4 {
  bottom: 1rem;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.top-1\\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-4 {
  top: 1rem;
}
.top-8 {
  top: 2rem;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-12 {
  margin-left: 3rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-12 {
  margin-right: 3rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mt-0\\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.block {
  display: block;
}
.\\!inline {
  display: inline !important;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.h-0\\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-5\\/6 {
  height: 83.333333%;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-8 {
  height: 2rem;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-\\[80vh\\] {
  max-height: 80vh;
}
.max-h-\\[90vh\\] {
  max-height: 90vh;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0\\.5 {
  width: 0.125rem;
}
.w-1 {
  width: 0.25rem;
}
.w-1\\/2 {
  width: 50%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-20 {
  width: 5rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\\/4 {
  width: 75%;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.w-screen {
  width: 100vw;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-80 {
  min-width: 20rem;
}
.max-w-24 {
  max-width: 6rem;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.-translate-y-1\\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.border-danger-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));
}
.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.border-green-700 {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.border-red-800 {
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}
.border-success-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.border-warning-700 {
  --tw-border-opacity: 1;
  border-color: rgb(180 83 9 / var(--tw-border-opacity, 1));
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}
.border-yellow-700 {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.border-t-primary-500 {
  --tw-border-opacity: 1;
  border-top-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.bg-blue-900\\/20 {
  background-color: rgb(30 58 138 / 0.2);
}
.bg-blue-900\\/50 {
  background-color: rgb(30 58 138 / 0.5);
}
.bg-danger-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-danger-900\\/20 {
  background-color: rgb(127 29 29 / 0.2);
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(161 161 170 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}
.bg-gray-800\\/50 {
  background-color: rgb(39 39 42 / 0.5);
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.bg-green-900\\/20 {
  background-color: rgb(20 83 45 / 0.2);
}
.bg-green-900\\/50 {
  background-color: rgb(20 83 45 / 0.5);
}
.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}
.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.bg-red-900\\/20 {
  background-color: rgb(127 29 29 / 0.2);
}
.bg-red-900\\/30 {
  background-color: rgb(127 29 29 / 0.3);
}
.bg-red-900\\/50 {
  background-color: rgb(127 29 29 / 0.5);
}
.bg-success-900\\/20 {
  background-color: rgb(20 83 45 / 0.2);
}
.bg-warning-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}
.bg-warning-900\\/50 {
  background-color: rgb(120 53 15 / 0.5);
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-yellow-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}
.bg-yellow-900\\/50 {
  background-color: rgb(113 63 18 / 0.5);
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-30 {
  --tw-bg-opacity: 0.3;
}
.bg-opacity-40 {
  --tw-bg-opacity: 0.4;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.from-primary-500 {
  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 165 233 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-pink-600 {
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}
.to-primary-600 {
  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-0\\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pt-4 {
  padding-top: 1rem;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-mono {
  font-family: JetBrains Mono, Consolas, monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-relaxed {
  line-height: 1.625;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-danger-100 {
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.text-danger-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-danger-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 216 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity, 1));
}
.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-primary-200 {
  --tw-text-opacity: 1;
  color: rgb(186 230 253 / var(--tw-text-opacity, 1));
}
.text-primary-400 {
  --tw-text-opacity: 1;
  color: rgb(56 189 248 / var(--tw-text-opacity, 1));
}
.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}
.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-success-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-success-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-warning-200 {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.text-warning-300 {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.text-warning-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.text-warning-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(161 161 170 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(161 161 170 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-50 {
  opacity: 0.5;
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-red-500\\/50 {
  --tw-ring-color: rgb(239 68 68 / 0.5);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
/* Custom utilities */
/* Focus utilities */
/* Safe area utilities for frameless window */
.drag-region {
    -webkit-app-region: drag;
  }
.no-drag {
    -webkit-app-region: no-drag;
  }

.last\\:mb-0:last-child {
  margin-bottom: 0px;
}

.hover\\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-danger-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-danger-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-green-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-primary-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-red-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.hover\\:bg-opacity-40:hover {
  --tw-bg-opacity: 0.4;
}

.hover\\:bg-opacity-50:hover {
  --tw-bg-opacity: 0.5;
}

.hover\\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}

.hover\\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(212 212 216 / var(--tw-text-opacity, 1));
}

.hover\\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\\:border-primary-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}

.focus\\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.focus\\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
}

.focus\\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\\:ring-yellow-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}

.focus\\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\\:ring-offset-gray-800:focus {
  --tw-ring-offset-color: #27272a;
}

.disabled\\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\\:opacity-100 {
  opacity: 1;
}`, "",{"version":3,"sources":["webpack://./src/renderer/styles/globals.css"],"names":[],"mappings":"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,gDAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,yDAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,UAAc;EAAd,iCAAc;UAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;KAAd,sBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,yDAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yDAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,yDAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,qBAAc;EAAA;;EAAd;IAAA,2CAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd,yDAAc;EAAd,oBAAc;EAAd;AAAc;;EAAd,4BAAc;EAAd;IAAA,qBAAc;IAAd,gCAAc;EAAA;;EAAd;IAAA,UAAc;IAAd,WAAc;EAAA;;EAAd;EAAA,sBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,sBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;EAAA,2BAAoB;EAApB,eAAoB;AAAA;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA,2BAAoB;EAApB,iBAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB;AAAoB;AAApB;EAAA,4BAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,wBAAoB;EAApB,kBAAoB;EAApB,qBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,qBAAoB;EAApB,kBAAoB;EAApB,qBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB;AAAoB;AAApB;EAAA,gCAAoB;EAApB,qBAAoB;EAApB,eAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB,kBAAoB;EAApB,6BAAoB;EAApB,kCAAoB;EAApB,wDAAoB;EAApB,oCAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB,aAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,gBAAoB;EAApB,eAAoB;EAApB,kBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,eAAoB;EAApB;AAAoB;AAApB;EAAA,cAAoB;EAApB,eAAoB;EAApB;AAAoB;AAApB;EAAA,eAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB,oBAAoB;EAApB,0BAAoB;EAApB,sFAAoB;EAApB,kBAAoB;EAApB,wBAAoB;EAApB,qBAAoB;EAApB,2BAAoB;EAApB,wBAAoB;EAApB;AAAoB;AAApB;EAAA,2BAAoB;EAApB,gBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,+BAAoB;EAApB,wCAAoB;EAApB,gBAAoB;EAApB,gBAAoB;EAApB,kBAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,0BAAoB;EAApB,uBAAoB;EAApB,wBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,6BAAoB;EAApB,eAAoB;EAApB,gBAAoB;EAApB,UAAoB;EAApB,oBAAoB;EAApB,cAAoB;EAApB,kBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,WAAoB;EAApB,kBAAoB;EAApB,eAAoB;EAApB,kBAAoB;EAApB,kBAAoB;EAApB;AAAoB;AAApB;EAAA,wBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,sBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,wBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,qBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,kBAAoB;EAApB,sBAAoB;EAApB;AAAoB;AAApB;EAAA,wBAAoB;EAApB,4BAAoB;EAApB,wBAAoB;EAApB,yBAAoB;EAApB,wBAAoB;EAApB,4BAAoB;EAApB,2BAAoB;EAApB,sBAAoB;EAApB,0BAAoB;EAApB,iCAAoB;EAApB,4BAAoB;EAApB,uBAAoB;EAApB,6CAAoB;EAApB,wBAAoB;EAApB,4BAAoB;EAApB,0BAAoB;EAApB,8BAAoB;EAApB,8BAAoB;EAApB,+BAAoB;EAApB,gCAAoB;EAApB,+BAAoB;EAApB,6BAAoB;EAApB,4BAAoB;EAApB,mCAAoB;EAApB,kCAAoB;EAApB,6BAAoB;EAApB,iCAAoB;EAApB,wCAAoB;EAApB,mCAAoB;EAApB,2BAAoB;EAApB,uDAAoB;EAApB,4BAAoB;EAApB,mCAAoB;EAApB,0CAAoB;EAApB,qCAAoB;EAApB,qCAAoB;EAApB,eAAoB;EAApB;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,iBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA,iBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,wBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,eAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,mBAAoB;EAApB,sBAAoB;AAAA;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,aAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,wBAAoB;EAApB,wBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,sBAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,0BAAoB;EAApB,sBAAoB;EAApB,wBAAoB;EAApB,uBAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,sBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,wBAAoB;EAApB,uBAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,sBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,4CAAoB;EAApB,oDAAoB;EAApB,4CAAoB;EAApB,8CAAoB;EAApB,4CAAoB;EAApB,oDAAoB;EAApB,kDAAoB;EAApB,wCAAoB;EAApB,gDAAoB;EAApB,8DAAoB;EAApB,oDAAoB;EAApB,0CAAoB;EAApB,0DAAoB;EAApB,4CAAoB;EAApB,oDAAoB;EAApB,gDAAoB;EAApB,wDAAoB;EAApB;AAAoB;AA2ClB,oBAAoB;AAElB;EAAA,oBAA8R;EAA9R,mBAA8R;EAA9R,uBAA8R;EAA9R,uBAA8R;EAA9R,mBAA8R;EAA9R,oBAA8R;EAA9R,gBAA8R;EAA9R,+FAA8R;EAA9R,wDAA8R;EAA9R;AAA8R;AAA9R;EAAA,8BAA8R;EAA9R,mBAA8R;EAA9R,2GAA8R;EAA9R,yGAA8R;EAA9R,4FAA8R;EAA9R,oBAA8R;EAA9R,4DAA8R;EAA9R,2BAA8R;EAA9R;AAA8R;AAA9R;EAAA,oBAA8R;EAA9R;AAA8R;AAI9R;EAAA,oBAA+E;EAA/E,mBAA+E;EAA/E,uBAA+E;EAA/E,uBAA+E;EAA/E,mBAA+E;EAA/E,oBAA+E;EAA/E,gBAA+E;EAA/E,+FAA+E;EAA/E,wDAA+E;EAA/E;AAA+E;AAA/E;EAAA,8BAA+E;EAA/E,mBAA+E;EAA/E,2GAA+E;EAA/E,yGAA+E;EAA/E,4FAA+E;EAA/E,oBAA+E;EAA/E,4DAA+E;EAA/E,2BAA+E;EAA/E;AAA+E;AAA/E;EAAA,oBAA+E;EAA/E;AAA+E;AAA/E;EAAA,kBAA+E;EAA/E,0DAA+E;EAA/E,oBAA+E;EAA/E;AAA+E;AAA/E;EAAA,kBAA+E;EAA/E;AAA+E;AAA/E;EAAA,kBAA+E;EAA/E;AAA+E;AAI/E;EAAA,oBAAyE;EAAzE,mBAAyE;EAAzE,uBAAyE;EAAzE,uBAAyE;EAAzE,mBAAyE;EAAzE,oBAAyE;EAAzE,gBAAyE;EAAzE,+FAAyE;EAAzE,wDAAyE;EAAzE;AAAyE;AAAzE;EAAA,8BAAyE;EAAzE,mBAAyE;EAAzE,2GAAyE;EAAzE,yGAAyE;EAAzE,4FAAyE;EAAzE,oBAAyE;EAAzE,4DAAyE;EAAzE,2BAAyE;EAAzE;AAAyE;AAAzE;EAAA,oBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE,yDAAyE;EAAzE,oBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE;AAAyE;AAYzE;EAAA,YAAuB;EAAvB,qBAAuB;EAAvB,sBAAuB;EAAvB,kBAAuB;EAAvB;AAAuB;AAIvB;EAAA,cAAqB;EAArB,kBAAqB;EAArB,mBAAqB;EAArB,mBAAqB;EAArB;AAAqB;AAOvB,iBAAiB;AAEf;EAAA,aAA8S;EAA9S,cAA8S;EAA9S,WAA8S;EAA9S,uBAA8S;EAA9S,iBAA8S;EAA9S,sBAA8S;EAA9S,yDAA8S;EAA9S,kBAA8S;EAA9S,yDAA8S;EAA9S,qBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,oBAA8S;EAA9S,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,sBAA8S;EAA9S,2DAA8S;EAA9S,8BAA8S;EAA9S,mBAA8S;EAA9S,2GAA8S;EAA9S,yGAA8S;EAA9S,4FAA8S;EAA9S,oBAA8S;EAA9S,4DAA8S;EAA9S,2BAA8S;EAA9S;AAA8S;AAA9S;EAAA,mBAA8S;EAA9S;AAA8S;AAA9S;EAAA,aAA8S;EAA9S,cAA8S;EAA9S,WAA8S;EAA9S,uBAA8S;EAA9S,iBAA8S;EAA9S,sBAA8S;EAA9S,yDAA8S;EAA9S,kBAA8S;EAA9S,yDAA8S;EAA9S,qBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,oBAA8S;EAA9S,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,sBAA8S;EAA9S,2DAA8S;EAA9S,8BAA8S;EAA9S,mBAA8S;EAA9S,2GAA8S;EAA9S,yGAA8S;EAA9S,4FAA8S;EAA9S,oBAA8S;EAA9S,4DAA8S;EAA9S,2BAA8S;EAA9S;AAA8S;AAA9S;EAAA,mBAA8S;EAA9S;AAA8S;AAI9S;EAAA,aAAsT;EAAtT,gBAAsT;EAAtT,WAAsT;EAAtT,uBAAsT;EAAtT,iBAAsT;EAAtT,sBAAsT;EAAtT,yDAAsT;EAAtT,kBAAsT;EAAtT,yDAAsT;EAAtT,qBAAsT;EAAtT,sBAAsT;EAAtT,mBAAsT;EAAtT,sBAAsT;EAAtT,mBAAsT;EAAtT,oBAAsT;EAAtT,oBAAsT;EAAtT;AAAsT;AAAtT;EAAA,oBAAsT;EAAtT;AAAsT;AAAtT;EAAA,oBAAsT;EAAtT;AAAsT;AAAtT;EAAA,sBAAsT;EAAtT,2DAAsT;EAAtT,8BAAsT;EAAtT,mBAAsT;EAAtT,2GAAsT;EAAtT,yGAAsT;EAAtT,4FAAsT;EAAtT,oBAAsT;EAAtT,4DAAsT;EAAtT,2BAAsT;EAAtT;AAAsT;AAAtT;EAAA,mBAAsT;EAAtT;AAAsT;AAGxT,gBAAgB;AAqBhB,iBAAiB;AAyBjB,mBAAmB;AAEjB;;EAAA;IAAA;EAA8E;AAAA;AAA9E;EAAA,kCAA8E;EAA9E,qBAA8E;EAA9E,iBAA8E;EAA9E,yDAA8E;EAA9E,sBAA8E;EAA9E;AAA8E;AAQhF;IACE,UAAU,WAAW,EAAE;IACvB,MAAM,YAAY,EAAE;IACpB,MAAM,aAAa,EAAE;IACrB,YAAY,cAAc,EAAE;EAC9B;AAEA,eAAe;AASf;IACE,mCAAmC;EACrC;AAEA;IACE,OAAO,UAAU,EAAE;IACnB,KAAK,UAAU,EAAE;EACnB;AAEA;IACE;MACE,UAAU;MACV,2BAA2B;IAC7B;IACA;MACE,UAAU;MACV,wBAAwB;IAC1B;EACF;AAEA;IACE;MACE,UAAU;MACV,4BAA4B;IAC9B;IACA;MACE,UAAU;MACV,wBAAwB;IAC1B;EACF;AAEA,wCAAwC;AAaxC,uBAAuB;AAiBvB,0BAA0B;AAExB;EAAA,cAAmD;EAAnD,yBAAmD;EAAnD,qBAAmD;EAAnD,kBAAmD;EAAnD,mBAAmD;EAAnD,oBAAmD;EAAnD;AAAmD;AAerD,6BAA6B;AAE3B;EAAA,iBAAiC;EAAjC,sBAAiC;EAAjC;AAAiC;AAGnC;IACE,WAAW;IACX,oBAA+F;IAA/F,kBAA+F;IAA/F,UAA+F;EACjG;AADE;;EAAA;IAAA;EAA+F;AAAA;AAA/F;EAAA,yDAA+F;EAA/F,qBAA+F;EAA/F,iBAA+F;EAA/F,sBAA+F;EAA/F;AAA+F;AAGjG,uBAAuB;AAavB,wBAAwB;AAEtB;EAAA,kBAA6G;EAA7G,WAA6G;EAA7G,eAA6G;EAA7G,gBAA6G;EAA7G,uBAA6G;EAA7G,iBAA6G;EAA7G,sBAA6G;EAA7G,yDAA6G;EAA7G,kBAA6G;EAA7G,yDAA6G;EAA7G,gBAA6G;EAA7G,6EAA6G;EAA7G,iGAA6G;EAA7G;AAA6G;AAI7G;EAAA,kBAA0J;EAA1J,aAA0J;EAA1J,eAA0J;EAA1J,yBAA0J;KAA1J,sBAA0J;UAA1J,iBAA0J;EAA1J,mBAA0J;EAA1J,uBAA0J;EAA1J,oBAA0J;EAA1J,qBAA0J;EAA1J,qBAA0J;EAA1J,wBAA0J;EAA1J,mBAA0J;EAA1J,oBAA0J;EAA1J,oBAA0J;EAA1J,mDAA0J;EAA1J,8BAA0J;EAA1J;AAA0J;AAA1J;EAAA,kBAA0J;EAA1J;AAA0J;AAA1J;EAAA,kBAA0J;EAA1J;AAA0J;AAvQ9J;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,2BAAmB;IAAnB;EAAmB;;EAAnB;IAAA,eAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,wJAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAgRjB,qBAAqB;AAiBrB,oBAAoB;AAKpB,6CAA6C;AAK7C;IACE,wBAAwB;EAC1B;AAEA;IACE,2BAA2B;EAC7B;;AAnTF;EAAA;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,sBAoTC;EApTD;AAoTC;;AApTD;EAAA,8BAoTC;EApTD;AAoTC;;AApTD;EAAA,2GAoTC;EApTD,yGAoTC;EApTD;AAoTC;;AApTD;EAAA,2GAoTC;EApTD,yGAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC","sourcesContent":["@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');\r\n\r\n@layer base {\r\n  * {\r\n    border-color: theme('colors.gray.200');\r\n  }\r\n\r\n  html {\r\n    font-family: 'Inter', system-ui, sans-serif;\r\n  }\r\n\r\n  body {\r\n    @apply bg-gray-900 text-gray-50;\r\n  }\r\n\r\n  /* Custom scrollbar styles */\r\n  * {\r\n    scrollbar-width: thin;\r\n    scrollbar-color: theme('colors.gray.600') theme('colors.gray.800');\r\n  }\r\n\r\n  *::-webkit-scrollbar {\r\n    width: 8px;\r\n    height: 8px;\r\n  }\r\n\r\n  *::-webkit-scrollbar-track {\r\n    @apply bg-gray-800 rounded;\r\n  }\r\n\r\n  *::-webkit-scrollbar-thumb {\r\n    @apply bg-gray-600 rounded hover:bg-gray-500;\r\n  }\r\n\r\n  *::-webkit-scrollbar-corner {\r\n    @apply bg-gray-800;\r\n  }\r\n}\r\n\r\n@layer components {\r\n  /* Button variants */\r\n  .btn {\r\n    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900 disabled:pointer-events-none disabled:opacity-50;\r\n  }\r\n\r\n  .btn-primary {\r\n    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;\r\n  }\r\n\r\n  .btn-secondary {\r\n    @apply btn bg-gray-700 text-gray-200 hover:bg-gray-600 active:bg-gray-800;\r\n  }\r\n\r\n  .btn-ghost {\r\n    @apply btn hover:bg-gray-800 hover:text-gray-100;\r\n  }\r\n\r\n  .btn-danger {\r\n    @apply btn bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;\r\n  }\r\n\r\n  .btn-sm {\r\n    @apply h-8 px-3 text-xs;\r\n  }\r\n\r\n  .btn-md {\r\n    @apply h-10 px-4 py-2;\r\n  }\r\n\r\n  .btn-lg {\r\n    @apply h-12 px-8 text-base;\r\n  }\r\n\r\n  /* Input styles */\r\n  .input {\r\n    @apply flex h-10 w-full rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 placeholder:text-gray-400 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:cursor-not-allowed disabled:opacity-50;\r\n  }\r\n\r\n  .textarea {\r\n    @apply flex min-h-[80px] w-full rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 placeholder:text-gray-400 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:cursor-not-allowed disabled:opacity-50;\r\n  }\r\n\r\n  /* Card styles */\r\n  .card {\r\n    @apply rounded-lg border border-gray-700 bg-gray-800 p-6 shadow-lg;\r\n  }\r\n\r\n  .card-header {\r\n    @apply flex flex-col space-y-1.5 p-6;\r\n  }\r\n\r\n  .card-title {\r\n    @apply text-2xl font-semibold leading-none tracking-tight text-gray-100;\r\n  }\r\n\r\n  .card-description {\r\n    @apply text-sm text-gray-400;\r\n  }\r\n\r\n  .card-content {\r\n    @apply p-6 pt-0;\r\n  }\r\n\r\n  /* Badge styles */\r\n  .badge {\r\n    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;\r\n  }\r\n\r\n  .badge-default {\r\n    @apply badge bg-gray-700 text-gray-200;\r\n  }\r\n\r\n  .badge-primary {\r\n    @apply badge bg-primary-100 text-primary-800;\r\n  }\r\n\r\n  .badge-success {\r\n    @apply badge bg-success-100 text-success-800;\r\n  }\r\n\r\n  .badge-warning {\r\n    @apply badge bg-warning-100 text-warning-800;\r\n  }\r\n\r\n  .badge-danger {\r\n    @apply badge bg-danger-100 text-danger-800;\r\n  }\r\n\r\n  /* Loading states */\r\n  .loading-spinner {\r\n    @apply animate-spin rounded-full border-2 border-gray-600 border-t-primary-500;\r\n  }\r\n\r\n  .loading-dots::after {\r\n    content: '';\r\n    animation: loading-dots 1.5s steps(4, end) infinite;\r\n  }\r\n\r\n  @keyframes loading-dots {\r\n    0%, 20% { content: ''; }\r\n    40% { content: '.'; }\r\n    60% { content: '..'; }\r\n    80%, 100% { content: '...'; }\r\n  }\r\n\r\n  /* Animations */\r\n  .fade-in {\r\n    animation: fade-in 0.5s ease-in-out;\r\n  }\r\n\r\n  .slide-up {\r\n    animation: slide-up 0.3s ease-out;\r\n  }\r\n\r\n  .slide-down {\r\n    animation: slide-down 0.3s ease-out;\r\n  }\r\n\r\n  @keyframes fade-in {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n  }\r\n\r\n  @keyframes slide-up {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(10px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  @keyframes slide-down {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(-10px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  /* Syntax highlighting for code blocks */\r\n  .code-block {\r\n    @apply bg-gray-900 rounded-lg p-4 overflow-x-auto;\r\n  }\r\n\r\n  .code-block pre {\r\n    @apply text-sm font-mono text-gray-100;\r\n  }\r\n\r\n  .code-block code {\r\n    @apply font-mono text-sm;\r\n  }\r\n\r\n  /* Diff viewer styles */\r\n  .diff-line {\r\n    @apply font-mono text-sm px-4 py-1 border-l-4;\r\n  }\r\n\r\n  .diff-line-add {\r\n    @apply diff-line bg-success-900/20 border-success-500 text-success-100;\r\n  }\r\n\r\n  .diff-line-remove {\r\n    @apply diff-line bg-danger-900/20 border-danger-500 text-danger-100;\r\n  }\r\n\r\n  .diff-line-normal {\r\n    @apply diff-line bg-gray-800 border-gray-600 text-gray-200;\r\n  }\r\n\r\n  /* Message bubble styles */\r\n  .message-bubble {\r\n    @apply rounded-lg px-4 py-3 max-w-[80%] break-words;\r\n  }\r\n\r\n  .message-bubble-user {\r\n    @apply message-bubble bg-primary-600 text-white ml-auto;\r\n  }\r\n\r\n  .message-bubble-assistant {\r\n    @apply message-bubble bg-gray-700 text-gray-100 mr-auto;\r\n  }\r\n\r\n  .message-bubble-system {\r\n    @apply message-bubble bg-gray-800 text-gray-300 border border-gray-600 mx-auto text-center text-sm;\r\n  }\r\n\r\n  /* Execution mode indicator */\r\n  .yolo-mode {\r\n    @apply border-2 border-danger-500;\r\n  }\r\n\r\n  .yolo-mode::before {\r\n    content: '';\r\n    @apply absolute inset-0 pointer-events-none border-2 border-danger-500 rounded-lg animate-pulse;\r\n  }\r\n\r\n  /* Tool result styles */\r\n  .tool-result {\r\n    @apply border border-gray-600 rounded-lg p-3 bg-gray-800;\r\n  }\r\n\r\n  .tool-result-success {\r\n    @apply tool-result border-success-600 bg-success-900/10;\r\n  }\r\n\r\n  .tool-result-error {\r\n    @apply tool-result border-danger-600 bg-danger-900/10;\r\n  }\r\n\r\n  /* Context menu styles */\r\n  .context-menu {\r\n    @apply absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-600 bg-gray-800 p-1 shadow-md;\r\n  }\r\n\r\n  .context-menu-item {\r\n    @apply relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm text-gray-200 outline-none hover:bg-gray-700 focus:bg-gray-700;\r\n  }\r\n\r\n  .context-menu-separator {\r\n    @apply -mx-1 my-1 h-px bg-gray-600;\r\n  }\r\n}\r\n\r\n@layer utilities {\r\n  /* Custom utilities */\r\n  .text-balance {\r\n    text-wrap: balance;\r\n  }\r\n\r\n  .scrollbar-thin {\r\n    scrollbar-width: thin;\r\n  }\r\n\r\n  .scrollbar-none {\r\n    scrollbar-width: none;\r\n  }\r\n\r\n  .scrollbar-none::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n\r\n  /* Focus utilities */\r\n  .focus-ring {\r\n    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900;\r\n  }\r\n\r\n  /* Safe area utilities for frameless window */\r\n  .safe-area-top {\r\n    padding-top: env(titlebar-area-height, 0px);\r\n  }\r\n\r\n  .drag-region {\r\n    -webkit-app-region: drag;\r\n  }\r\n\r\n  .no-drag {\r\n    -webkit-app-region: no-drag;\r\n  }\r\n}"],"sourceRoot":""}]);
// Exports
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./src/renderer/App.tsx":
/*!******************************!*\
  !*** ./src/renderer/App.tsx ***!
  \******************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! ./store/appStore */ "./src/renderer/store/appStore.ts");
const useElectronAPI_1 = __webpack_require__(/*! ./hooks/useElectronAPI */ "./src/renderer/hooks/useElectronAPI.ts");
// import { useHotkeys } from './hooks/useHotkeys';
const TitleBar_1 = __importDefault(__webpack_require__(/*! ./components/ui/TitleBar */ "./src/renderer/components/ui/TitleBar.tsx"));
const Sidebar_1 = __importDefault(__webpack_require__(/*! ./components/ui/Sidebar */ "./src/renderer/components/ui/Sidebar.tsx"));
const ChatInterface_1 = __importDefault(__webpack_require__(/*! ./components/chat/ChatInterface */ "./src/renderer/components/chat/ChatInterface.tsx"));
const SettingsPanel_1 = __importDefault(__webpack_require__(/*! ./components/settings/SettingsPanel */ "./src/renderer/components/settings/SettingsPanel.tsx"));
const LoadingScreen_1 = __importDefault(__webpack_require__(/*! ./components/ui/LoadingScreen */ "./src/renderer/components/ui/LoadingScreen.tsx"));
const ErrorToast_1 = __importDefault(__webpack_require__(/*! ./components/ui/ErrorToast */ "./src/renderer/components/ui/ErrorToast.tsx"));
const ToastContainer_1 = __importDefault(__webpack_require__(/*! ./components/ui/ToastContainer */ "./src/renderer/components/ui/ToastContainer.tsx"));
const YoloModeIndicator_1 = __importDefault(__webpack_require__(/*! ./components/ui/YoloModeIndicator */ "./src/renderer/components/ui/YoloModeIndicator.tsx"));
const appStore_2 = __webpack_require__(/*! ./store/appStore */ "./src/renderer/store/appStore.ts");
const App = () => {
    const { currentView, sidebarOpen, settingsOpen, loading, error, settings, } = (0, appStore_1.useAppStore)();
    const { loadSettings } = (0, useElectronAPI_1.useSettings)();
    const { loadConversations } = (0, useElectronAPI_1.useConversations)();
    const { loadSystemInfo } = (0, useElectronAPI_1.useSystemInfo)();
    const { toasts, hideToast } = (0, useElectronAPI_1.useToast)();
    const isYoloMode = (0, appStore_2.useIsYoloMode)();
    // Initialize hotkeys
    // useHotkeys();
    // Initialize the app on mount
    (0, react_1.useEffect)(() => {
        const initialize = async () => {
            try {
                // Load initial data
                await Promise.all([
                    loadSettings(),
                    loadConversations(),
                    loadSystemInfo(),
                ]);
            }
            catch (error) {
                console.error('Failed to initialize app:', error);
            }
        };
        initialize();
    }, [loadSettings, loadConversations, loadSystemInfo]);
    // Show loading screen during initialization
    if (loading || !settings) {
        return (0, jsx_runtime_1.jsx)(LoadingScreen_1.default, {});
    }
    return ((0, jsx_runtime_1.jsxs)("div", { className: `h-screen w-screen flex flex-col bg-gray-900 text-gray-100 ${isYoloMode ? 'yolo-mode' : ''}`, children: [(0, jsx_runtime_1.jsx)(TitleBar_1.default, {}), (0, jsx_runtime_1.jsxs)("div", { className: "flex flex-1 overflow-hidden", children: [sidebarOpen && ((0, jsx_runtime_1.jsx)("div", { className: "w-80 flex-shrink-0 border-r border-gray-700", children: (0, jsx_runtime_1.jsx)(Sidebar_1.default, {}) })), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 flex flex-col relative", children: [currentView === 'chat' && (0, jsx_runtime_1.jsx)(ChatInterface_1.default, {}), currentView === 'settings' && (0, jsx_runtime_1.jsx)(SettingsPanel_1.default, {}), currentView === 'logs' && (0, jsx_runtime_1.jsx)(LogsPanel, {}), settingsOpen && ((0, jsx_runtime_1.jsx)("div", { className: "absolute inset-0 z-50 bg-black/50 backdrop-blur-sm", children: (0, jsx_runtime_1.jsx)("div", { className: "h-full flex items-center justify-center p-4", children: (0, jsx_runtime_1.jsx)("div", { className: "bg-gray-800 border border-gray-700 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-auto", children: (0, jsx_runtime_1.jsx)(SettingsPanel_1.default, {}) }) }) }))] })] }), error && (0, jsx_runtime_1.jsx)(ErrorToast_1.default, { message: error }), (0, jsx_runtime_1.jsx)(ToastContainer_1.default, { toasts: toasts, onRemove: hideToast }), (0, jsx_runtime_1.jsx)("div", { className: "fixed bottom-4 right-4 z-40", children: (0, jsx_runtime_1.jsx)(YoloModeIndicator_1.default, {}) })] }));
};
// Placeholder component for logs panel
const LogsPanel = () => {
    return ((0, jsx_runtime_1.jsx)("div", { className: "flex-1 p-6", children: (0, jsx_runtime_1.jsxs)("div", { className: "max-w-4xl mx-auto", children: [(0, jsx_runtime_1.jsx)("h1", { className: "text-2xl font-bold text-gray-100 mb-6", children: "System Logs" }), (0, jsx_runtime_1.jsx)("div", { className: "bg-gray-800 border border-gray-700 rounded-lg p-4", children: (0, jsx_runtime_1.jsxs)("div", { className: "font-mono text-sm text-gray-300 space-y-1", children: [(0, jsx_runtime_1.jsx)("div", { className: "text-success-400", children: "[INFO] Application started successfully" }), (0, jsx_runtime_1.jsx)("div", { className: "text-primary-400", children: "[DEBUG] Loading settings..." }), (0, jsx_runtime_1.jsx)("div", { className: "text-success-400", children: "[INFO] Settings loaded" }), (0, jsx_runtime_1.jsx)("div", { className: "text-primary-400", children: "[DEBUG] Loading conversations..." }), (0, jsx_runtime_1.jsx)("div", { className: "text-success-400", children: "[INFO] Conversations loaded" }), (0, jsx_runtime_1.jsx)("div", { className: "text-warning-400", children: "[WARN] No API key configured" })] }) })] }) }));
};
exports["default"] = App;


/***/ }),

/***/ "./src/renderer/components/chat/ChatInput.tsx":
/*!****************************************************!*\
  !*** ./src/renderer/components/chat/ChatInput.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const ChatInput = () => {
    const { currentConversation, isStreaming, agentState, sendMessage, stopGeneration, } = (0, appStore_1.useAppStore)();
    const [input, setInput] = (0, react_1.useState)('');
    const [isRecording, setIsRecording] = (0, react_1.useState)(false);
    const textareaRef = (0, react_1.useRef)(null);
    const fileInputRef = (0, react_1.useRef)(null);
    // Auto-resize textarea
    (0, react_1.useEffect)(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 200)}px`;
        }
    }, [input]);
    // Focus input when conversation changes
    (0, react_1.useEffect)(() => {
        if (currentConversation && textareaRef.current) {
            textareaRef.current.focus();
        }
    }, [currentConversation]);
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!input.trim() || !currentConversation || isStreaming) {
            return;
        }
        const message = input.trim();
        setInput('');
        try {
            await sendMessage(currentConversation.id, message);
        }
        catch (error) {
            console.error('Failed to send message:', error);
        }
    };
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };
    const handleStop = () => {
        stopGeneration();
    };
    const handleFileUpload = (e) => {
        const files = Array.from(e.target.files || []);
        if (files.length > 0) {
            // TODO: Implement file upload functionality
            console.log('Files selected:', files);
        }
    };
    const toggleRecording = () => {
        if (isRecording) {
            // TODO: Stop recording and process audio
            setIsRecording(false);
        }
        else {
            // TODO: Start voice recording
            setIsRecording(true);
        }
    };
    const canSend = input.trim() && currentConversation && !isStreaming;
    const isWaitingForConfirmation = agentState?.isWaitingForConfirmation;
    const isDisabled = isStreaming || isWaitingForConfirmation;
    if (!currentConversation) {
        return null;
    }
    return ((0, jsx_runtime_1.jsx)("div", { className: "border-t border-gray-700 bg-gray-800 p-4", children: (0, jsx_runtime_1.jsxs)("div", { className: "max-w-4xl mx-auto", children: [isWaitingForConfirmation && ((0, jsx_runtime_1.jsx)("div", { className: "mb-3 p-3 bg-warning-900/50 border border-warning-700 rounded-lg text-warning-200 text-sm", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center gap-2", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-warning-400 rounded-full animate-pulse" }), (0, jsx_runtime_1.jsx)("span", { children: "The agent is waiting for your confirmation before proceeding." })] }) })), isStreaming && ((0, jsx_runtime_1.jsx)("div", { className: "mb-3 p-3 bg-blue-900/50 border border-blue-700 rounded-lg text-blue-200 text-sm", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center gap-2", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-blue-400 rounded-full animate-pulse" }), (0, jsx_runtime_1.jsx)("span", { children: "Assistant is responding..." })] }), (0, jsx_runtime_1.jsxs)("button", { onClick: handleStop, className: "px-2 py-1 bg-blue-700 hover:bg-blue-600 rounded text-xs transition-colors", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Square, { className: "w-3 h-3 inline mr-1" }), "Stop"] })] }) })), (0, jsx_runtime_1.jsxs)("form", { onSubmit: handleSubmit, className: "relative", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-end gap-2", children: [(0, jsx_runtime_1.jsx)("button", { type: "button", onClick: () => fileInputRef.current?.click(), disabled: isDisabled, className: `p-2 rounded-lg transition-colors ${isDisabled
                                        ? 'text-gray-600 cursor-not-allowed'
                                        : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'}`, title: "Attach files", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Paperclip, { className: "w-5 h-5" }) }), (0, jsx_runtime_1.jsx)("div", { className: "flex-1 relative", children: (0, jsx_runtime_1.jsx)("textarea", { ref: textareaRef, value: input, onChange: (e) => setInput(e.target.value), onKeyDown: handleKeyDown, placeholder: isDisabled
                                            ? 'Please wait...'
                                            : 'Type your message... (Enter to send, Shift+Enter for new line)', disabled: isDisabled, className: `w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl resize-none transition-colors ${isDisabled
                                            ? 'opacity-50 cursor-not-allowed'
                                            : 'focus:border-primary-500 focus:ring-1 focus:ring-primary-500'} text-gray-100 placeholder-gray-400`, rows: 1, style: { maxHeight: '200px' } }) }), (0, jsx_runtime_1.jsx)("button", { type: "button", onClick: toggleRecording, disabled: isDisabled, className: `p-2 rounded-lg transition-colors ${isRecording
                                        ? 'bg-red-600 text-white'
                                        : isDisabled
                                            ? 'text-gray-600 cursor-not-allowed'
                                            : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'}`, title: isRecording ? 'Stop recording' : 'Start voice recording', children: isRecording ? ((0, jsx_runtime_1.jsx)(lucide_react_1.MicOff, { className: "w-5 h-5" })) : ((0, jsx_runtime_1.jsx)(lucide_react_1.Mic, { className: "w-5 h-5" })) }), (0, jsx_runtime_1.jsx)("button", { type: "submit", disabled: !canSend, className: `p-2 rounded-lg transition-colors ${canSend
                                        ? 'bg-primary-600 hover:bg-primary-700 text-white'
                                        : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`, title: "Send message (Enter)", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Send, { className: "w-5 h-5" }) })] }), (0, jsx_runtime_1.jsx)("input", { ref: fileInputRef, type: "file", multiple: true, onChange: handleFileUpload, className: "hidden", accept: ".txt,.md,.json,.csv,.pdf,.doc,.docx,.png,.jpg,.jpeg,.gif" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "mt-2 text-xs text-gray-500 text-center", children: ["Press ", (0, jsx_runtime_1.jsx)("kbd", { className: "px-1 py-0.5 bg-gray-700 rounded", children: "Enter" }), " to send,", ' ', (0, jsx_runtime_1.jsx)("kbd", { className: "px-1 py-0.5 bg-gray-700 rounded", children: "Shift" }), " +", ' ', (0, jsx_runtime_1.jsx)("kbd", { className: "px-1 py-0.5 bg-gray-700 rounded", children: "Enter" }), " for new line"] })] }) }));
};
exports["default"] = ChatInput;


/***/ }),

/***/ "./src/renderer/components/chat/ChatInterface.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/chat/ChatInterface.tsx ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const MessageBubble_1 = __importDefault(__webpack_require__(/*! ./MessageBubble */ "./src/renderer/components/chat/MessageBubble.tsx"));
const ChatInput_1 = __importDefault(__webpack_require__(/*! ./ChatInput */ "./src/renderer/components/chat/ChatInput.tsx"));
const PlanConfirmation_1 = __importDefault(__webpack_require__(/*! ./PlanConfirmation */ "./src/renderer/components/chat/PlanConfirmation.tsx"));
const ToolResultDisplay_1 = __importDefault(__webpack_require__(/*! ./ToolResultDisplay */ "./src/renderer/components/chat/ToolResultDisplay.tsx"));
const StreamingMessage_1 = __importDefault(__webpack_require__(/*! ./StreamingMessage */ "./src/renderer/components/chat/StreamingMessage.tsx"));
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const ChatInterface = () => {
    const { currentConversation, currentPlan, toolResults, isStreaming, streamingMessage, agentState, } = (0, appStore_1.useAppStore)();
    const messages = (0, appStore_1.useCurrentMessages)();
    const messagesEndRef = (0, react_1.useRef)(null);
    // Auto-scroll to bottom when new messages arrive
    (0, react_1.useEffect)(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [messages, streamingMessage, toolResults]);
    // Show welcome screen if no conversation is selected
    if (!currentConversation) {
        return ((0, jsx_runtime_1.jsx)("div", { className: "flex-1 flex items-center justify-center bg-gray-900", children: (0, jsx_runtime_1.jsxs)("div", { className: "text-center max-w-md", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Bot, { className: "w-10 h-10 text-white" }) }), (0, jsx_runtime_1.jsx)("h2", { className: "text-2xl font-bold text-gray-100 mb-4", children: "Welcome to AI Assistant" }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-400 mb-6", children: "Start a new conversation or select an existing one from the sidebar to begin chatting with your AI assistant." }), (0, jsx_runtime_1.jsxs)("div", { className: "text-sm text-gray-500 space-y-2", children: [(0, jsx_runtime_1.jsx)("p", { children: "\u2728 Multi-LLM provider support" }), (0, jsx_runtime_1.jsx)("p", { children: "\uD83D\uDEE0\uFE0F Extensible tooling system" }), (0, jsx_runtime_1.jsx)("p", { children: "\uD83E\uDD16 Intelligent agent workflow" }), (0, jsx_runtime_1.jsx)("p", { children: "\uD83D\uDCBE Conversation persistence" })] })] }) }));
    }
    return ((0, jsx_runtime_1.jsxs)("div", { className: "flex-1 flex flex-col bg-gray-900", children: [(0, jsx_runtime_1.jsxs)("div", { className: "h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageSquare, { className: "w-5 h-5 text-gray-400" }), (0, jsx_runtime_1.jsx)("h1", { className: "text-lg font-semibold text-gray-100", children: currentConversation.title }), messages.length > 0 && ((0, jsx_runtime_1.jsxs)("span", { className: "text-sm text-gray-500", children: [messages.length, " message", messages.length !== 1 ? 's' : ''] }))] }), (0, jsx_runtime_1.jsx)("div", { className: "flex items-center space-x-2 text-sm text-gray-400", children: agentState?.isWaitingForConfirmation && ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-1", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-warning-400 rounded-full animate-pulse" }), (0, jsx_runtime_1.jsx)("span", { children: "Waiting for confirmation" })] })) })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 overflow-y-auto p-6 space-y-4", children: [messages.length === 0 ? ((0, jsx_runtime_1.jsxs)("div", { className: "text-center py-12", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Bot, { className: "w-12 h-12 text-gray-600 mx-auto mb-4" }), (0, jsx_runtime_1.jsx)("h3", { className: "text-lg font-medium text-gray-300 mb-2", children: "Start the conversation" }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-500", children: "Ask me anything or give me a task to help you with." })] })) : ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [messages.map((message) => ((0, jsx_runtime_1.jsx)(MessageBubble_1.default, { message: message }, message.id))), isStreaming && streamingMessage && ((0, jsx_runtime_1.jsx)(StreamingMessage_1.default, { content: streamingMessage })), toolResults.length > 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-3", children: [(0, jsx_runtime_1.jsxs)("h4", { className: "text-sm font-medium text-gray-400 flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)("span", { children: "Tool Execution Results" }), (0, jsx_runtime_1.jsx)("div", { className: "flex-1 h-px bg-gray-700" })] }), toolResults.map((result) => ((0, jsx_runtime_1.jsx)(ToolResultDisplay_1.default, { result: result }, result.id)))] })), currentPlan && agentState?.isWaitingForConfirmation && ((0, jsx_runtime_1.jsx)(PlanConfirmation_1.default, { plan: currentPlan }))] })), (0, jsx_runtime_1.jsx)("div", { ref: messagesEndRef })] }), (0, jsx_runtime_1.jsx)(ChatInput_1.default, {})] }));
};
exports["default"] = ChatInterface;


/***/ }),

/***/ "./src/renderer/components/chat/MessageBubble.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/chat/MessageBubble.tsx ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const date_fns_1 = __webpack_require__(/*! date-fns */ "./node_modules/date-fns/index.cjs");
const react_markdown_1 = __importDefault(__webpack_require__(/*! react-markdown */ "./node_modules/react-markdown/index.js"));
const react_syntax_highlighter_1 = __webpack_require__(/*! react-syntax-highlighter */ "./node_modules/react-syntax-highlighter/dist/esm/index.js");
const prism_1 = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ "./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js");
const MessageBubble = ({ message }) => {
    const [copied, setCopied] = (0, react_1.useState)(false);
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(message.content);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        }
        catch (error) {
            console.error('Failed to copy message:', error);
        }
    };
    const getStatusIcon = () => {
        switch (message.status) {
            case 'sending':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Clock, { className: "w-3 h-3 text-gray-500 animate-pulse" });
            case 'error':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.AlertCircle, { className: "w-3 h-3 text-red-500" });
            default:
                return null;
        }
    };
    const formatTime = (timestamp) => {
        return (0, date_fns_1.format)(new Date(timestamp), 'HH:mm');
    };
    if (isSystem) {
        return ((0, jsx_runtime_1.jsx)("div", { className: "flex justify-center", children: (0, jsx_runtime_1.jsx)("div", { className: "max-w-xs px-3 py-1 text-xs text-gray-500 bg-gray-800 rounded-full", children: message.content }) }));
    }
    return ((0, jsx_runtime_1.jsxs)("div", { className: `flex gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`, children: [(0, jsx_runtime_1.jsx)("div", { className: `flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`, children: (0, jsx_runtime_1.jsx)("div", { className: `w-8 h-8 rounded-full flex items-center justify-center ${isUser
                        ? 'bg-primary-600 text-white'
                        : 'bg-gradient-to-br from-purple-500 to-pink-600 text-white'}`, children: isUser ? ((0, jsx_runtime_1.jsx)(lucide_react_1.User, { className: "w-4 h-4" })) : ((0, jsx_runtime_1.jsx)(lucide_react_1.Bot, { className: "w-4 h-4" })) }) }), (0, jsx_runtime_1.jsxs)("div", { className: `flex-1 max-w-3xl ${isUser ? 'items-end' : 'items-start'} flex flex-col`, children: [(0, jsx_runtime_1.jsxs)("div", { className: `px-4 py-3 rounded-2xl shadow-sm relative group ${isUser
                            ? 'bg-primary-600 text-white ml-12'
                            : 'bg-gray-800 text-gray-100 mr-12'} ${message.status === 'error' ? 'ring-2 ring-red-500/50' : ''}`, children: [(0, jsx_runtime_1.jsx)("div", { className: "prose prose-sm max-w-none", children: isUser ? ((0, jsx_runtime_1.jsx)("div", { className: "whitespace-pre-wrap break-words", children: message.content })) : ((0, jsx_runtime_1.jsx)(react_markdown_1.default, { components: {
                                        code({ node, inline, className, children, ...props }) {
                                            const match = /language-(\w+)/.exec(className || '');
                                            const language = match ? match[1] : '';
                                            if (!inline && language) {
                                                return ((0, jsx_runtime_1.jsxs)("div", { className: "relative", children: [(0, jsx_runtime_1.jsx)(react_syntax_highlighter_1.Prism, { style: prism_1.oneDark, language: language, PreTag: "div", className: "rounded-lg my-2", ...props, children: String(children).replace(/\n$/, '') }), (0, jsx_runtime_1.jsx)("button", { onClick: () => navigator.clipboard.writeText(String(children)), className: "absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-200 transition-colors", title: "Copy code", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Copy, { className: "w-3 h-3" }) })] }));
                                            }
                                            return ((0, jsx_runtime_1.jsx)("code", { className: "bg-gray-700 px-1 py-0.5 rounded text-sm", ...props, children: children }));
                                        },
                                        p: ({ children }) => ((0, jsx_runtime_1.jsx)("div", { className: "mb-2 last:mb-0 leading-relaxed text-sm", children: children })),
                                        ul: ({ children }) => ((0, jsx_runtime_1.jsx)("ul", { className: "list-disc list-inside space-y-1 text-sm mb-2", children: children })),
                                        ol: ({ children }) => ((0, jsx_runtime_1.jsx)("ol", { className: "list-decimal list-inside space-y-1 text-sm mb-2", children: children })),
                                        blockquote: ({ children }) => ((0, jsx_runtime_1.jsx)("blockquote", { className: "border-l-2 border-gray-600 pl-3 italic text-gray-300 text-sm", children: children })),
                                    }, className: isUser ? 'prose-invert' : '', children: message.content })) }), (0, jsx_runtime_1.jsx)("button", { onClick: handleCopy, className: `absolute top-2 right-2 p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity ${isUser
                                    ? 'text-primary-200 hover:text-white hover:bg-primary-700'
                                    : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'}`, title: "Copy message", children: copied ? ((0, jsx_runtime_1.jsx)(lucide_react_1.Check, { className: "w-3 h-3" })) : ((0, jsx_runtime_1.jsx)(lucide_react_1.Copy, { className: "w-3 h-3" })) })] }), (0, jsx_runtime_1.jsxs)("div", { className: `flex items-center gap-2 mt-1 text-xs text-gray-500 ${isUser ? 'flex-row-reverse' : 'flex-row'}`, children: [(0, jsx_runtime_1.jsx)("span", { children: formatTime(message.timestamp) }), getStatusIcon(), message.tokens && ((0, jsx_runtime_1.jsxs)("span", { className: "text-gray-600", children: [message.tokens, " tokens"] })), message.model && !isUser && ((0, jsx_runtime_1.jsx)("span", { className: "text-gray-600", children: message.model }))] }), message.status === 'error' && message.error && ((0, jsx_runtime_1.jsxs)("div", { className: "mt-2 px-3 py-2 bg-red-900/50 border border-red-800 rounded-lg text-sm text-red-200", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center gap-2 mb-1", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertCircle, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { className: "font-medium", children: "Error" })] }), (0, jsx_runtime_1.jsx)("div", { className: "text-red-300", children: message.error })] }))] })] }));
};
exports["default"] = MessageBubble;


/***/ }),

/***/ "./src/renderer/components/chat/PlanConfirmation.tsx":
/*!***********************************************************!*\
  !*** ./src/renderer/components/chat/PlanConfirmation.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const PlanConfirmation = ({ plan }) => {
    const { confirmPlan, rejectPlan } = (0, appStore_1.useAppStore)();
    const handleConfirm = () => {
        confirmPlan(plan.id);
    };
    const handleReject = () => {
        rejectPlan(plan.id);
    };
    const getStepIcon = (type) => {
        switch (type) {
            case 'tool':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Wrench, { className: "w-4 h-4" });
            case 'code':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Code, { className: "w-4 h-4" });
            case 'file':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.FileText, { className: "w-4 h-4" });
            default:
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Clock, { className: "w-4 h-4" });
        }
    };
    const getRiskColor = (risk) => {
        switch (risk) {
            case 'low':
                return 'text-green-400 bg-green-900/50 border-green-700';
            case 'medium':
                return 'text-yellow-400 bg-yellow-900/50 border-yellow-700';
            case 'high':
                return 'text-red-400 bg-red-900/50 border-red-700';
        }
    };
    return ((0, jsx_runtime_1.jsxs)("div", { className: "bg-gray-800 border border-gray-600 rounded-lg p-4 space-y-4", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-5 h-5 text-warning-400" }), (0, jsx_runtime_1.jsx)("h3", { className: "text-lg font-semibold text-gray-100", children: "Plan Confirmation Required" })] }), (0, jsx_runtime_1.jsxs)("div", { className: `px-2 py-1 rounded text-xs border ${getRiskColor(plan.riskLevel)}`, children: [plan.riskLevel.toUpperCase(), " RISK"] })] }), (0, jsx_runtime_1.jsx)("div", { className: "text-sm text-gray-300", children: (0, jsx_runtime_1.jsx)("p", { children: plan.description }) }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-3", children: [(0, jsx_runtime_1.jsx)("h4", { className: "text-sm font-medium text-gray-400", children: "Planned Actions:" }), (0, jsx_runtime_1.jsx)("div", { className: "space-y-2", children: plan.steps.map((step, index) => ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-start space-x-3 p-3 bg-gray-700 rounded-lg", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex-shrink-0 mt-0.5", children: (0, jsx_runtime_1.jsx)("div", { className: "w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-xs text-gray-300", children: index + 1 }) }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 min-w-0", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2 mb-1", children: [getStepIcon(step.type), (0, jsx_runtime_1.jsx)("span", { className: "text-sm font-medium text-gray-200", children: step.action }), step.type && ((0, jsx_runtime_1.jsx)("span", { className: "text-xs text-gray-500 bg-gray-600 px-2 py-0.5 rounded", children: step.type }))] }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-400", children: step.description }), step.details && ((0, jsx_runtime_1.jsx)("div", { className: "mt-2 p-2 bg-gray-800 rounded text-xs text-gray-500 font-mono", children: step.details }))] })] }, index))) })] }), plan.warnings && plan.warnings.length > 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-2", children: [(0, jsx_runtime_1.jsxs)("h4", { className: "text-sm font-medium text-warning-400 flex items-center space-x-1", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "Important Warnings:" })] }), (0, jsx_runtime_1.jsx)("ul", { className: "space-y-1", children: plan.warnings.map((warning, index) => ((0, jsx_runtime_1.jsxs)("li", { className: "text-sm text-warning-300 flex items-start space-x-2", children: [(0, jsx_runtime_1.jsx)("span", { className: "text-warning-500 mt-1", children: "\u2022" }), (0, jsx_runtime_1.jsx)("span", { children: warning })] }, index))) })] })), plan.estimatedDuration && ((0, jsx_runtime_1.jsxs)("div", { className: "text-sm text-gray-400", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Clock, { className: "w-4 h-4 inline mr-1" }), "Estimated completion time: ", plan.estimatedDuration] })), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-end space-x-3 pt-4 border-t border-gray-600", children: [(0, jsx_runtime_1.jsxs)("button", { onClick: handleReject, className: "flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-gray-200 rounded-lg transition-colors", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.XCircle, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "Reject" })] }), (0, jsx_runtime_1.jsxs)("button", { onClick: handleConfirm, className: "flex items-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "Approve & Execute" })] })] })] }));
};
exports["default"] = PlanConfirmation;


/***/ }),

/***/ "./src/renderer/components/chat/StreamingMessage.tsx":
/*!***********************************************************!*\
  !*** ./src/renderer/components/chat/StreamingMessage.tsx ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const react_markdown_1 = __importDefault(__webpack_require__(/*! react-markdown */ "./node_modules/react-markdown/index.js"));
const react_syntax_highlighter_1 = __webpack_require__(/*! react-syntax-highlighter */ "./node_modules/react-syntax-highlighter/dist/esm/index.js");
const prism_1 = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ "./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js");
const StreamingMessage = ({ content }) => {
    return ((0, jsx_runtime_1.jsxs)("div", { className: "flex gap-3 flex-row", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex-shrink-0 mr-3", children: (0, jsx_runtime_1.jsx)("div", { className: "w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-pink-600 text-white", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Bot, { className: "w-4 h-4" }) }) }), (0, jsx_runtime_1.jsx)("div", { className: "flex-1 max-w-3xl items-start flex flex-col", children: (0, jsx_runtime_1.jsxs)("div", { className: "px-4 py-3 rounded-2xl shadow-sm bg-gray-800 text-gray-100 mr-12 relative", children: [(0, jsx_runtime_1.jsx)("div", { className: "prose prose-sm max-w-none", children: content ? ((0, jsx_runtime_1.jsx)(react_markdown_1.default, { components: {
                                    code({ node, inline, className, children, ...props }) {
                                        const match = /language-(\w+)/.exec(className || '');
                                        const language = match ? match[1] : '';
                                        if (!inline && language) {
                                            return ((0, jsx_runtime_1.jsx)(react_syntax_highlighter_1.Prism, { style: prism_1.oneDark, language: language, PreTag: "div", className: "rounded-lg my-2", ...props, children: String(children).replace(/\n$/, '') }));
                                        }
                                        return ((0, jsx_runtime_1.jsx)("code", { className: "bg-gray-700 px-1 py-0.5 rounded text-sm", ...props, children: children }));
                                    },
                                    p: ({ children }) => ((0, jsx_runtime_1.jsx)("div", { className: "mb-2 last:mb-0 leading-relaxed text-sm", children: children })),
                                    ul: ({ children }) => ((0, jsx_runtime_1.jsx)("ul", { className: "list-disc list-inside space-y-1 text-sm mb-2", children: children })),
                                    ol: ({ children }) => ((0, jsx_runtime_1.jsx)("ol", { className: "list-decimal list-inside space-y-1 text-sm mb-2", children: children })),
                                    blockquote: ({ children }) => ((0, jsx_runtime_1.jsx)("blockquote", { className: "border-l-2 border-gray-600 pl-3 italic text-gray-300 text-sm", children: children })),
                                }, children: content })) : ((0, jsx_runtime_1.jsxs)("div", { className: "text-sm text-gray-400", children: [(0, jsx_runtime_1.jsx)("span", { children: "Thinking" }), (0, jsx_runtime_1.jsx)("span", { className: "ml-1 animate-pulse", children: "..." })] })) }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center mt-2 text-xs text-gray-500", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex space-x-1", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-1 h-1 bg-gray-400 rounded-full animate-bounce" }), (0, jsx_runtime_1.jsx)("div", { className: "w-1 h-1 bg-gray-400 rounded-full animate-bounce", style: { animationDelay: '0.1s' } }), (0, jsx_runtime_1.jsx)("div", { className: "w-1 h-1 bg-gray-400 rounded-full animate-bounce", style: { animationDelay: '0.2s' } })] }), (0, jsx_runtime_1.jsx)("span", { className: "ml-2", children: "Assistant is typing..." })] })] }) })] }));
};
exports["default"] = StreamingMessage;


/***/ }),

/***/ "./src/renderer/components/chat/ToolResultDisplay.tsx":
/*!************************************************************!*\
  !*** ./src/renderer/components/chat/ToolResultDisplay.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const ToolResultDisplay = ({ result }) => {
    const [isExpanded, setIsExpanded] = (0, react_1.useState)(true);
    const [copied, setCopied] = (0, react_1.useState)(false);
    const getToolIcon = (toolName) => {
        switch (toolName.toLowerCase()) {
            case 'file_read':
            case 'file_write':
            case 'file_search':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.FileText, { className: "w-4 h-4" });
            case 'code_execute':
            case 'code_analyze':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Code, { className: "w-4 h-4" });
            case 'terminal':
            case 'shell':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Terminal, { className: "w-4 h-4" });
            case 'database':
            case 'sql':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Database, { className: "w-4 h-4" });
            case 'web_search':
            case 'web_scrape':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Globe, { className: "w-4 h-4" });
            default:
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Wrench, { className: "w-4 h-4" });
        }
    };
    const getStatusIcon = () => {
        switch (result.status) {
            case 'success':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, { className: "w-4 h-4 text-green-500" });
            case 'error':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.XCircle, { className: "w-4 h-4 text-red-500" });
            case 'running':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Clock, { className: "w-4 h-4 text-blue-500 animate-pulse" });
            default:
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Clock, { className: "w-4 h-4 text-gray-500" });
        }
    };
    const getStatusColor = () => {
        switch (result.status) {
            case 'success':
                return 'border-green-600 bg-green-900/20';
            case 'error':
                return 'border-red-600 bg-red-900/20';
            case 'running':
                return 'border-blue-600 bg-blue-900/20';
            default:
                return 'border-gray-600 bg-gray-800';
        }
    };
    const handleCopy = async () => {
        try {
            const textToCopy = typeof result.output === 'string'
                ? result.output
                : JSON.stringify(result.output, null, 2);
            await navigator.clipboard.writeText(textToCopy);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        }
        catch (error) {
            console.error('Failed to copy result:', error);
        }
    };
    const formatOutput = (output) => {
        if (typeof output === 'string') {
            return output;
        }
        return JSON.stringify(output, null, 2);
    };
    const formatDuration = (duration) => {
        if (duration < 1000) {
            return `${duration}ms`;
        }
        return `${(duration / 1000).toFixed(2)}s`;
    };
    return ((0, jsx_runtime_1.jsxs)("div", { className: `border rounded-lg ${getStatusColor()}`, children: [(0, jsx_runtime_1.jsxs)("div", { className: "p-3", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3", children: [(0, jsx_runtime_1.jsx)("button", { onClick: () => setIsExpanded(!isExpanded), className: "flex-shrink-0 text-gray-400 hover:text-gray-300 transition-colors", children: isExpanded ? ((0, jsx_runtime_1.jsx)(lucide_react_1.ChevronDown, { className: "w-4 h-4" })) : ((0, jsx_runtime_1.jsx)(lucide_react_1.ChevronRight, { className: "w-4 h-4" })) }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [getToolIcon(result.toolName), (0, jsx_runtime_1.jsx)("span", { className: "text-sm font-medium text-gray-200", children: result.toolName })] }), getStatusIcon()] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2 text-xs text-gray-500", children: [result.duration && ((0, jsx_runtime_1.jsx)("span", { children: formatDuration(result.duration) })), result.timestamp && ((0, jsx_runtime_1.jsx)("span", { children: new Date(result.timestamp).toLocaleTimeString() }))] })] }), result.input && ((0, jsx_runtime_1.jsxs)("div", { className: "mt-2 text-sm text-gray-400", children: [(0, jsx_runtime_1.jsx)("span", { className: "font-medium", children: "Input: " }), (0, jsx_runtime_1.jsx)("span", { className: "font-mono text-xs", children: typeof result.input === 'string'
                                    ? result.input.length > 100
                                        ? `${result.input.substring(0, 100)}...`
                                        : result.input
                                    : JSON.stringify(result.input).length > 100
                                        ? `${JSON.stringify(result.input).substring(0, 100)}...`
                                        : JSON.stringify(result.input) })] }))] }), isExpanded && ((0, jsx_runtime_1.jsxs)("div", { className: "border-t border-gray-600", children: [result.status === 'error' && result.error && ((0, jsx_runtime_1.jsx)("div", { className: "p-3 bg-red-900/30", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-start space-x-2", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.XCircle, { className: "w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1", children: [(0, jsx_runtime_1.jsx)("div", { className: "text-sm font-medium text-red-300 mb-1", children: "Execution Error" }), (0, jsx_runtime_1.jsx)("div", { className: "text-sm text-red-200 font-mono whitespace-pre-wrap", children: result.error })] })] }) })), result.output && ((0, jsx_runtime_1.jsxs)("div", { className: "p-3", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between mb-2", children: [(0, jsx_runtime_1.jsx)("span", { className: "text-sm font-medium text-gray-300", children: "Output:" }), (0, jsx_runtime_1.jsx)("button", { onClick: handleCopy, className: "flex items-center space-x-1 px-2 py-1 text-xs text-gray-400 hover:text-gray-300 hover:bg-gray-700 rounded transition-colors", title: "Copy output", children: copied ? ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Check, { className: "w-3 h-3" }), (0, jsx_runtime_1.jsx)("span", { children: "Copied" })] })) : ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Copy, { className: "w-3 h-3" }), (0, jsx_runtime_1.jsx)("span", { children: "Copy" })] })) })] }), (0, jsx_runtime_1.jsx)("div", { className: "bg-gray-900 border border-gray-600 rounded p-3 max-h-80 overflow-y-auto", children: (0, jsx_runtime_1.jsx)("pre", { className: "text-sm text-gray-300 whitespace-pre-wrap font-mono", children: formatOutput(result.output) }) })] })), (result.metadata && Object.keys(result.metadata).length > 0) && ((0, jsx_runtime_1.jsxs)("div", { className: "p-3 border-t border-gray-600 bg-gray-800/50", children: [(0, jsx_runtime_1.jsx)("div", { className: "text-sm font-medium text-gray-300 mb-2", children: "Metadata:" }), (0, jsx_runtime_1.jsx)("div", { className: "grid grid-cols-2 gap-2", children: Object.entries(result.metadata).map(([key, value]) => ((0, jsx_runtime_1.jsxs)("div", { className: "text-xs", children: [(0, jsx_runtime_1.jsxs)("span", { className: "text-gray-500", children: [key, ":"] }), (0, jsx_runtime_1.jsx)("span", { className: "text-gray-400 ml-1 font-mono", children: typeof value === 'string' ? value : JSON.stringify(value) })] }, key))) })] }))] }))] }));
};
exports["default"] = ToolResultDisplay;


/***/ }),

/***/ "./src/renderer/components/modals/ConfirmationModal.tsx":
/*!**************************************************************!*\
  !*** ./src/renderer/components/modals/ConfirmationModal.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const useUtils_1 = __webpack_require__(/*! @renderer/hooks/useUtils */ "./src/renderer/hooks/useUtils.ts");
const ConfirmationModal = ({ isOpen, onClose, onConfirm, onCancel, title, message, type = 'info', confirmText = 'Confirm', cancelText = 'Cancel', details, showDetails = false, dangerous = false, loading = false, }) => {
    const containerRef = (0, useUtils_1.useFocusTrap)(isOpen);
    const modalRef = (0, useUtils_1.useOutsideClick)(() => {
        if (!loading)
            onClose();
    });
    (0, react_1.useEffect)(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        }
        else {
            document.body.style.overflow = 'unset';
        }
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);
    if (!isOpen)
        return null;
    const getIcon = () => {
        switch (type) {
            case 'warning':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-6 h-6 text-yellow-500" });
            case 'error':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.XCircle, { className: "w-6 h-6 text-red-500" });
            case 'success':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, { className: "w-6 h-6 text-green-500" });
            default:
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Info, { className: "w-6 h-6 text-blue-500" });
        }
    };
    const getHeaderColor = () => {
        switch (type) {
            case 'warning':
                return 'border-yellow-500';
            case 'error':
                return 'border-red-500';
            case 'success':
                return 'border-green-500';
            default:
                return 'border-blue-500';
        }
    };
    const getConfirmButtonStyle = () => {
        if (dangerous) {
            return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';
        }
        switch (type) {
            case 'warning':
                return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';
            case 'error':
                return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';
            case 'success':
                return 'bg-green-600 hover:bg-green-700 focus:ring-green-500';
            default:
                return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';
        }
    };
    const handleConfirm = () => {
        if (!loading) {
            onConfirm();
        }
    };
    const handleCancel = () => {
        if (!loading) {
            if (onCancel) {
                onCancel();
            }
            else {
                onClose();
            }
        }
    };
    return ((0, jsx_runtime_1.jsx)("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50", children: (0, jsx_runtime_1.jsx)("div", { ref: modalRef, className: "bg-gray-800 rounded-lg shadow-2xl w-full max-w-md mx-4", children: (0, jsx_runtime_1.jsxs)("div", { ref: containerRef, children: [(0, jsx_runtime_1.jsxs)("div", { className: `flex items-center justify-between p-6 border-b border-gray-700 border-t-4 ${getHeaderColor()}`, children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3", children: [getIcon(), (0, jsx_runtime_1.jsx)("h3", { className: "text-lg font-semibold text-gray-100", children: title })] }), !loading && ((0, jsx_runtime_1.jsx)("button", { onClick: onClose, className: "text-gray-400 hover:text-gray-200 transition-colors", disabled: loading, children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, { className: "w-5 h-5" }) }))] }), (0, jsx_runtime_1.jsxs)("div", { className: "p-6", children: [(0, jsx_runtime_1.jsx)("p", { className: "text-gray-300 mb-4 leading-relaxed", children: message }), details && showDetails && ((0, jsx_runtime_1.jsxs)("div", { className: "bg-gray-900 rounded-lg p-4 mb-4", children: [(0, jsx_runtime_1.jsx)("h4", { className: "text-sm font-medium text-gray-400 mb-2", children: "Details:" }), (0, jsx_runtime_1.jsx)("pre", { className: "text-sm text-gray-300 whitespace-pre-wrap overflow-x-auto", children: details })] })), dangerous && ((0, jsx_runtime_1.jsx)("div", { className: "bg-red-900 bg-opacity-30 border border-red-500 rounded-lg p-3 mb-4", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-4 h-4 text-red-400 flex-shrink-0" }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm text-red-300", children: "This action is potentially dangerous and cannot be undone." })] }) }))] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-end space-x-3 p-6 border-t border-gray-700", children: [(0, jsx_runtime_1.jsx)("button", { onClick: handleCancel, disabled: loading, className: "px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed", children: cancelText }), (0, jsx_runtime_1.jsx)("button", { onClick: handleConfirm, disabled: loading, className: `px-4 py-2 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${getConfirmButtonStyle()}`, children: loading ? ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" }), (0, jsx_runtime_1.jsx)("span", { children: "Processing..." })] })) : (confirmText) })] })] }) }) }));
};
exports["default"] = ConfirmationModal;


/***/ }),

/***/ "./src/renderer/components/settings/SettingsPanel.tsx":
/*!************************************************************!*\
  !*** ./src/renderer/components/settings/SettingsPanel.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const useElectronAPI_1 = __webpack_require__(/*! @renderer/hooks/useElectronAPI */ "./src/renderer/hooks/useElectronAPI.ts");
const constants_1 = __webpack_require__(/*! @shared/constants */ "./src/shared/constants.ts");
const SettingsPanel = () => {
    const { settings, setSettingsOpen } = (0, appStore_1.useAppStore)();
    const { updateSettings, resetSettings, isLoading } = (0, useElectronAPI_1.useSettings)();
    const [localSettings, setLocalSettings] = (0, react_1.useState)(null);
    const [showApiKey, setShowApiKey] = (0, react_1.useState)(false);
    const [hasChanges, setHasChanges] = (0, react_1.useState)(false);
    const [saveStatus, setSaveStatus] = (0, react_1.useState)('idle');
    (0, react_1.useEffect)(() => {
        if (settings) {
            setLocalSettings({ ...settings });
        }
    }, [settings]);
    (0, react_1.useEffect)(() => {
        if (localSettings && settings) {
            const changed = JSON.stringify(localSettings) !== JSON.stringify(settings);
            setHasChanges(changed);
        }
    }, [localSettings, settings]);
    const handleSave = async () => {
        if (!localSettings || !hasChanges)
            return;
        setSaveStatus('saving');
        try {
            await updateSettings(localSettings);
            setSaveStatus('saved');
            setTimeout(() => setSaveStatus('idle'), 2000);
        }
        catch (error) {
            setSaveStatus('error');
            setTimeout(() => setSaveStatus('idle'), 3000);
        }
    };
    const handleReset = async () => {
        if (window.confirm('Are you sure you want to reset all settings to defaults?')) {
            setSaveStatus('saving');
            try {
                await resetSettings();
                setSaveStatus('saved');
                setTimeout(() => setSaveStatus('idle'), 2000);
            }
            catch (error) {
                setSaveStatus('error');
                setTimeout(() => setSaveStatus('idle'), 3000);
            }
        }
    };
    const updateLocalSetting = (path, value) => {
        if (!localSettings)
            return;
        const keys = path.split('.');
        const newSettings = { ...localSettings };
        let current = newSettings;
        for (let i = 0; i < keys.length - 1; i++) {
            current[keys[i]] = { ...current[keys[i]] };
            current = current[keys[i]];
        }
        current[keys[keys.length - 1]] = value;
        setLocalSettings(newSettings);
    };
    if (!localSettings) {
        return ((0, jsx_runtime_1.jsx)("div", { className: "flex items-center justify-center h-64", children: (0, jsx_runtime_1.jsx)("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" }) }));
    }
    return ((0, jsx_runtime_1.jsxs)("div", { className: "flex-1 flex flex-col bg-gray-900", children: [(0, jsx_runtime_1.jsxs)("div", { className: "h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Settings, { className: "w-5 h-5 text-gray-400" }), (0, jsx_runtime_1.jsx)("h1", { className: "text-lg font-semibold text-gray-100", children: "Settings" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3", children: [saveStatus === 'saved' && ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2 text-green-400", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm", children: "Saved" })] })), saveStatus === 'error' && ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2 text-red-400", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm", children: "Error saving" })] })), hasChanges && ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsxs)("button", { onClick: handleReset, disabled: isLoading || saveStatus === 'saving', className: "px-3 py-1 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.RotateCcw, { className: "w-4 h-4 mr-1 inline" }), "Reset"] }), (0, jsx_runtime_1.jsx)("button", { onClick: handleSave, disabled: isLoading || saveStatus === 'saving', className: "px-3 py-1 text-sm bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors disabled:opacity-50", children: saveStatus === 'saving' ? ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" }), (0, jsx_runtime_1.jsx)("span", { children: "Saving..." })] })) : ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Save, { className: "w-4 h-4 mr-1 inline" }), "Save"] })) })] })), (0, jsx_runtime_1.jsx)("button", { onClick: () => setSettingsOpen(false), className: "p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700 rounded-lg transition-colors", children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, { className: "w-5 h-5" }) })] })] }), (0, jsx_runtime_1.jsx)("div", { className: "flex-1 overflow-y-auto p-6", children: (0, jsx_runtime_1.jsxs)("div", { className: "max-w-4xl mx-auto space-y-8", children: [(0, jsx_runtime_1.jsxs)("div", { className: "bg-gray-800 border border-gray-700 rounded-lg p-6", children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-xl font-semibold text-gray-100 mb-4", children: "Language Model" }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Provider" }), (0, jsx_runtime_1.jsxs)("select", { value: localSettings.llm.provider, onChange: (e) => updateLocalSetting('llm.provider', e.target.value), className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none", children: [(0, jsx_runtime_1.jsx)("option", { value: "openai", children: "OpenAI" }), (0, jsx_runtime_1.jsx)("option", { value: "anthropic", children: "Anthropic" }), (0, jsx_runtime_1.jsx)("option", { value: "deepseek", children: "DeepSeek" })] })] }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Model" }), (0, jsx_runtime_1.jsx)("select", { value: localSettings.llm.model, onChange: (e) => updateLocalSetting('llm.model', e.target.value), className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none", children: Object.entries(constants_1.DEFAULT_MODELS[localSettings.llm.provider] || {}).map(([model, label]) => ((0, jsx_runtime_1.jsx)("option", { value: model, children: label }, model))) })] }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "API Key" }), (0, jsx_runtime_1.jsxs)("div", { className: "relative", children: [(0, jsx_runtime_1.jsx)("input", { type: showApiKey ? 'text' : 'password', value: localSettings.llm.apiKey, onChange: (e) => updateLocalSetting('llm.apiKey', e.target.value), className: "w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none", placeholder: "Enter your API key" }), (0, jsx_runtime_1.jsx)("button", { type: "button", onClick: () => setShowApiKey(!showApiKey), className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200", children: showApiKey ? (0, jsx_runtime_1.jsx)(lucide_react_1.EyeOff, { className: "w-4 h-4" }) : (0, jsx_runtime_1.jsx)(lucide_react_1.Eye, { className: "w-4 h-4" }) })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Max Tokens" }), (0, jsx_runtime_1.jsx)("input", { type: "number", value: localSettings.llm.maxTokens, onChange: (e) => updateLocalSetting('llm.maxTokens', parseInt(e.target.value)), min: "1", max: "32000", className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none" })] }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Temperature" }), (0, jsx_runtime_1.jsx)("input", { type: "number", value: localSettings.llm.temperature, onChange: (e) => updateLocalSetting('llm.temperature', parseFloat(e.target.value)), min: "0", max: "2", step: "0.1", className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none" })] })] })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-gray-800 border border-gray-700 rounded-lg p-6", children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-xl font-semibold text-gray-100 mb-4", children: "Interface" }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Theme" }), (0, jsx_runtime_1.jsxs)("select", { value: localSettings.ui.theme, onChange: (e) => updateLocalSetting('ui.theme', e.target.value), className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none", children: [(0, jsx_runtime_1.jsx)("option", { value: "system", children: "System" }), (0, jsx_runtime_1.jsx)("option", { value: "light", children: "Light" }), (0, jsx_runtime_1.jsx)("option", { value: "dark", children: "Dark" })] })] }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Font Size" }), (0, jsx_runtime_1.jsxs)("select", { value: localSettings.ui.fontSize, onChange: (e) => updateLocalSetting('ui.fontSize', e.target.value), className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none", children: [(0, jsx_runtime_1.jsx)("option", { value: "small", children: "Small" }), (0, jsx_runtime_1.jsx)("option", { value: "medium", children: "Medium" }), (0, jsx_runtime_1.jsx)("option", { value: "large", children: "Large" })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("input", { type: "checkbox", id: "compactMode", checked: localSettings.ui.compactMode, onChange: (e) => updateLocalSetting('ui.compactMode', e.target.checked), className: "w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500" }), (0, jsx_runtime_1.jsx)("label", { htmlFor: "compactMode", className: "ml-2 text-sm text-gray-300", children: "Compact mode" })] })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-gray-800 border border-gray-700 rounded-lg p-6", children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-xl font-semibold text-gray-100 mb-4", children: "Agent Behavior" }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Execution Mode" }), (0, jsx_runtime_1.jsxs)("select", { value: localSettings.agent.defaultExecutionMode, onChange: (e) => updateLocalSetting('agent.defaultExecutionMode', e.target.value), className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none", children: [(0, jsx_runtime_1.jsx)("option", { value: "confirm", children: "Ask for confirmation" }), (0, jsx_runtime_1.jsx)("option", { value: "yolo", children: "Execute automatically (YOLO mode)" })] }), localSettings.agent.defaultExecutionMode === 'yolo' && ((0, jsx_runtime_1.jsx)("div", { className: "mt-2 p-3 bg-red-900 bg-opacity-30 border border-red-500 rounded-lg", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-4 h-4 text-red-400 flex-shrink-0" }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm text-red-300", children: "YOLO mode allows the agent to execute commands without confirmation. Use with caution." })] }) }))] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("input", { type: "checkbox", id: "autoSave", checked: localSettings.agent.autoSaveConversations, onChange: (e) => updateLocalSetting('agent.autoSaveConversations', e.target.checked), className: "w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500" }), (0, jsx_runtime_1.jsx)("label", { htmlFor: "autoSave", className: "ml-2 text-sm text-gray-300", children: "Auto-save conversations" })] }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("label", { className: "block text-sm font-medium text-gray-300 mb-2", children: "Max Context Length" }), (0, jsx_runtime_1.jsx)("input", { type: "number", value: localSettings.agent.maxContextLength, onChange: (e) => updateLocalSetting('agent.maxContextLength', parseInt(e.target.value)), min: "1000", max: "128000", className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none" }), (0, jsx_runtime_1.jsx)("p", { className: "text-xs text-gray-500 mt-1", children: "Maximum number of tokens to keep in conversation context" })] })] })] })] }) })] }));
};
exports["default"] = SettingsPanel;


/***/ }),

/***/ "./src/renderer/components/ui/ErrorToast.tsx":
/*!***************************************************!*\
  !*** ./src/renderer/components/ui/ErrorToast.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const ErrorToast = ({ message, duration = 5000 }) => {
    const { clearError } = (0, appStore_1.useAppStore)();
    const [isVisible, setIsVisible] = (0, react_1.useState)(true);
    (0, react_1.useEffect)(() => {
        const timer = setTimeout(() => {
            handleClose();
        }, duration);
        return () => clearTimeout(timer);
    }, [duration]);
    const handleClose = () => {
        setIsVisible(false);
        setTimeout(() => {
            clearError();
        }, 300); // Wait for animation to complete
    };
    if (!isVisible) {
        return null;
    }
    return ((0, jsx_runtime_1.jsx)("div", { className: "fixed top-4 right-4 z-50 slide-down", children: (0, jsx_runtime_1.jsxs)("div", { className: "bg-danger-600 text-white rounded-lg shadow-lg p-4 pr-12 max-w-md relative", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-start space-x-3", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertCircle, { className: "w-5 h-5 flex-shrink-0 mt-0.5" }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("h4", { className: "font-medium text-sm", children: "Error" }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm text-danger-100 mt-1", children: message })] })] }), (0, jsx_runtime_1.jsx)("button", { onClick: handleClose, className: "absolute top-2 right-2 p-1 rounded-full hover:bg-danger-700 transition-colors", children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, { className: "w-4 h-4" }) })] }) }));
};
exports["default"] = ErrorToast;


/***/ }),

/***/ "./src/renderer/components/ui/LoadingScreen.tsx":
/*!******************************************************!*\
  !*** ./src/renderer/components/ui/LoadingScreen.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const LoadingScreen = ({ message = "Initializing AI Assistant..." }) => {
    return ((0, jsx_runtime_1.jsx)("div", { className: "h-screen w-screen bg-gray-900 flex items-center justify-center", children: (0, jsx_runtime_1.jsxs)("div", { className: "text-center", children: [(0, jsx_runtime_1.jsx)("div", { className: "mb-8", children: (0, jsx_runtime_1.jsx)("div", { className: "w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto shadow-2xl", children: (0, jsx_runtime_1.jsx)("svg", { className: "w-8 h-8 text-white", fill: "currentColor", viewBox: "0 0 20 20", children: (0, jsx_runtime_1.jsx)("path", { d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" }) }) }) }), (0, jsx_runtime_1.jsx)("div", { className: "mb-6", children: (0, jsx_runtime_1.jsx)("div", { className: "w-12 h-12 mx-auto", children: (0, jsx_runtime_1.jsx)("div", { className: "loading-spinner w-full h-full" }) }) }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-2", children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-xl font-semibold text-gray-100", children: "AI Assistant Desktop" }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-400 text-sm", children: message })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex justify-center space-x-2 mt-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-primary-500 rounded-full animate-pulse" }), (0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-primary-500 rounded-full animate-pulse", style: { animationDelay: '0.2s' } }), (0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-primary-500 rounded-full animate-pulse", style: { animationDelay: '0.4s' } })] })] }) }));
};
exports["default"] = LoadingScreen;


/***/ }),

/***/ "./src/renderer/components/ui/Sidebar.tsx":
/*!************************************************!*\
  !*** ./src/renderer/components/ui/Sidebar.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const useElectronAPI_1 = __webpack_require__(/*! @renderer/hooks/useElectronAPI */ "./src/renderer/hooks/useElectronAPI.ts");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const Sidebar = () => {
    const { conversations, currentConversationId, setCurrentConversation, startNewConversation, setCurrentView, currentView, } = (0, appStore_1.useAppStore)();
    const { deleteConversation } = (0, useElectronAPI_1.useConversations)();
    const executionMode = (0, appStore_1.useExecutionMode)();
    const [activeMenu, setActiveMenu] = (0, react_1.useState)(null);
    const handleNewChat = () => {
        startNewConversation('New Chat');
    };
    const handleDeleteConversation = async (id, e) => {
        e.stopPropagation();
        if (confirm('Are you sure you want to delete this conversation?')) {
            await deleteConversation(id);
        }
        setActiveMenu(null);
    };
    const formatTime = (date) => {
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        if (days === 0) {
            return 'Today';
        }
        else if (days === 1) {
            return 'Yesterday';
        }
        else if (days < 7) {
            return `${days} days ago`;
        }
        else {
            return date.toLocaleDateString();
        }
    };
    return ((0, jsx_runtime_1.jsxs)("div", { className: "h-full flex flex-col bg-gray-800", children: [(0, jsx_runtime_1.jsx)("div", { className: "p-4 border-b border-gray-700", children: (0, jsx_runtime_1.jsxs)("button", { onClick: handleNewChat, className: "w-full btn btn-primary btn-md flex items-center justify-center space-x-2", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Plus, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "New Chat" })] }) }), (0, jsx_runtime_1.jsx)("div", { className: "px-4 py-3 border-b border-gray-700", children: (0, jsx_runtime_1.jsxs)("nav", { className: "space-y-1", children: [(0, jsx_runtime_1.jsxs)("button", { onClick: () => setCurrentView('chat'), className: `w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${currentView === 'chat'
                                ? 'bg-primary-600 text-white'
                                : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`, children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageSquare, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "Chat" })] }), (0, jsx_runtime_1.jsxs)("button", { onClick: () => setCurrentView('settings'), className: `w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${currentView === 'settings'
                                ? 'bg-primary-600 text-white'
                                : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`, children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Settings, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "Settings" })] }), (0, jsx_runtime_1.jsxs)("button", { onClick: () => setCurrentView('logs'), className: `w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${currentView === 'logs'
                                ? 'bg-primary-600 text-white'
                                : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`, children: [(0, jsx_runtime_1.jsx)(lucide_react_1.FileText, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { children: "Logs" })] })] }) }), (0, jsx_runtime_1.jsx)("div", { className: "px-4 py-3 border-b border-gray-700", children: (0, jsx_runtime_1.jsx)("div", { className: `flex items-center space-x-2 px-3 py-2 rounded-md ${executionMode === 'yolo'
                        ? 'bg-danger-900/20 border border-danger-600'
                        : 'bg-success-900/20 border border-success-600'}`, children: executionMode === 'yolo' ? ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Zap, { className: "w-4 h-4 text-danger-400" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm text-danger-300 font-medium", children: "YOLO Mode" })] })) : ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Shield, { className: "w-4 h-4 text-success-400" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm text-success-300 font-medium", children: "Confirm Mode" })] })) }) }), (0, jsx_runtime_1.jsx)("div", { className: "flex-1 overflow-y-auto", children: (0, jsx_runtime_1.jsxs)("div", { className: "px-4 py-3", children: [(0, jsx_runtime_1.jsx)("h3", { className: "text-xs font-medium text-gray-400 uppercase tracking-wide mb-3", children: "Recent Conversations" }), conversations.length === 0 ? ((0, jsx_runtime_1.jsxs)("div", { className: "text-center py-8", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageSquare, { className: "w-8 h-8 text-gray-600 mx-auto mb-3" }), (0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-500", children: "No conversations yet" }), (0, jsx_runtime_1.jsx)("p", { className: "text-xs text-gray-600 mt-1", children: "Start a new chat to begin" })] })) : ((0, jsx_runtime_1.jsx)("div", { className: "space-y-1", children: conversations.map((conversation) => ((0, jsx_runtime_1.jsxs)("div", { className: `group relative flex items-center space-x-3 p-3 rounded-md cursor-pointer transition-colors ${currentConversationId === conversation.id
                                    ? 'bg-primary-600 text-white'
                                    : 'hover:bg-gray-700 text-gray-300'}`, onClick: () => setCurrentConversation(conversation.id), children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageSquare, { className: "w-4 h-4 flex-shrink-0" }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 min-w-0", children: [(0, jsx_runtime_1.jsx)("p", { className: "text-sm font-medium truncate", children: conversation.title }), (0, jsx_runtime_1.jsxs)("p", { className: "text-xs text-gray-500 mt-0.5", children: [formatTime(conversation.updatedAt), " \u2022 ", conversation.messages.length, " messages"] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "relative", children: [(0, jsx_runtime_1.jsx)("button", { onClick: (e) => {
                                                    e.stopPropagation();
                                                    setActiveMenu(activeMenu === conversation.id ? null : conversation.id);
                                                }, className: "p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-600 transition-all", children: (0, jsx_runtime_1.jsx)(lucide_react_1.MoreHorizontal, { className: "w-4 h-4" }) }), activeMenu === conversation.id && ((0, jsx_runtime_1.jsx)("div", { className: "absolute right-0 top-8 z-50 context-menu", children: (0, jsx_runtime_1.jsxs)("button", { onClick: (e) => handleDeleteConversation(conversation.id, e), className: "context-menu-item text-danger-300 hover:bg-danger-600 hover:text-white", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Trash2, { className: "w-4 h-4 mr-2" }), "Delete"] }) }))] })] }, conversation.id))) }))] }) }), (0, jsx_runtime_1.jsx)("div", { className: "p-4 border-t border-gray-700", children: (0, jsx_runtime_1.jsxs)("div", { className: "text-xs text-gray-500 text-center", children: [(0, jsx_runtime_1.jsx)("p", { children: "AI Assistant Desktop" }), (0, jsx_runtime_1.jsx)("p", { className: "mt-1", children: "v1.0.0" })] }) })] }));
};
exports["default"] = Sidebar;


/***/ }),

/***/ "./src/renderer/components/ui/TitleBar.tsx":
/*!*************************************************!*\
  !*** ./src/renderer/components/ui/TitleBar.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const useElectronAPI_1 = __webpack_require__(/*! @renderer/hooks/useElectronAPI */ "./src/renderer/hooks/useElectronAPI.ts");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const TitleBar = () => {
    const { sidebarOpen, setSidebarOpen, setSettingsOpen, currentView, isProcessing } = (0, appStore_1.useAppStore)();
    const { minimize, maximize, close } = (0, useElectronAPI_1.useWindowControls)();
    const isYoloMode = (0, useElectronAPI_1.useIsYoloMode)();
    return ((0, jsx_runtime_1.jsxs)("div", { className: "h-12 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-4 drag-region select-none", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3 no-drag", children: [(0, jsx_runtime_1.jsx)("button", { onClick: () => setSidebarOpen(!sidebarOpen), className: "p-2 rounded-md hover:bg-gray-700 transition-colors", title: "Toggle Sidebar", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Menu, { className: "w-4 h-4 text-gray-300" }) }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded flex items-center justify-center", children: (0, jsx_runtime_1.jsx)("svg", { className: "w-3 h-3 text-white", fill: "currentColor", viewBox: "0 0 20 20", children: (0, jsx_runtime_1.jsx)("path", { d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" }) }) }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm font-medium text-gray-200", children: "AI Assistant" })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 flex items-center justify-center space-x-4 drag-region", children: [isYoloMode && ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2 px-2 py-1 bg-red-900 bg-opacity-40 border border-red-500 rounded text-red-300 text-xs font-medium", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-3 h-3" }), (0, jsx_runtime_1.jsx)("span", { children: "YOLO" })] })), isProcessing && ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-2 text-sm text-primary-400", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-4 h-4 loading-spinner" }), (0, jsx_runtime_1.jsx)("span", { children: "Processing..." })] })), (0, jsx_runtime_1.jsxs)("div", { className: "text-xs text-gray-500 uppercase tracking-wide", children: [currentView === 'chat' && 'Chat', currentView === 'settings' && 'Settings', currentView === 'logs' && 'System Logs'] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-1 no-drag", children: [(0, jsx_runtime_1.jsx)("button", { onClick: () => setSettingsOpen(true), className: "p-2 rounded-md hover:bg-gray-700 transition-colors", title: "Settings", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Settings, { className: "w-4 h-4 text-gray-300" }) }), (0, jsx_runtime_1.jsx)("div", { className: "w-px h-6 bg-gray-600 mx-2" }), (0, jsx_runtime_1.jsx)("button", { onClick: minimize, className: "p-2 rounded-md hover:bg-gray-700 transition-colors", title: "Minimize", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Minimize2, { className: "w-4 h-4 text-gray-300" }) }), (0, jsx_runtime_1.jsx)("button", { onClick: maximize, className: "p-2 rounded-md hover:bg-gray-700 transition-colors", title: "Maximize", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Maximize2, { className: "w-4 h-4 text-gray-300" }) }), (0, jsx_runtime_1.jsx)("button", { onClick: close, className: "p-2 rounded-md hover:bg-danger-600 hover:text-white transition-colors", title: "Close", children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, { className: "w-4 h-4 text-gray-300" }) })] })] }));
};
exports["default"] = TitleBar;


/***/ }),

/***/ "./src/renderer/components/ui/ToastContainer.tsx":
/*!*******************************************************!*\
  !*** ./src/renderer/components/ui/ToastContainer.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const framer_motion_1 = __webpack_require__(/*! framer-motion */ "./node_modules/framer-motion/dist/cjs/index.js");
const ToastContainer = ({ toasts, onRemove }) => {
    return ((0, jsx_runtime_1.jsx)("div", { className: "fixed top-4 right-4 z-50 space-y-2", children: (0, jsx_runtime_1.jsx)(framer_motion_1.AnimatePresence, { children: toasts.map((toast) => ((0, jsx_runtime_1.jsx)(ToastItem, { toast: toast, onRemove: onRemove }, toast.id))) }) }));
};
const ToastItem = ({ toast, onRemove, }) => {
    (0, react_1.useEffect)(() => {
        if (toast.duration && toast.duration > 0) {
            const timer = setTimeout(() => {
                onRemove(toast.id);
            }, toast.duration);
            return () => clearTimeout(timer);
        }
    }, [toast.id, toast.duration, onRemove]);
    const getIcon = () => {
        switch (toast.type) {
            case 'success':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, { className: "w-5 h-5 text-green-400" });
            case 'error':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.XCircle, { className: "w-5 h-5 text-red-400" });
            case 'warning':
                return (0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-5 h-5 text-yellow-400" });
            default:
                return (0, jsx_runtime_1.jsx)(lucide_react_1.Info, { className: "w-5 h-5 text-blue-400" });
        }
    };
    const getBackgroundColor = () => {
        switch (toast.type) {
            case 'success':
                return 'bg-green-900 border-green-500';
            case 'error':
                return 'bg-red-900 border-red-500';
            case 'warning':
                return 'bg-yellow-900 border-yellow-500';
            default:
                return 'bg-blue-900 border-blue-500';
        }
    };
    return ((0, jsx_runtime_1.jsx)(framer_motion_1.motion.div, { initial: { opacity: 0, x: 300, scale: 0.3 }, animate: { opacity: 1, x: 0, scale: 1 }, exit: { opacity: 0, x: 300, scale: 0.5, transition: { duration: 0.2 } }, className: `${getBackgroundColor()} bg-opacity-90 backdrop-blur-sm border-l-4 rounded-lg shadow-lg p-4 min-w-80 max-w-md`, children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-start space-x-3", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex-shrink-0", children: getIcon() }), (0, jsx_runtime_1.jsx)("div", { className: "flex-1 min-w-0", children: (0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-100 leading-relaxed", children: toast.message }) }), (0, jsx_runtime_1.jsx)("button", { onClick: () => onRemove(toast.id), className: "flex-shrink-0 text-gray-400 hover:text-gray-200 transition-colors", children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, { className: "w-4 h-4" }) })] }) }));
};
exports["default"] = ToastContainer;


/***/ }),

/***/ "./src/renderer/components/ui/YoloModeIndicator.tsx":
/*!**********************************************************!*\
  !*** ./src/renderer/components/ui/YoloModeIndicator.tsx ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const lucide_react_1 = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/lucide-react.js");
const framer_motion_1 = __webpack_require__(/*! framer-motion */ "./node_modules/framer-motion/dist/cjs/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
const useElectronAPI_1 = __webpack_require__(/*! @renderer/hooks/useElectronAPI */ "./src/renderer/hooks/useElectronAPI.ts");
const ConfirmationModal_1 = __importDefault(__webpack_require__(/*! @renderer/components/modals/ConfirmationModal */ "./src/renderer/components/modals/ConfirmationModal.tsx"));
const YoloModeIndicator = () => {
    const { settings } = (0, appStore_1.useAppStore)();
    const { updateSettings } = (0, useElectronAPI_1.useSettings)();
    const [showModeSwitch, setShowModeSwitch] = (0, react_1.useState)(false);
    const [showWarning, setShowWarning] = (0, react_1.useState)(false);
    const isYoloMode = settings?.agent?.defaultExecutionMode === 'yolo';
    const toggleExecutionMode = async () => {
        if (!settings)
            return;
        const newMode = isYoloMode ? 'confirm' : 'yolo';
        if (newMode === 'yolo') {
            setShowWarning(true);
        }
        else {
            await updateSettings({
                ...settings,
                agent: {
                    ...settings.agent,
                    defaultExecutionMode: 'confirm',
                },
            });
        }
    };
    const confirmYoloMode = async () => {
        if (!settings)
            return;
        await updateSettings({
            ...settings,
            agent: {
                ...settings.agent,
                defaultExecutionMode: 'yolo',
            },
        });
        setShowWarning(false);
    };
    if (!isYoloMode) {
        return ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)("div", { className: "flex items-center space-x-2", children: (0, jsx_runtime_1.jsxs)("button", { onClick: toggleExecutionMode, className: "flex items-center space-x-2 px-3 py-1 bg-green-900 bg-opacity-30 border border-green-500 rounded-lg text-green-300 hover:bg-green-800 hover:bg-opacity-40 transition-colors", title: "Switch to YOLO mode", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Shield, { className: "w-4 h-4" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm font-medium", children: "Safe Mode" })] }) }), (0, jsx_runtime_1.jsx)(ConfirmationModal_1.default, { isOpen: showWarning, onClose: () => setShowWarning(false), onConfirm: confirmYoloMode, onCancel: () => setShowWarning(false), title: "Enable YOLO Mode", message: "YOLO mode allows the AI agent to execute commands automatically without asking for confirmation. This can be dangerous and may result in unintended changes to your system.", type: "warning", dangerous: true, confirmText: "Enable YOLO Mode", cancelText: "Keep Safe Mode", details: `YOLO mode will:
• Execute shell commands automatically
• Modify files without confirmation
• Install software and dependencies
• Make system configuration changes
• Perform potentially destructive operations

Only enable this if you fully trust the AI agent and understand the risks.`, showDetails: true })] }));
    }
    return ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(framer_motion_1.motion.div, { initial: { opacity: 0, scale: 0.8 }, animate: { opacity: 1, scale: 1 }, className: "flex items-center space-x-2", children: (0, jsx_runtime_1.jsxs)(framer_motion_1.motion.button, { onClick: toggleExecutionMode, className: "flex items-center space-x-2 px-3 py-1 bg-red-900 bg-opacity-40 border border-red-500 rounded-lg text-red-300 hover:bg-red-800 hover:bg-opacity-50 transition-colors", title: "Switch to Safe mode", animate: {
                        boxShadow: [
                            '0 0 0 0 rgba(239, 68, 68, 0.4)',
                            '0 0 0 4px rgba(239, 68, 68, 0.1)',
                            '0 0 0 0 rgba(239, 68, 68, 0.4)',
                        ],
                    }, transition: {
                        duration: 2,
                        repeat: Infinity,
                    }, children: [(0, jsx_runtime_1.jsx)(framer_motion_1.motion.div, { animate: { rotate: [0, 10, -10, 0] }, transition: { duration: 0.5, repeat: Infinity, repeatDelay: 3 }, children: (0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { className: "w-4 h-4" }) }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm font-bold", children: "YOLO MODE" }), (0, jsx_runtime_1.jsx)(lucide_react_1.Zap, { className: "w-4 h-4" })] }) }), (0, jsx_runtime_1.jsx)("style", { children: `
        .yolo-mode {
          position: relative;
        }
        
        .yolo-mode::before {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 3px solid #ef4444;
          pointer-events: none;
          z-index: 9999;
          animation: yolo-pulse 3s infinite;
        }
        
        @keyframes yolo-pulse {
          0%, 100% {
            border-color: rgba(239, 68, 68, 0.3);
            box-shadow: inset 0 0 0 0 rgba(239, 68, 68, 0.1);
          }
          50% {
            border-color: rgba(239, 68, 68, 0.8);
            box-shadow: inset 0 0 0 3px rgba(239, 68, 68, 0.2);
          }
        }
        
        .yolo-mode .title-bar {
          background: linear-gradient(90deg, #1f2937 0%, #7f1d1d 100%);
        }
      ` })] }));
};
exports["default"] = YoloModeIndicator;


/***/ }),

/***/ "./src/renderer/hooks/useElectronAPI.ts":
/*!**********************************************!*\
  !*** ./src/renderer/hooks/useElectronAPI.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.useToast = exports.useDiffViewer = exports.useStreamingMessage = exports.useKeyboardShortcuts = exports.useIsYoloMode = exports.useWindowControls = exports.useFileSystem = exports.useTools = exports.useAgent = exports.useLLM = exports.useSystemInfo = exports.useConversations = exports.useSettings = exports.useElectronAPI = void 0;
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
// Hook for accessing the Electron API
const useElectronAPI = () => {
    const api = window.electronAPI;
    if (!api) {
        throw new Error('Electron API not available. Make sure the app is running in Electron.');
    }
    return api;
};
exports.useElectronAPI = useElectronAPI;
// Hook for settings management
const useSettings = () => {
    const api = (0, exports.useElectronAPI)();
    const { setSettings, setError, setLoading } = (0, appStore_1.useAppStore)();
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const loadSettings = (0, react_1.useCallback)(async () => {
        setIsLoading(true);
        try {
            const response = await api.settings.get();
            if (response.success) {
                setSettings(response.data);
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load settings');
            }
        }
        catch (error) {
            setError(error.message);
        }
        finally {
            setIsLoading(false);
        }
    }, [api.settings, setSettings, setError]);
    const updateSettings = (0, react_1.useCallback)(async (updates) => {
        setIsLoading(true);
        try {
            const response = await api.settings.set(updates);
            if (response.success) {
                await loadSettings(); // Reload settings after update
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to update settings');
            }
        }
        catch (error) {
            setError(error.message);
        }
        finally {
            setIsLoading(false);
        }
    }, [api.settings, loadSettings, setError]);
    const resetSettings = (0, react_1.useCallback)(async () => {
        setIsLoading(true);
        try {
            const response = await api.settings.reset();
            if (response.success) {
                await loadSettings(); // Reload settings after reset
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to reset settings');
            }
        }
        catch (error) {
            setError(error.message);
        }
        finally {
            setIsLoading(false);
        }
    }, [api.settings, loadSettings, setError]);
    return {
        loadSettings,
        updateSettings,
        resetSettings,
        isLoading,
    };
};
exports.useSettings = useSettings;
// Hook for conversation management
const useConversations = () => {
    const api = (0, exports.useElectronAPI)();
    const { setConversations, setError } = (0, appStore_1.useAppStore)();
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const loadConversations = (0, react_1.useCallback)(async () => {
        setIsLoading(true);
        try {
            const response = await api.conversations.list();
            if (response.success) {
                setConversations(response.data);
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load conversations');
            }
        }
        catch (error) {
            setError(error.message);
        }
        finally {
            setIsLoading(false);
        }
    }, [api.conversations, setConversations, setError]);
    const createConversation = (0, react_1.useCallback)(async (title) => {
        try {
            const response = await api.conversations.create({ title });
            if (response.success) {
                await loadConversations(); // Reload conversations
                return response.data;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to create conversation');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
    }, [api.conversations, loadConversations, setError]);
    const deleteConversation = (0, react_1.useCallback)(async (id) => {
        try {
            const response = await api.conversations.delete({ id });
            if (response.success) {
                await loadConversations(); // Reload conversations
                return true;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to delete conversation');
                return false;
            }
        }
        catch (error) {
            setError(error.message);
            return false;
        }
    }, [api.conversations, loadConversations, setError]);
    return {
        loadConversations,
        createConversation,
        deleteConversation,
        isLoading,
    };
};
exports.useConversations = useConversations;
// Hook for system information
const useSystemInfo = () => {
    const api = (0, exports.useElectronAPI)();
    const { setSystemInfo, setError } = (0, appStore_1.useAppStore)();
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const loadSystemInfo = (0, react_1.useCallback)(async () => {
        setIsLoading(true);
        try {
            const response = await api.system.info();
            if (response.success) {
                setSystemInfo(response.data);
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load system information');
            }
        }
        catch (error) {
            setError(error.message);
        }
        finally {
            setIsLoading(false);
        }
    }, [api.system, setSystemInfo, setError]);
    return {
        loadSystemInfo,
        isLoading,
    };
};
exports.useSystemInfo = useSystemInfo;
// Hook for LLM communication
const useLLM = () => {
    const api = (0, exports.useElectronAPI)();
    const { setStreaming, setStreamingMessage, appendStreamingMessage, clearStreamingMessage, setError } = (0, appStore_1.useAppStore)();
    const sendMessage = (0, react_1.useCallback)(async (messages, systemPrompt) => {
        try {
            const response = await api.llm.chat({ messages, systemPrompt });
            if (response.success) {
                return response.data.content;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to send message');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
    }, [api.llm, setError]);
    const streamMessage = (0, react_1.useCallback)(async (messages, systemPrompt, onChunk) => {
        setStreaming(true);
        clearStreamingMessage();
        try {
            const response = await api.llm.stream({ messages, systemPrompt });
            if (response.success) {
                // Handle streaming response
                // Note: In a real implementation, this would need to handle the streaming protocol
                console.log('Streaming response:', response.data);
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to stream message');
            }
        }
        catch (error) {
            setError(error.message);
        }
        finally {
            setStreaming(false);
        }
    }, [api.llm, setStreaming, clearStreamingMessage, setError]);
    return {
        sendMessage,
        streamMessage,
    };
};
exports.useLLM = useLLM;
// Hook for agent operations
const useAgent = () => {
    const api = (0, exports.useElectronAPI)();
    const { setCurrentPlan, setProcessing, addToolResult, setError } = (0, appStore_1.useAppStore)();
    const createPlan = (0, react_1.useCallback)(async (conversationId, executionMode) => {
        setProcessing(true);
        try {
            const response = await api.agent.plan({ conversationId, executionMode });
            if (response.success) {
                setCurrentPlan(response.data.plan);
                return response.data.plan;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to create plan');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
        finally {
            setProcessing(false);
        }
    }, [api.agent, setCurrentPlan, setProcessing, setError]);
    const executePlan = (0, react_1.useCallback)(async (conversationId, stepId) => {
        setProcessing(true);
        try {
            const response = await api.agent.execute({ conversationId, stepId });
            if (response.success) {
                const results = response.data.results;
                if (results) {
                    results.forEach(result => addToolResult(result));
                }
                return response.data;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to execute plan');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
        finally {
            setProcessing(false);
        }
    }, [api.agent, addToolResult, setProcessing, setError]);
    const confirmExecution = (0, react_1.useCallback)(async (conversationId, approved) => {
        try {
            const response = await api.agent.confirm({ conversationId, approved });
            if (response.success) {
                return true;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to confirm execution');
                return false;
            }
        }
        catch (error) {
            setError(error.message);
            return false;
        }
    }, [api.agent, setError]);
    return {
        createPlan,
        executePlan,
        confirmExecution,
    };
};
exports.useAgent = useAgent;
// Hook for tools
const useTools = () => {
    const api = (0, exports.useElectronAPI)();
    const { setError } = (0, appStore_1.useAppStore)();
    const [availableTools, setAvailableTools] = (0, react_1.useState)([]);
    const loadTools = (0, react_1.useCallback)(async () => {
        try {
            const response = await api.tools.list();
            if (response.success) {
                setAvailableTools(response.data);
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load tools');
            }
        }
        catch (error) {
            setError(error.message);
        }
    }, [api.tools, setError]);
    const executeTool = (0, react_1.useCallback)(async (name, args) => {
        try {
            const response = await api.tools.execute({ name, arguments: args });
            if (response.success) {
                return response.data;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Tool execution failed');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
    }, [api.tools, setError]);
    (0, react_1.useEffect)(() => {
        loadTools();
    }, [loadTools]);
    return {
        availableTools,
        loadTools,
        executeTool,
    };
};
exports.useTools = useTools;
// Hook for file system operations
const useFileSystem = () => {
    const api = (0, exports.useElectronAPI)();
    const { setError } = (0, appStore_1.useAppStore)();
    const readFile = (0, react_1.useCallback)(async (path) => {
        try {
            const response = await api.fs.read({ path });
            if (response.success) {
                return response.data;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to read file');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
    }, [api.fs, setError]);
    const writeFile = (0, react_1.useCallback)(async (path, content) => {
        try {
            const response = await api.fs.write({ path, content });
            if (response.success) {
                return true;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to write file');
                return false;
            }
        }
        catch (error) {
            setError(error.message);
            return false;
        }
    }, [api.fs, setError]);
    const listDirectory = (0, react_1.useCallback)(async (path) => {
        try {
            const response = await api.fs.list({ path });
            if (response.success) {
                return response.data;
            }
            else {
                setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to list directory');
                return null;
            }
        }
        catch (error) {
            setError(error.message);
            return null;
        }
    }, [api.fs, setError]);
    return {
        readFile,
        writeFile,
        listDirectory,
    };
};
exports.useFileSystem = useFileSystem;
// Hook for window controls
const useWindowControls = () => {
    const api = (0, exports.useElectronAPI)();
    const minimize = (0, react_1.useCallback)(() => {
        api.window.minimize().catch(console.error);
    }, [api.window]);
    const maximize = (0, react_1.useCallback)(() => {
        api.window.maximize().catch(console.error);
    }, [api.window]);
    const close = (0, react_1.useCallback)(() => {
        api.window.close().catch(console.error);
    }, [api.window]);
    return {
        minimize,
        maximize,
        close,
    };
};
exports.useWindowControls = useWindowControls;
// Hook for YOLO mode detection
const useIsYoloMode = () => {
    const { settings } = (0, appStore_1.useAppStore)();
    return settings?.agent?.defaultExecutionMode === 'yolo';
};
exports.useIsYoloMode = useIsYoloMode;
// Hook for keyboard shortcuts
const useKeyboardShortcuts = () => {
    const { currentConversation, settingsOpen, sidebarOpen } = (0, appStore_1.useAppStore)();
    const { createConversation } = (0, exports.useConversations)();
    const { minimize, maximize, close } = (0, exports.useWindowControls)();
    (0, react_1.useEffect)(() => {
        const handleKeyDown = (event) => {
            // Ctrl/Cmd + N: New conversation
            if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
                event.preventDefault();
                createConversation('New Conversation');
            }
            // Ctrl/Cmd + ,: Open settings
            if ((event.ctrlKey || event.metaKey) && event.key === ',') {
                event.preventDefault();
                appStore_1.useAppStore.getState().setSettingsOpen(!settingsOpen);
            }
            // Ctrl/Cmd + B: Toggle sidebar
            if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
                event.preventDefault();
                appStore_1.useAppStore.getState().setSidebarOpen(!sidebarOpen);
            }
            // Ctrl/Cmd + W: Close window
            if ((event.ctrlKey || event.metaKey) && event.key === 'w') {
                event.preventDefault();
                close();
            }
            // Ctrl/Cmd + M: Minimize window
            if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
                event.preventDefault();
                minimize();
            }
            // F11: Toggle maximize
            if (event.key === 'F11') {
                event.preventDefault();
                maximize();
            }
            // Escape: Close modals/settings
            if (event.key === 'Escape') {
                if (settingsOpen) {
                    appStore_1.useAppStore.getState().setSettingsOpen(false);
                }
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [createConversation, settingsOpen, sidebarOpen, close, minimize, maximize]);
};
exports.useKeyboardShortcuts = useKeyboardShortcuts;
// Hook for streaming message handling
const useStreamingMessage = () => {
    const { isStreaming, streamingMessage } = (0, appStore_1.useAppStore)();
    const [currentMessage, setCurrentMessage] = (0, react_1.useState)('');
    (0, react_1.useEffect)(() => {
        if (isStreaming && streamingMessage) {
            setCurrentMessage(streamingMessage);
        }
        else if (!isStreaming) {
            setCurrentMessage('');
        }
    }, [isStreaming, streamingMessage]);
    return {
        isStreaming,
        currentMessage,
    };
};
exports.useStreamingMessage = useStreamingMessage;
// Hook for diff viewing
const useDiffViewer = () => {
    const [diffData, setDiffData] = (0, react_1.useState)(null);
    const showDiff = (0, react_1.useCallback)((oldContent, newContent, fileName) => {
        setDiffData({ oldContent, newContent, fileName });
    }, []);
    const hideDiff = (0, react_1.useCallback)(() => {
        setDiffData(null);
    }, []);
    return {
        diffData,
        showDiff,
        hideDiff,
    };
};
exports.useDiffViewer = useDiffViewer;
// Hook for toast notifications
const useToast = () => {
    const [toasts, setToasts] = (0, react_1.useState)([]);
    const showToast = (0, react_1.useCallback)((type, message, duration = 5000) => {
        const id = Math.random().toString(36).substr(2, 9);
        const toast = { id, type, message, duration };
        setToasts(prev => [...prev, toast]);
        if (duration > 0) {
            setTimeout(() => {
                setToasts(prev => prev.filter(t => t.id !== id));
            }, duration);
        }
    }, []);
    const hideToast = (0, react_1.useCallback)((id) => {
        setToasts(prev => prev.filter(t => t.id !== id));
    }, []);
    const clearToasts = (0, react_1.useCallback)(() => {
        setToasts([]);
    }, []);
    return {
        toasts,
        showToast,
        hideToast,
        clearToasts,
    };
};
exports.useToast = useToast;


/***/ }),

/***/ "./src/renderer/hooks/useUtils.ts":
/*!****************************************!*\
  !*** ./src/renderer/hooks/useUtils.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.useScrollPosition = exports.useOutsideClick = exports.useFocusTrap = exports.useTheme = exports.useAsync = exports.useTimeout = exports.useInterval = exports.usePrevious = exports.useWindowSize = exports.useOnlineStatus = exports.useClipboard = exports.useLocalStorage = exports.useDebounce = void 0;
const react_1 = __webpack_require__(/*! react */ "./node_modules/react/index.js");
const appStore_1 = __webpack_require__(/*! @renderer/store/appStore */ "./src/renderer/store/appStore.ts");
// Hook for debounced values
const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = (0, react_1.useState)(value);
    (0, react_1.useEffect)(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);
        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);
    return debouncedValue;
};
exports.useDebounce = useDebounce;
// Hook for local storage
const useLocalStorage = (key, initialValue) => {
    const [storedValue, setStoredValue] = (0, react_1.useState)(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        }
        catch (error) {
            console.error(`Error reading localStorage key "${key}":`, error);
            return initialValue;
        }
    });
    const setValue = (0, react_1.useCallback)((value) => {
        try {
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            setStoredValue(valueToStore);
            window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
        catch (error) {
            console.error(`Error setting localStorage key "${key}":`, error);
        }
    }, [key, storedValue]);
    return [storedValue, setValue];
};
exports.useLocalStorage = useLocalStorage;
// Hook for clipboard operations
const useClipboard = () => {
    const [copied, setCopied] = (0, react_1.useState)(false);
    const copyToClipboard = (0, react_1.useCallback)(async (text) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
            return true;
        }
        catch (error) {
            console.error('Failed to copy to clipboard:', error);
            return false;
        }
    }, []);
    const readFromClipboard = (0, react_1.useCallback)(async () => {
        try {
            return await navigator.clipboard.readText();
        }
        catch (error) {
            console.error('Failed to read from clipboard:', error);
            return null;
        }
    }, []);
    return {
        copied,
        copyToClipboard,
        readFromClipboard,
    };
};
exports.useClipboard = useClipboard;
// Hook for online/offline status
const useOnlineStatus = () => {
    const [isOnline, setIsOnline] = (0, react_1.useState)(navigator.onLine);
    (0, react_1.useEffect)(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);
    return isOnline;
};
exports.useOnlineStatus = useOnlineStatus;
// Hook for window size
const useWindowSize = () => {
    const [windowSize, setWindowSize] = (0, react_1.useState)({
        width: window.innerWidth,
        height: window.innerHeight,
    });
    (0, react_1.useEffect)(() => {
        const handleResize = () => {
            setWindowSize({
                width: window.innerWidth,
                height: window.innerHeight,
            });
        };
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    return windowSize;
};
exports.useWindowSize = useWindowSize;
// Hook for previous value
const usePrevious = (value) => {
    const ref = (0, react_1.useRef)();
    (0, react_1.useEffect)(() => {
        ref.current = value;
    });
    return ref.current;
};
exports.usePrevious = usePrevious;
// Hook for interval
const useInterval = (callback, delay) => {
    const savedCallback = (0, react_1.useRef)(callback);
    (0, react_1.useEffect)(() => {
        savedCallback.current = callback;
    }, [callback]);
    (0, react_1.useEffect)(() => {
        if (delay === null)
            return;
        const id = setInterval(() => savedCallback.current(), delay);
        return () => clearInterval(id);
    }, [delay]);
};
exports.useInterval = useInterval;
// Hook for timeout
const useTimeout = (callback, delay) => {
    const savedCallback = (0, react_1.useRef)(callback);
    (0, react_1.useEffect)(() => {
        savedCallback.current = callback;
    }, [callback]);
    (0, react_1.useEffect)(() => {
        if (delay === null)
            return;
        const id = setTimeout(() => savedCallback.current(), delay);
        return () => clearTimeout(id);
    }, [delay]);
};
exports.useTimeout = useTimeout;
// Hook for async operation
const useAsync = (asyncFunction, immediate = true) => {
    const [status, setStatus] = (0, react_1.useState)('idle');
    const [value, setValue] = (0, react_1.useState)(null);
    const [error, setError] = (0, react_1.useState)(null);
    const execute = (0, react_1.useCallback)(async () => {
        setStatus('pending');
        setValue(null);
        setError(null);
        try {
            const response = await asyncFunction();
            setValue(response);
            setStatus('success');
        }
        catch (error) {
            setError(error);
            setStatus('error');
        }
    }, [asyncFunction]);
    (0, react_1.useEffect)(() => {
        if (immediate) {
            execute();
        }
    }, [execute, immediate]);
    return { execute, status, value, error };
};
exports.useAsync = useAsync;
// Hook for theme detection
const useTheme = () => {
    const { settings } = (0, appStore_1.useAppStore)();
    const [systemTheme, setSystemTheme] = (0, react_1.useState)('dark');
    (0, react_1.useEffect)(() => {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
        const handleChange = (e) => {
            setSystemTheme(e.matches ? 'dark' : 'light');
        };
        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
    }, []);
    const currentTheme = settings?.ui?.theme === 'system' ? systemTheme : settings?.ui?.theme || 'dark';
    return {
        theme: currentTheme,
        systemTheme,
        isDark: currentTheme === 'dark',
    };
};
exports.useTheme = useTheme;
// Hook for focus trap
const useFocusTrap = (isActive) => {
    const containerRef = (0, react_1.useRef)(null);
    (0, react_1.useEffect)(() => {
        if (!isActive || !containerRef.current)
            return;
        const container = containerRef.current;
        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        const handleTabKey = (e) => {
            if (e.key !== 'Tab')
                return;
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    lastElement?.focus();
                    e.preventDefault();
                }
            }
            else {
                if (document.activeElement === lastElement) {
                    firstElement?.focus();
                    e.preventDefault();
                }
            }
        };
        container.addEventListener('keydown', handleTabKey);
        firstElement?.focus();
        return () => {
            container.removeEventListener('keydown', handleTabKey);
        };
    }, [isActive]);
    return containerRef;
};
exports.useFocusTrap = useFocusTrap;
// Hook for outside click detection
const useOutsideClick = (callback) => {
    const ref = (0, react_1.useRef)(null);
    (0, react_1.useEffect)(() => {
        const handleClick = (event) => {
            if (ref.current && !ref.current.contains(event.target)) {
                callback();
            }
        };
        document.addEventListener('mousedown', handleClick);
        return () => document.removeEventListener('mousedown', handleClick);
    }, [callback]);
    return ref;
};
exports.useOutsideClick = useOutsideClick;
// Hook for scroll position
const useScrollPosition = () => {
    const [scrollPosition, setScrollPosition] = (0, react_1.useState)({ x: 0, y: 0 });
    (0, react_1.useEffect)(() => {
        const handleScroll = () => {
            setScrollPosition({
                x: window.scrollX,
                y: window.scrollY,
            });
        };
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);
    return scrollPosition;
};
exports.useScrollPosition = useScrollPosition;


/***/ }),

/***/ "./src/renderer/index.tsx":
/*!********************************!*\
  !*** ./src/renderer/index.tsx ***!
  \********************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
const react_1 = __importDefault(__webpack_require__(/*! react */ "./node_modules/react/index.js"));
const client_1 = __webpack_require__(/*! react-dom/client */ "./node_modules/react-dom/client.js");
const App_1 = __importDefault(__webpack_require__(/*! ./App */ "./src/renderer/App.tsx"));
__webpack_require__(/*! ./styles/globals.css */ "./src/renderer/styles/globals.css");
// Error boundary for development
class ErrorBoundary extends react_1.default.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }
    componentDidCatch(error, errorInfo) {
        console.error('React Error Boundary caught an error:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            return ((0, jsx_runtime_1.jsx)("div", { className: "min-h-screen bg-gray-900 flex items-center justify-center p-4", children: (0, jsx_runtime_1.jsxs)("div", { className: "bg-gray-800 border border-gray-700 rounded-lg p-8 max-w-lg w-full", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center space-x-3 mb-4", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-8 h-8 bg-danger-600 rounded-full flex items-center justify-center", children: (0, jsx_runtime_1.jsx)("svg", { className: "w-4 h-4 text-white", fill: "currentColor", viewBox: "0 0 20 20", children: (0, jsx_runtime_1.jsx)("path", { fillRule: "evenodd", d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z", clipRule: "evenodd" }) }) }), (0, jsx_runtime_1.jsx)("h2", { className: "text-xl font-semibold text-gray-100", children: "Application Error" })] }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-300 mb-4", children: "Something went wrong while loading the application. This is likely a development issue." }), this.state.error && ((0, jsx_runtime_1.jsxs)("details", { className: "bg-gray-900 border border-gray-600 rounded p-3 mb-4", children: [(0, jsx_runtime_1.jsx)("summary", { className: "text-sm font-medium text-gray-200 cursor-pointer", children: "Error Details" }), (0, jsx_runtime_1.jsxs)("pre", { className: "text-xs text-gray-400 mt-2 overflow-x-auto", children: [this.state.error.message, '\n\n', this.state.error.stack] })] })), (0, jsx_runtime_1.jsxs)("div", { className: "flex space-x-3", children: [(0, jsx_runtime_1.jsx)("button", { onClick: () => window.location.reload(), className: "btn btn-primary btn-sm", children: "Reload App" }), (0, jsx_runtime_1.jsx)("button", { onClick: () => this.setState({ hasError: false, error: undefined }), className: "btn btn-secondary btn-sm", children: "Try Again" })] })] }) }));
        }
        return this.props.children;
    }
}
// Initialize the React app
const container = document.getElementById('root');
if (!container) {
    throw new Error('Root element not found');
}
const root = (0, client_1.createRoot)(container);
root.render((0, jsx_runtime_1.jsx)(react_1.default.StrictMode, { children: (0, jsx_runtime_1.jsx)(ErrorBoundary, { children: (0, jsx_runtime_1.jsx)(App_1.default, {}) }) }));


/***/ }),

/***/ "./src/renderer/store/appStore.ts":
/*!****************************************!*\
  !*** ./src/renderer/store/appStore.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.subscribeToSettingsChanges = exports.subscribeToConversationChanges = exports.subscribeToThemeChanges = exports.useModel = exports.useProvider = exports.useIsYoloMode = exports.useExecutionMode = exports.useHasActiveConversation = exports.useCurrentMessages = exports.useAppStore = void 0;
const zustand_1 = __webpack_require__(/*! zustand */ "./node_modules/zustand/esm/index.js");
const middleware_1 = __webpack_require__(/*! zustand/middleware */ "./node_modules/zustand/esm/middleware.js");
const initialState = {
    sidebarOpen: true,
    settingsOpen: false,
    currentView: 'chat',
    theme: 'system',
    loading: false,
    settings: null,
    conversations: [],
    currentConversationId: null,
    currentConversation: null,
    agentState: null,
    isProcessing: false,
    currentPlan: null,
    toolResults: [],
    systemInfo: null,
    error: null,
    streamingMessage: '',
    isStreaming: false,
};
exports.useAppStore = (0, zustand_1.create)()((0, middleware_1.subscribeWithSelector)((set, get) => ({
    ...initialState,
    // UI Actions
    setSidebarOpen: (open) => set({ sidebarOpen: open }),
    setSettingsOpen: (open) => set({ settingsOpen: open }),
    setCurrentView: (view) => set({ currentView: view }),
    setTheme: (theme) => set({ theme }),
    setLoading: (loading) => set({ loading }),
    // Settings Actions
    setSettings: (settings) => set({ settings }),
    updateSettings: (updates) => {
        const currentSettings = get().settings;
        if (currentSettings) {
            set({ settings: { ...currentSettings, ...updates } });
        }
    },
    // Conversation Actions
    setConversations: (conversations) => set({ conversations }),
    addConversation: (conversation) => {
        const conversations = get().conversations;
        set({
            conversations: [conversation, ...conversations],
            currentConversationId: conversation.id,
            currentConversation: conversation,
        });
    },
    updateConversation: (id, updates) => {
        const conversations = get().conversations;
        const updatedConversations = conversations.map(conv => conv.id === id ? { ...conv, ...updates } : conv);
        set({ conversations: updatedConversations });
        // Update current conversation if it's the one being updated
        if (get().currentConversationId === id) {
            const updatedCurrent = updatedConversations.find(conv => conv.id === id);
            set({ currentConversation: updatedCurrent || null });
        }
    },
    deleteConversation: (id) => {
        const conversations = get().conversations;
        const filteredConversations = conversations.filter(conv => conv.id !== id);
        set({ conversations: filteredConversations });
        // Clear current conversation if it was deleted
        if (get().currentConversationId === id) {
            const newCurrent = filteredConversations[0] || null;
            set({
                currentConversationId: newCurrent?.id || null,
                currentConversation: newCurrent,
            });
        }
    },
    setCurrentConversation: (id) => {
        if (id === null) {
            set({ currentConversationId: null, currentConversation: null });
            return;
        }
        const conversation = get().conversations.find(conv => conv.id === id);
        set({
            currentConversationId: id,
            currentConversation: conversation || null,
        });
    },
    // Message Actions
    addMessage: (conversationId, message) => {
        const conversations = get().conversations;
        const updatedConversations = conversations.map(conv => {
            if (conv.id === conversationId) {
                return {
                    ...conv,
                    messages: [...conv.messages, message],
                    updatedAt: new Date(),
                };
            }
            return conv;
        });
        set({ conversations: updatedConversations });
        // Update current conversation if it's the active one
        if (get().currentConversationId === conversationId) {
            const updatedCurrent = updatedConversations.find(conv => conv.id === conversationId);
            set({ currentConversation: updatedCurrent || null });
        }
    },
    updateMessage: (conversationId, messageId, updates) => {
        const conversations = get().conversations;
        const updatedConversations = conversations.map(conv => {
            if (conv.id === conversationId) {
                const updatedMessages = conv.messages.map(msg => msg.id === messageId ? { ...msg, ...updates } : msg);
                return { ...conv, messages: updatedMessages };
            }
            return conv;
        });
        set({ conversations: updatedConversations });
        // Update current conversation if it's the active one
        if (get().currentConversationId === conversationId) {
            const updatedCurrent = updatedConversations.find(conv => conv.id === conversationId);
            set({ currentConversation: updatedCurrent || null });
        }
    },
    // Agent Actions
    setAgentState: (state) => set({ agentState: state }),
    setProcessing: (processing) => set({ isProcessing: processing }),
    setCurrentPlan: (plan) => set({ currentPlan: plan }),
    addToolResult: (result) => {
        const toolResults = get().toolResults;
        set({ toolResults: [...toolResults, result] });
    },
    clearToolResults: () => set({ toolResults: [] }),
    confirmPlan: (planId) => {
        // TODO: Implement plan confirmation logic
        console.log('Plan confirmed:', planId);
        set({ currentPlan: null, agentState: { ...get().agentState, isWaitingForConfirmation: false, status: 'idle' } });
    },
    rejectPlan: (planId) => {
        // TODO: Implement plan rejection logic
        console.log('Plan rejected:', planId);
        set({ currentPlan: null, agentState: { ...get().agentState, isWaitingForConfirmation: false, status: 'idle' } });
    },
    stopGeneration: () => {
        // TODO: Implement stop generation logic
        console.log('Generation stopped');
        set({ isStreaming: false, streamingMessage: '' });
    },
    // System Actions
    setSystemInfo: (info) => set({ systemInfo: info }),
    // Error Actions
    setError: (error) => set({ error }),
    clearError: () => set({ error: null }),
    // Streaming Actions
    setStreamingMessage: (message) => set({ streamingMessage: message }),
    appendStreamingMessage: (chunk) => {
        const current = get().streamingMessage;
        set({ streamingMessage: current + chunk });
    },
    setStreaming: (streaming) => set({ isStreaming: streaming }),
    clearStreamingMessage: () => set({ streamingMessage: '' }),
    // Composite Actions
    startNewConversation: (title) => {
        const newConversation = {
            id: Date.now().toString(),
            title,
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        get().addConversation(newConversation);
    },
    sendMessage: (conversationId, content) => {
        const message = {
            id: Date.now().toString(),
            role: 'user',
            content,
            timestamp: new Date(),
        };
        get().addMessage(conversationId, message);
        // TODO: Implement actual message sending to agent
        console.log('Message sent:', content);
    },
    reset: () => set(initialState),
})));
// Selectors for computed values
const useCurrentMessages = () => (0, exports.useAppStore)(state => state.currentConversation?.messages || []);
exports.useCurrentMessages = useCurrentMessages;
const useHasActiveConversation = () => (0, exports.useAppStore)(state => !!state.currentConversation);
exports.useHasActiveConversation = useHasActiveConversation;
const useExecutionMode = () => (0, exports.useAppStore)(state => state.settings?.agent.defaultExecutionMode || 'confirm');
exports.useExecutionMode = useExecutionMode;
const useIsYoloMode = () => (0, exports.useAppStore)(state => state.settings?.agent.defaultExecutionMode === 'yolo');
exports.useIsYoloMode = useIsYoloMode;
const useProvider = () => (0, exports.useAppStore)(state => state.settings?.llm.provider || 'openai');
exports.useProvider = useProvider;
const useModel = () => (0, exports.useAppStore)(state => state.settings?.llm.model || 'gpt-4');
exports.useModel = useModel;
// Subscriptions for side effects
const subscribeToThemeChanges = (callback) => exports.useAppStore.subscribe((state) => state.theme, callback);
exports.subscribeToThemeChanges = subscribeToThemeChanges;
const subscribeToConversationChanges = (callback) => exports.useAppStore.subscribe((state) => state.currentConversation, callback);
exports.subscribeToConversationChanges = subscribeToConversationChanges;
const subscribeToSettingsChanges = (callback) => exports.useAppStore.subscribe((state) => state.settings, callback);
exports.subscribeToSettingsChanges = subscribeToSettingsChanges;


/***/ }),

/***/ "./src/renderer/styles/globals.css":
/*!*****************************************!*\
  !*** ./src/renderer/styles/globals.css ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "./node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "./node_modules/style-loader/dist/runtime/insertBySelector.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "./node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "./node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_globals_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/dist/cjs.js!./globals.css */ "./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./src/renderer/styles/globals.css");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());

      options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
    
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_globals_css__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_globals_css__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_globals_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_globals_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


/***/ }),

/***/ "./src/shared/constants.ts":
/*!*********************************!*\
  !*** ./src/shared/constants.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.COLOR_THEMES = exports.REGEX_PATTERNS = exports.PROD_CONSTANTS = exports.DEV_CONSTANTS = exports.FEATURE_FLAGS = exports.VALIDATION_RULES = exports.SUCCESS_MESSAGES = exports.ERROR_MESSAGES = exports.DEFAULT_SETTINGS = exports.FILE_TYPES = exports.TOOL_CATEGORIES = exports.EXECUTION_MODES = exports.KEYBOARD_SHORTCUTS = exports.UI_CONSTANTS = exports.TOKEN_LIMITS = exports.API_ENDPOINTS = exports.DEFAULT_MODELS = exports.LLM_PROVIDERS = exports.DB_CONSTANTS = exports.APP_DESCRIPTION = exports.APP_VERSION = exports.APP_NAME = void 0;
// Application constants
exports.APP_NAME = 'AI Assistant';
exports.APP_VERSION = '1.0.0';
exports.APP_DESCRIPTION = 'Desktop AI Assistant with Multi-LLM Support';
// Database constants
exports.DB_CONSTANTS = {
    DATABASE_NAME: 'ai-assistant.db',
    BACKUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    CLEANUP_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    MAX_CONVERSATION_AGE: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
    MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB in bytes
    VACUUM_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
};
// LLM Provider constants
exports.LLM_PROVIDERS = {
    OPENAI: 'openai',
    ANTHROPIC: 'anthropic',
    DEEPSEEK: 'deepseek',
};
// Default models for each provider
exports.DEFAULT_MODELS = {
    [exports.LLM_PROVIDERS.OPENAI]: {
        'gpt-4': 'GPT-4',
        'gpt-4-turbo': 'GPT-4 Turbo',
        'gpt-3.5-turbo': 'GPT-3.5 Turbo',
    },
    [exports.LLM_PROVIDERS.ANTHROPIC]: {
        'claude-3-opus-20240229': 'Claude 3 Opus',
        'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
        'claude-3-haiku-20240307': 'Claude 3 Haiku',
    },
    [exports.LLM_PROVIDERS.DEEPSEEK]: {
        'deepseek-chat': 'DeepSeek Chat',
        'deepseek-coder': 'DeepSeek Coder',
    },
};
// API endpoints
exports.API_ENDPOINTS = {
    [exports.LLM_PROVIDERS.OPENAI]: 'https://api.openai.com/v1',
    [exports.LLM_PROVIDERS.ANTHROPIC]: 'https://api.anthropic.com/v1',
    [exports.LLM_PROVIDERS.DEEPSEEK]: 'https://api.deepseek.com/v1',
};
// Token limits for different models
exports.TOKEN_LIMITS = {
    'gpt-4': 8192,
    'gpt-4-turbo': 128000,
    'gpt-3.5-turbo': 4096,
    'claude-3-opus-20240229': 200000,
    'claude-3-sonnet-20240229': 200000,
    'claude-3-haiku-20240307': 200000,
    'deepseek-chat': 32768,
    'deepseek-coder': 32768,
};
// UI constants
exports.UI_CONSTANTS = {
    SIDEBAR_WIDTH: 280,
    SIDEBAR_COLLAPSED_WIDTH: 60,
    HEADER_HEIGHT: 60,
    MESSAGE_MAX_WIDTH: 800,
    ANIMATION_DURATION: 200,
    DEBOUNCE_DELAY: 300,
    TOAST_DURATION: 5000,
    TYPING_INDICATOR_DELAY: 1000,
};
// Keyboard shortcuts
exports.KEYBOARD_SHORTCUTS = {
    NEW_CONVERSATION: 'Ctrl+N',
    TOGGLE_SIDEBAR: 'Ctrl+B',
    TOGGLE_SETTINGS: 'Ctrl+,',
    FOCUS_INPUT: 'Ctrl+Enter',
    CLOSE_MODAL: 'Escape',
    TOGGLE_FULLSCREEN: 'F11',
    TOGGLE_DEVTOOLS: 'F12',
    RELOAD_APP: 'Ctrl+Shift+R',
};
// Agent execution modes
exports.EXECUTION_MODES = {
    CONFIRM: 'confirm',
    YOLO: 'yolo',
};
// Tool categories
exports.TOOL_CATEGORIES = {
    FILE_SYSTEM: 'file_system',
    SHELL: 'shell',
    SEARCH: 'search',
    NETWORK: 'network',
    SYSTEM: 'system',
};
// File type mappings
exports.FILE_TYPES = {
    TEXT: ['txt', 'md', 'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'],
    CODE: ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'swift'],
    WEB: ['html', 'css', 'scss', 'sass', 'less'],
    DATA: ['csv', 'tsv', 'json', 'xml', 'sql'],
    IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
    DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
    ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],
};
// Default application settings
exports.DEFAULT_SETTINGS = {
    llm: {
        provider: 'openai',
        model: 'gpt-4',
        apiKey: '',
        temperature: 0.7,
        maxTokens: 2048,
    },
    ui: {
        theme: 'system',
        fontSize: 'medium',
        compactMode: false,
    },
    agent: {
        defaultExecutionMode: 'confirm',
        autoSaveConversations: true,
        maxContextLength: 32000,
    },
    tools: {
        enabledTools: [
            'run_shell_command',
            'read_file',
            'write_file',
            'list_directory',
            'glob_files',
            'grep',
            'replace_in_file',
        ],
        toolSettings: {},
    },
};
// Error messages
exports.ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
    API_KEY_MISSING: 'API key is required for the selected provider.',
    API_KEY_INVALID: 'Invalid API key. Please check your credentials.',
    RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
    MODEL_NOT_AVAILABLE: 'Selected model is not available.',
    TOOL_EXECUTION_FAILED: 'Tool execution failed. Please try again.',
    FILE_NOT_FOUND: 'File not found.',
    PERMISSION_DENIED: 'Permission denied.',
    INVALID_INPUT: 'Invalid input provided.',
    UNKNOWN_ERROR: 'An unknown error occurred.',
};
// Success messages
exports.SUCCESS_MESSAGES = {
    SETTINGS_SAVED: 'Settings saved successfully.',
    CONVERSATION_CREATED: 'New conversation created.',
    CONVERSATION_DELETED: 'Conversation deleted.',
    FILE_SAVED: 'File saved successfully.',
    TOOL_EXECUTED: 'Tool executed successfully.',
    API_KEY_VALIDATED: 'API key validated successfully.',
};
// Validation rules
exports.VALIDATION_RULES = {
    API_KEY_MIN_LENGTH: 10,
    CONVERSATION_TITLE_MAX_LENGTH: 100,
    MESSAGE_MAX_LENGTH: 10000,
    FILENAME_MAX_LENGTH: 255,
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_CONCURRENT_REQUESTS: 5,
};
// Feature flags
exports.FEATURE_FLAGS = {
    ENABLE_VOICE_INPUT: false,
    ENABLE_VOICE_OUTPUT: false,
    ENABLE_PLUGINS: false,
    ENABLE_CUSTOM_TOOLS: false,
    ENABLE_COLLABORATION: false,
    ENABLE_CLOUD_SYNC: false,
};
// Development constants
exports.DEV_CONSTANTS = {
    MOCK_DELAY: 1000,
    DEBUG_LOGGING: true,
    HOT_RELOAD: true,
    DEVTOOLS_ENABLED: true,
};
// Production constants
exports.PROD_CONSTANTS = {
    TELEMETRY_ENDPOINT: 'https://telemetry.example.com',
    UPDATE_CHECK_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
    CRASH_REPORT_ENDPOINT: 'https://crashes.example.com',
    ANALYTICS_ENDPOINT: 'https://analytics.example.com',
};
// Regular expressions
exports.REGEX_PATTERNS = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    URL: /^https?:\/\/.+/,
    API_KEY: /^[a-zA-Z0-9_-]+$/,
    FILENAME: /^[^<>:"/\\|?*]+$/,
    VERSION: /^\d+\.\d+\.\d+$/,
};
// Color themes
exports.COLOR_THEMES = {
    DARK: {
        primary: '#0ea5e9',
        secondary: '#64748b',
        background: '#0f172a',
        surface: '#1e293b',
        text: '#f8fafc',
        textSecondary: '#cbd5e1',
        border: '#334155',
        success: '#22c55e',
        warning: '#f59e0b',
        error: '#ef4444',
    },
    LIGHT: {
        primary: '#0ea5e9',
        secondary: '#64748b',
        background: '#ffffff',
        surface: '#f8fafc',
        text: '#0f172a',
        textSecondary: '#475569',
        border: '#e2e8f0',
        success: '#22c55e',
        warning: '#f59e0b',
        error: '#ef4444',
    },
};


/***/ }),

/***/ "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e":
/*!***************************************************************************************************************************************************************************!*\
  !*** data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e ***!
  \***************************************************************************************************************************************************************************/
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e";

/***/ }),

/***/ "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e";

/***/ }),

/***/ "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e";

/***/ }),

/***/ "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%2371717a%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%2371717a%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module) => {

module.exports = "data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%2371717a%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e";

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/create fake namespace object */
/******/ 	(() => {
/******/ 		var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);
/******/ 		var leafPrototypes;
/******/ 		// create a fake namespace object
/******/ 		// mode & 1: value is a module id, require it
/******/ 		// mode & 2: merge all properties of value into the ns
/******/ 		// mode & 4: return value when already ns object
/******/ 		// mode & 16: return value when it's Promise-like
/******/ 		// mode & 8|1: behave like require
/******/ 		__webpack_require__.t = function(value, mode) {
/******/ 			if(mode & 1) value = this(value);
/******/ 			if(mode & 8) return value;
/******/ 			if(typeof value === 'object' && value) {
/******/ 				if((mode & 4) && value.__esModule) return value;
/******/ 				if((mode & 16) && typeof value.then === 'function') return value;
/******/ 			}
/******/ 			var ns = Object.create(null);
/******/ 			__webpack_require__.r(ns);
/******/ 			var def = {};
/******/ 			leafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];
/******/ 			for(var current = mode & 2 && value; (typeof current == 'object' || typeof current == 'function') && !~leafPrototypes.indexOf(current); current = getProto(current)) {
/******/ 				Object.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));
/******/ 			}
/******/ 			def['default'] = () => (value);
/******/ 			__webpack_require__.d(ns, def);
/******/ 			return ns;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	(() => {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = (chunkId) => {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks
/******/ 		__webpack_require__.u = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "" + chunkId + ".bundle.js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/load script */
/******/ 	(() => {
/******/ 		var inProgress = {};
/******/ 		var dataWebpackPrefix = "ai-assistant-desktop:";
/******/ 		// loadScript function to load a script via script tag
/******/ 		__webpack_require__.l = (url, done, key, chunkId) => {
/******/ 			if(inProgress[url]) { inProgress[url].push(done); return; }
/******/ 			var script, needAttach;
/******/ 			if(key !== undefined) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				for(var i = 0; i < scripts.length; i++) {
/******/ 					var s = scripts[i];
/******/ 					if(s.getAttribute("src") == url || s.getAttribute("data-webpack") == dataWebpackPrefix + key) { script = s; break; }
/******/ 				}
/******/ 			}
/******/ 			if(!script) {
/******/ 				needAttach = true;
/******/ 				script = document.createElement('script');
/******/ 		
/******/ 				script.charset = 'utf-8';
/******/ 				script.timeout = 120;
/******/ 				if (__webpack_require__.nc) {
/******/ 					script.setAttribute("nonce", __webpack_require__.nc);
/******/ 				}
/******/ 				script.setAttribute("data-webpack", dataWebpackPrefix + key);
/******/ 		
/******/ 				script.src = url;
/******/ 			}
/******/ 			inProgress[url] = [done];
/******/ 			var onScriptComplete = (prev, event) => {
/******/ 				// avoid mem leaks in IE.
/******/ 				script.onerror = script.onload = null;
/******/ 				clearTimeout(timeout);
/******/ 				var doneFns = inProgress[url];
/******/ 				delete inProgress[url];
/******/ 				script.parentNode && script.parentNode.removeChild(script);
/******/ 				doneFns && doneFns.forEach((fn) => (fn(event)));
/******/ 				if(prev) return prev(event);
/******/ 			}
/******/ 			var timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);
/******/ 			script.onerror = onScriptComplete.bind(null, script.onerror);
/******/ 			script.onload = onScriptComplete.bind(null, script.onload);
/******/ 			needAttach && document.head.appendChild(script);
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.nmd = (module) => {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	(() => {
/******/ 		var scriptUrl;
/******/ 		if (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + "";
/******/ 		var document = __webpack_require__.g.document;
/******/ 		if (!scriptUrl && document) {
/******/ 			if (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')
/******/ 				scriptUrl = document.currentScript.src;
/******/ 			if (!scriptUrl) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				if(scripts.length) {
/******/ 					var i = scripts.length - 1;
/******/ 					while (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;
/******/ 				}
/******/ 			}
/******/ 		}
/******/ 		// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration
/******/ 		// or pass an empty string ("") and set the __webpack_public_path__ variable from your code to use your own logic.
/******/ 		if (!scriptUrl) throw new Error("Automatic publicPath is not supported in this browser");
/******/ 		scriptUrl = scriptUrl.replace(/^blob:/, "").replace(/#.*$/, "").replace(/\?.*$/, "").replace(/\/[^\/]+$/, "/");
/******/ 		__webpack_require__.p = scriptUrl;
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		__webpack_require__.b = document.baseURI || self.location.href;
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"main": 0
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.f.j = (chunkId, promises) => {
/******/ 				// JSONP chunk loading for javascript
/******/ 				var installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;
/******/ 				if(installedChunkData !== 0) { // 0 means "already installed".
/******/ 		
/******/ 					// a Promise means "currently loading".
/******/ 					if(installedChunkData) {
/******/ 						promises.push(installedChunkData[2]);
/******/ 					} else {
/******/ 						if(true) { // all chunks have JS
/******/ 							// setup Promise in chunk cache
/******/ 							var promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));
/******/ 							promises.push(installedChunkData[2] = promise);
/******/ 		
/******/ 							// start chunk loading
/******/ 							var url = __webpack_require__.p + __webpack_require__.u(chunkId);
/******/ 							// create error before stack unwound to get useful stacktrace later
/******/ 							var error = new Error();
/******/ 							var loadingEnded = (event) => {
/******/ 								if(__webpack_require__.o(installedChunks, chunkId)) {
/******/ 									installedChunkData = installedChunks[chunkId];
/******/ 									if(installedChunkData !== 0) installedChunks[chunkId] = undefined;
/******/ 									if(installedChunkData) {
/******/ 										var errorType = event && (event.type === 'load' ? 'missing' : event.type);
/******/ 										var realSrc = event && event.target && event.target.src;
/******/ 										error.message = 'Loading chunk ' + chunkId + ' failed.\n(' + errorType + ': ' + realSrc + ')';
/******/ 										error.name = 'ChunkLoadError';
/******/ 										error.type = errorType;
/******/ 										error.request = realSrc;
/******/ 										installedChunkData[1](error);
/******/ 									}
/******/ 								}
/******/ 							};
/******/ 							__webpack_require__.l(url, loadingEnded, "chunk-" + chunkId, chunkId);
/******/ 						}
/******/ 					}
/******/ 				}
/******/ 		};
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = global["webpackChunkai_assistant_desktop"] = global["webpackChunkai_assistant_desktop"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	(() => {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_getU-107cdf"], () => (__webpack_require__("./src/renderer/index.tsx")))
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	
/******/ })()
;
//# sourceMappingURL=main.bundle.js.map