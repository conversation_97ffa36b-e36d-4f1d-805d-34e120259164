import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { 
  AppSettings, 
  Conversation, 
  Message, 
  AgentState,
  SystemInfo,
  ToolResult,
  AgentPlan
} from '@shared/types';

export interface AppState {
  // UI State
  sidebarOpen: boolean;
  settingsOpen: boolean;
  currentView: 'chat' | 'settings' | 'logs';
  theme: 'light' | 'dark' | 'system';
  loading: boolean;
  
  // Settings
  settings: AppSettings | null;
  
  // Conversations
  conversations: Conversation[];
  currentConversationId: string | null;
  currentConversation: Conversation | null;
  
  // Agent State
  agentState: AgentState | null;
  isProcessing: boolean;
  currentPlan: AgentPlan | null;
  toolResults: ToolResult[];
  
  // System Info
  systemInfo: SystemInfo | null;
  
  // Error State
  error: string | null;
  
  // Streaming State
  streamingMessage: string;
  isStreaming: boolean;
}

export interface AppActions {
  // UI Actions
  setSidebarOpen: (open: boolean) => void;
  setSettingsOpen: (open: boolean) => void;
  setCurrentView: (view: AppState['currentView']) => void;
  setTheme: (theme: AppState['theme']) => void;
  setLoading: (loading: boolean) => void;
  
  // Settings Actions
  setSettings: (settings: AppSettings) => void;
  updateSettings: (updates: Partial<AppSettings>) => void;
  
  // Conversation Actions
  setConversations: (conversations: Conversation[]) => void;
  addConversation: (conversation: Conversation) => void;
  updateConversation: (id: string, updates: Partial<Conversation>) => void;
  deleteConversation: (id: string) => void;
  setCurrentConversation: (id: string | null) => void;
  
  // Message Actions
  addMessage: (conversationId: string, message: Message) => void;
  updateMessage: (conversationId: string, messageId: string, updates: Partial<Message>) => void;
  
  // Agent Actions
  setAgentState: (state: AgentState) => void;
  setProcessing: (processing: boolean) => void;
  setCurrentPlan: (plan: AgentPlan | null) => void;
  addToolResult: (result: ToolResult) => void;
  clearToolResults: () => void;
  confirmPlan: (planId: string) => void;
  rejectPlan: (planId: string) => void;
  stopGeneration: () => void;
  
  // System Actions
  setSystemInfo: (info: SystemInfo) => void;
  
  // Error Actions
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Streaming Actions
  setStreamingMessage: (message: string) => void;
  appendStreamingMessage: (chunk: string) => void;
  setStreaming: (streaming: boolean) => void;
  clearStreamingMessage: () => void;
  
  // Composite Actions
  startNewConversation: (title: string) => void;
  sendMessage: (conversationId: string, content: string) => void;
  reset: () => void;
}

const initialState: AppState = {
  sidebarOpen: true,
  settingsOpen: false,
  currentView: 'chat',
  theme: 'system',
  loading: false,
  
  settings: null,
  
  conversations: [],
  currentConversationId: null,
  currentConversation: null,
  
  agentState: null,
  isProcessing: false,
  currentPlan: null,
  toolResults: [],
  
  systemInfo: null,
  
  error: null,
  
  streamingMessage: '',
  isStreaming: false,
};

export const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    
    // UI Actions
    setSidebarOpen: (open) => set({ sidebarOpen: open }),
    setSettingsOpen: (open) => set({ settingsOpen: open }),
    setCurrentView: (view) => set({ currentView: view }),
    setTheme: (theme) => set({ theme }),
    setLoading: (loading) => set({ loading }),
    
    // Settings Actions
    setSettings: (settings) => set({ settings }),
    updateSettings: (updates) => {
      const currentSettings = get().settings;
      if (currentSettings) {
        set({ settings: { ...currentSettings, ...updates } });
      }
    },
    
    // Conversation Actions
    setConversations: (conversations) => set({ conversations }),
    addConversation: (conversation) => {
      const conversations = get().conversations;
      set({ 
        conversations: [conversation, ...conversations],
        currentConversationId: conversation.id,
        currentConversation: conversation,
      });
    },
    updateConversation: (id, updates) => {
      const conversations = get().conversations;
      const updatedConversations = conversations.map(conv =>
        conv.id === id ? { ...conv, ...updates } : conv
      );
      set({ conversations: updatedConversations });
      
      // Update current conversation if it's the one being updated
      if (get().currentConversationId === id) {
        const updatedCurrent = updatedConversations.find(conv => conv.id === id);
        set({ currentConversation: updatedCurrent || null });
      }
    },
    deleteConversation: (id) => {
      const conversations = get().conversations;
      const filteredConversations = conversations.filter(conv => conv.id !== id);
      set({ conversations: filteredConversations });
      
      // Clear current conversation if it was deleted
      if (get().currentConversationId === id) {
        const newCurrent = filteredConversations[0] || null;
        set({ 
          currentConversationId: newCurrent?.id || null,
          currentConversation: newCurrent,
        });
      }
    },
    setCurrentConversation: (id) => {
      if (id === null) {
        set({ currentConversationId: null, currentConversation: null });
        return;
      }
      
      const conversation = get().conversations.find(conv => conv.id === id);
      set({ 
        currentConversationId: id,
        currentConversation: conversation || null,
      });
    },
    
    // Message Actions
    addMessage: (conversationId, message) => {
      const conversations = get().conversations;
      const updatedConversations = conversations.map(conv => {
        if (conv.id === conversationId) {
          return {
            ...conv,
            messages: [...conv.messages, message],
            updatedAt: new Date(),
          };
        }
        return conv;
      });
      
      set({ conversations: updatedConversations });
      
      // Update current conversation if it's the active one
      if (get().currentConversationId === conversationId) {
        const updatedCurrent = updatedConversations.find(conv => conv.id === conversationId);
        set({ currentConversation: updatedCurrent || null });
      }
    },
    updateMessage: (conversationId, messageId, updates) => {
      const conversations = get().conversations;
      const updatedConversations = conversations.map(conv => {
        if (conv.id === conversationId) {
          const updatedMessages = conv.messages.map(msg =>
            msg.id === messageId ? { ...msg, ...updates } : msg
          );
          return { ...conv, messages: updatedMessages };
        }
        return conv;
      });
      
      set({ conversations: updatedConversations });
      
      // Update current conversation if it's the active one
      if (get().currentConversationId === conversationId) {
        const updatedCurrent = updatedConversations.find(conv => conv.id === conversationId);
        set({ currentConversation: updatedCurrent || null });
      }
    },
    
    // Agent Actions
    setAgentState: (state) => set({ agentState: state }),
    setProcessing: (processing) => set({ isProcessing: processing }),
    setCurrentPlan: (plan) => set({ currentPlan: plan }),
    addToolResult: (result) => {
      const toolResults = get().toolResults;
      set({ toolResults: [...toolResults, result] });
    },
    clearToolResults: () => set({ toolResults: [] }),
    confirmPlan: (planId) => {
      // TODO: Implement plan confirmation logic
      console.log('Plan confirmed:', planId);
      set({ currentPlan: null, agentState: { ...get().agentState, isWaitingForConfirmation: false, status: 'idle' } });
    },
    rejectPlan: (planId) => {
      // TODO: Implement plan rejection logic
      console.log('Plan rejected:', planId);
      set({ currentPlan: null, agentState: { ...get().agentState, isWaitingForConfirmation: false, status: 'idle' } });
    },
    stopGeneration: () => {
      // TODO: Implement stop generation logic
      console.log('Generation stopped');
      set({ isStreaming: false, streamingMessage: '' });
    },
    
    // System Actions
    setSystemInfo: (info) => set({ systemInfo: info }),
    
    // Error Actions
    setError: (error) => set({ error }),
    clearError: () => set({ error: null }),
    
    // Streaming Actions
    setStreamingMessage: (message) => set({ streamingMessage: message }),
    appendStreamingMessage: (chunk) => {
      const current = get().streamingMessage;
      set({ streamingMessage: current + chunk });
    },
    setStreaming: (streaming) => set({ isStreaming: streaming }),
    clearStreamingMessage: () => set({ streamingMessage: '' }),
    
    // Composite Actions
    startNewConversation: (title) => {
      const newConversation: Conversation = {
        id: Date.now().toString(),
        title,
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      get().addConversation(newConversation);
    },
    sendMessage: (conversationId, content) => {
      const message: Message = {
        id: Date.now().toString(),
        role: 'user',
        content,
        timestamp: new Date(),
      };
      
      get().addMessage(conversationId, message);
      
      // TODO: Implement actual message sending to agent
      console.log('Message sent:', content);
    },
    reset: () => set(initialState),
  }))
);

// Selectors for computed values
export const useCurrentMessages = () => 
  useAppStore(state => state.currentConversation?.messages || []);

export const useHasActiveConversation = () => 
  useAppStore(state => !!state.currentConversation);

export const useExecutionMode = () => 
  useAppStore(state => state.settings?.agent.defaultExecutionMode || 'confirm');

export const useIsYoloMode = () => 
  useAppStore(state => state.settings?.agent.defaultExecutionMode === 'yolo');

export const useProvider = () => 
  useAppStore(state => state.settings?.llm.provider || 'openai');

export const useModel = () => 
  useAppStore(state => state.settings?.llm.model || 'gpt-4');

// Subscriptions for side effects
export const subscribeToThemeChanges = (callback: (theme: string) => void) =>
  useAppStore.subscribe(
    (state) => state.theme,
    callback
  );

export const subscribeToConversationChanges = (callback: (conversation: Conversation | null) => void) =>
  useAppStore.subscribe(
    (state) => state.currentConversation,
    callback
  );

export const subscribeToSettingsChanges = (callback: (settings: AppSettings | null) => void) =>
  useAppStore.subscribe(
    (state) => state.settings,
    callback
  );