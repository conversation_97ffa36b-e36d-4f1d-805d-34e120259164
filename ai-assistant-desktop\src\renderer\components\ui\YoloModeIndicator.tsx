import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Zap, Shield, Settings } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '@renderer/store/appStore';
import { useSettings } from '@renderer/hooks/useElectronAPI';
import ConfirmationModal from '@renderer/components/modals/ConfirmationModal';

const YoloModeIndicator: React.FC = () => {
  const { settings } = useAppStore();
  const { updateSettings } = useSettings();
  const [showModeSwitch, setShowModeSwitch] = useState(false);
  const [showWarning, setShowWarning] = useState(false);

  const isYoloMode = settings?.agent?.defaultExecutionMode === 'yolo';

  const toggleExecutionMode = async () => {
    if (!settings) return;

    const newMode = isYoloMode ? 'confirm' : 'yolo';
    
    if (newMode === 'yolo') {
      setShowWarning(true);
    } else {
      await updateSettings({
        ...settings,
        agent: {
          ...settings.agent,
          defaultExecutionMode: 'confirm',
        },
      });
    }
  };

  const confirmYoloMode = async () => {
    if (!settings) return;

    await updateSettings({
      ...settings,
      agent: {
        ...settings.agent,
        defaultExecutionMode: 'yolo',
      },
    });
    setShowWarning(false);
  };

  if (!isYoloMode) {
    return (
      <>
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleExecutionMode}
            className="flex items-center space-x-2 px-3 py-1 bg-green-900 bg-opacity-30 border border-green-500 rounded-lg text-green-300 hover:bg-green-800 hover:bg-opacity-40 transition-colors"
            title="Switch to YOLO mode"
          >
            <Shield className="w-4 h-4" />
            <span className="text-sm font-medium">Safe Mode</span>
          </button>
        </div>

        <ConfirmationModal
          isOpen={showWarning}
          onClose={() => setShowWarning(false)}
          onConfirm={confirmYoloMode}
          onCancel={() => setShowWarning(false)}
          title="Enable YOLO Mode"
          message="YOLO mode allows the AI agent to execute commands automatically without asking for confirmation. This can be dangerous and may result in unintended changes to your system."
          type="warning"
          dangerous={true}
          confirmText="Enable YOLO Mode"
          cancelText="Keep Safe Mode"
          details={`YOLO mode will:
• Execute shell commands automatically
• Modify files without confirmation
• Install software and dependencies
• Make system configuration changes
• Perform potentially destructive operations

Only enable this if you fully trust the AI agent and understand the risks.`}
          showDetails={true}
        />
      </>
    );
  }

  return (
    <>
      {/* YOLO Mode Active Indicator */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex items-center space-x-2"
      >
        <motion.button
          onClick={toggleExecutionMode}
          className="flex items-center space-x-2 px-3 py-1 bg-red-900 bg-opacity-40 border border-red-500 rounded-lg text-red-300 hover:bg-red-800 hover:bg-opacity-50 transition-colors"
          title="Switch to Safe mode"
          animate={{
            boxShadow: [
              '0 0 0 0 rgba(239, 68, 68, 0.4)',
              '0 0 0 4px rgba(239, 68, 68, 0.1)',
              '0 0 0 0 rgba(239, 68, 68, 0.4)',
            ],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
          }}
        >
          <motion.div
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 0.5, repeat: Infinity, repeatDelay: 3 }}
          >
            <AlertTriangle className="w-4 h-4" />
          </motion.div>
          <span className="text-sm font-bold">YOLO MODE</span>
          <Zap className="w-4 h-4" />
        </motion.button>
      </motion.div>

      {/* Global YOLO Mode Styles */}
      <style>{`
        .yolo-mode {
          position: relative;
        }
        
        .yolo-mode::before {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 3px solid #ef4444;
          pointer-events: none;
          z-index: 9999;
          animation: yolo-pulse 3s infinite;
        }
        
        @keyframes yolo-pulse {
          0%, 100% {
            border-color: rgba(239, 68, 68, 0.3);
            box-shadow: inset 0 0 0 0 rgba(239, 68, 68, 0.1);
          }
          50% {
            border-color: rgba(239, 68, 68, 0.8);
            box-shadow: inset 0 0 0 3px rgba(239, 68, 68, 0.2);
          }
        }
        
        .yolo-mode .title-bar {
          background: linear-gradient(90deg, #1f2937 0%, #7f1d1d 100%);
        }
      `}</style>
    </>
  );
};

export default YoloModeIndicator;
