{"version": 3, "file": "main.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACgH;AACjB;AACO;AACtG,4CAA4C,6jBAA6Q;AACzT,4CAA4C,mkBAAgR;AAC5T,4CAA4C,+YAAsL;AAClO,4CAA4C,qiBAAiQ;AAC7S,8BAA8B,mFAA2B,CAAC,4FAAqC;AAC/F,8GAA8G,IAAI,IAAI,mCAAmC,IAAI,kBAAkB;AAC/K,yCAAyC,sFAA+B;AACxE,yCAAyC,sFAA+B;AACxE,yCAAyC,sFAA+B;AACxE,yCAAyC,sFAA+B;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B;AAC1B,mBAAmB;AACnB,uBAAuB;AACvB,yBAAyB;AACzB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,oBAAoB;AACpB,kCAAkC;AAClC,oBAAoB;AACpB;AACA,kBAAkB;AAClB,6CAA6C;AAC7C,iCAAiC;AACjC,mCAAmC;AACnC,4CAA4C;AAC5C;;AAEA;AACA;AACA;AACA;;AAEA;AACA,aAAa;AACb,wBAAwB;AACxB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa;AACb,kBAAkB;AAClB,yBAAyB;AACzB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oDAAoD;AACpD,iCAAiC;AACjC,mCAAmC;AACnC,kBAAkB;AAClB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB,yBAAyB;AACzB,6BAA6B;AAC7B;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB,kCAAkC;AAClC,oCAAoC;AACpC,mBAAmB;AACnB,wBAAwB;AACxB,wBAAwB;AACxB,2BAA2B;AAC3B,kBAAkB;AAClB,aAAa;AACb,cAAc;AACd;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,iCAAiC;AACjC,0BAA0B;AAC1B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iCAAiC;AACjC,wBAAwB;AACxB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,8BAA8B;AAC9B,iBAAiB;AACjB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,cAAc;AACd,kBAAkB;AAClB;;AAEA;AACA;AACA,cAAc;AACd,kBAAkB;AAClB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,0BAA0B;AAC1B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,mCAAmC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,mCAAmC;AAC7D;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,mCAAmC;AAC7D;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,mCAAm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cAAc;AACd,UAAU;AACV,UAAU;AACV,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,OAAO,kGAAkG,WAAW,WAAW,WAAW,WAAW,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,KAAK,WAAW,YAAY,MAAM,OAAO,qBAAqB,oBAAoB,qBAAqB,qBAAqB,MAAM,MAAM,WAAW,MAAM,YAAY,MAAM,MAAM,qBAAqB,qBAAqB,qBAAqB,UAAU,oBAAoB,qBAAqB,qBAAqB,qBAAqB,qBAAqB,MAAM,OAAO,MAAM,KAAK,oBAAoB,qBAAqB,MAAM,QAAQ,MAAM,KAAK,oBAAoB,oBAAoB,qBAAqB,MAAM,MAAM,MAAM,KAAK,WAAW,WAAW,MAAM,MAAM,MAAM,UAAU,WAAW,WAAW,MAAM,MAAM,MAAM,KAAK,UAAU,WAAW,MAAM,MAAM,MAAM,MAAM,WAAW,MAAM,SAAS,MAAM,QAAQ,qBAAqB,qBAAqB,qBAAqB,oBAAoB,MAAM,MAAM,MAAM,KAAK,UAAU,MAAM,MAAM,MAAM,MAAM,UAAU,UAAU,WAAW,WAAW,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,QAAQ,MAAM,KAAK,oBAAoB,qBAAqB,qBAAqB,MAAM,QAAQ,MAAM,SAAS,qBAAqB,qBAAqB,qBAAqB,oBAAoB,qBAAqB,qBAAqB,qBAAqB,oBAAoB,oBAAoB,oBAAoB,MAAM,MAAM,MAAM,MAAM,WAAW,MAAM,OAAO,MAAM,QAAQ,qBAAqB,qBAAqB,qBAAqB,MAAM,MAAM,MAAM,KAAK,UAAU,MAAM,MAAM,MAAM,KAAK,WAAW,MAAM,MAAM,MAAM,KAAK,WAAW,MAAM,MAAM,MAAM,MAAM,UAAU,MAAM,OAAO,MAAM,KAAK,qBAAqB,qBAAqB,MAAM,MAAM,MAAM,KAAK,WAAW,MAAM,OAAO,MAAM,KAAK,qBAAqB,oBAAoB,MAAM,MAAM,MAAM,KAAK,WAAW,MAAM,MAAM,MAAM,iBAAiB,UAAU,MAAM,KAAK,UAAU,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,WAAW,UAAU,UAAU,MAAM,MAAM,KAAK,KAAK,UAAU,MAAM,MAAM,MAAM,KAAK,WAAW,MAAM,OAAO,MAAM,KAAK,oBAAoB,oBAAoB,MAAM,MAAM,oBAAoB,oBAAoB,MAAM,MAAM,MAAM,MAAM,UAAU,MAAM,MAAM,KAAK,KAAK,UAAU,MAAM,QAAQ,MAAM,YAAY,oBAAoB,qBAAqB,MAAM,MAAM,MAAM,MAAM,UAAU,UAAU,MAAM,WAAW,KAAK,UAAU,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,UAAU,UAAU,UAAU,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,MAAM,MAAM,KAAK,WAAW,WAAW,KAAK,KAAK,MAAM,KAAK,WAAW,MAAM,MAAM,KAAK,WAAW,WAAW,KAAK,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,MAAM,MAAM,KAAK,WAAW,WAAW,KAAK,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,UAAU,WAAW,UAAU,WAAW,KAAK,MAAM,KAAK,WAAW,KAAK,KAAK,KAAK,WAAW,MAAM,KAAK,WAAW,MAAM,KAAK,WAAW,WAAW,WAAW,KAAK,MAAM,WAAW,KAAK,WAAW,WAAW,MAAM,KAAK,UAAU,UAAU,MAAM,KAAK,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,WAAW,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK,WAAW,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY,YAAY,KAAK,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,WAAW,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,YAAY,aAAa,YAAY,aAAa,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,WAAW,aAAa,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,KAAK,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,cAAc,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,WAAW,aAAa,aAAa,aAAa,MAAM,MAAM,aAAa,KAAK,WAAW,YAAY,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,YAAY,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,aAAa,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,aAAa,cAAc,cAAc,OAAO,KAAK,KAAK,MAAM,KAAK,MAAM,YAAY,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,oBAAoB,qBAAqB,qBAAqB,qBAAqB,MAAM,UAAU,KAAK,YAAY,MAAM,KAAK,oBAAoB,qBAAqB,MAAM,KAAK,KAAK,UAAU,YAAY,MAAM,KAAK,UAAU,YAAY,MAAM,KAAK,KAAK,KAAK,UAAU,YAAY,MAAM,KAAK,UAAU,YAAY,MAAM,KAAK,YAAY,aAAa,cAAc,MAAM,WAAW,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,aAAa,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,UAAU,YAAY,aAAa,YAAY,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,YAAY,aAAa,aAAa,aAAa,MAAM,MAAM,aAAa,aAAa,MAAM,YAAY,YAAY,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,YAAY,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,OAAO,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,OAAO,KAAK,YAAY,MAAM,OAAO,MAAM,WAAW,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,YAAY,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,MAAM,YAAY,aAAa,MAAM,MAAM,cAAc,cAAc,aAAa,MAAM,YAAY,MAAM,KAAK,YAAY,OAAO,MAAM,KAAK,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,aAAa,MAAM,OAAO,MAAM,YAAY,aAAa,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,YAAY,MAAM,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,yCAAyC,yBAAyB,wBAAwB,6EAA6E,IAAI,IAAI,mCAAmC,IAAI,mBAAmB,qBAAqB,SAAS,+CAA+C,OAAO,gBAAgB,oDAAoD,OAAO,gBAAgB,wCAAwC,OAAO,gDAAgD,8BAA8B,2EAA2E,OAAO,gCAAgC,mBAAmB,oBAAoB,OAAO,sCAAsC,mCAAmC,OAAO,sCAAsC,qDAAqD,OAAO,uCAAuC,2BAA2B,OAAO,KAAK,2BAA2B,uCAAuC,uSAAuS,OAAO,wBAAwB,wFAAwF,OAAO,0BAA0B,kFAAkF,OAAO,sBAAsB,yDAAyD,OAAO,uBAAuB,qFAAqF,OAAO,mBAAmB,gCAAgC,OAAO,mBAAmB,8BAA8B,OAAO,mBAAmB,mCAAmC,OAAO,0CAA0C,uTAAuT,OAAO,qBAAqB,+TAA+T,OAAO,wCAAwC,2EAA2E,OAAO,wBAAwB,6CAA6C,OAAO,uBAAuB,gFAAgF,OAAO,6BAA6B,qCAAqC,OAAO,yBAAyB,wBAAwB,OAAO,0CAA0C,uFAAuF,OAAO,0BAA0B,+CAA+C,OAAO,0BAA0B,qDAAqD,OAAO,0BAA0B,qDAAqD,OAAO,0BAA0B,qDAAqD,OAAO,yBAAyB,mDAAmD,OAAO,sDAAsD,uFAAuF,OAAO,gCAAgC,oBAAoB,4DAA4D,OAAO,mCAAmC,kBAAkB,cAAc,cAAc,eAAe,cAAc,gBAAgB,oBAAoB,iBAAiB,OAAO,0CAA0C,4CAA4C,OAAO,qBAAqB,0CAA0C,OAAO,uBAAuB,4CAA4C,OAAO,8BAA8B,eAAe,aAAa,aAAa,aAAa,OAAO,+BAA+B,cAAc,qBAAqB,sCAAsC,SAAS,YAAY,qBAAqB,mCAAmC,SAAS,OAAO,iCAAiC,cAAc,qBAAqB,uCAAuC,SAAS,YAAY,qBAAqB,mCAAmC,SAAS,OAAO,sEAAsE,0DAA0D,OAAO,2BAA2B,+CAA+C,OAAO,4BAA4B,iCAAiC,OAAO,oDAAoD,sDAAsD,OAAO,0BAA0B,+EAA+E,OAAO,6BAA6B,4EAA4E,OAAO,6BAA6B,mEAAmE,OAAO,4DAA4D,4DAA4D,OAAO,gCAAgC,gEAAgE,OAAO,qCAAqC,gEAAgE,OAAO,kCAAkC,2GAA2G,OAAO,0DAA0D,0CAA0C,OAAO,8BAA8B,oBAAoB,wGAAwG,OAAO,sDAAsD,iEAAiE,OAAO,gCAAgC,gEAAgE,OAAO,8BAA8B,8DAA8D,OAAO,wDAAwD,sHAAsH,OAAO,8BAA8B,mKAAmK,OAAO,mCAAmC,2CAA2C,OAAO,KAAK,0BAA0B,iDAAiD,2BAA2B,OAAO,2BAA2B,8BAA8B,OAAO,2BAA2B,8BAA8B,OAAO,8CAA8C,sBAAsB,OAAO,kDAAkD,qHAAqH,OAAO,8EAA8E,oDAAoD,OAAO,wBAAwB,iCAAiC,OAAO,oBAAoB,oCAAoC,OAAO,KAAK,mBAAmB;AAC3h8B;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;;AChiGvC,kFAAyC;AACzC,mGAA+C;AAC/C,qHAAgG;AAChG,mDAAmD;AACnD,qIAAgD;AAChD,kIAA8C;AAC9C,wJAA4D;AAC5D,gKAAgE;AAChE,oJAA0D;AAC1D,2IAAoD;AACpD,uJAA4D;AAC5D,gKAAkE;AAClE,mGAAiD;AAEjD,MAAM,GAAG,GAAa,GAAG,EAAE;IACzB,MAAM,EACJ,WAAW,EACX,WAAW,EACX,YAAY,EACZ,OAAO,EACP,KAAK,EACL,QAAQ,GACT,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,EAAE,YAAY,EAAE,GAAG,gCAAW,GAAE,CAAC;IACvC,MAAM,EAAE,iBAAiB,EAAE,GAAG,qCAAgB,GAAE,CAAC;IACjD,MAAM,EAAE,cAAc,EAAE,GAAG,kCAAa,GAAE,CAAC;IAC3C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,6BAAQ,GAAE,CAAC;IACzC,MAAM,UAAU,GAAG,4BAAa,GAAE,CAAC;IAEnC,qBAAqB;IACrB,gBAAgB;IAEhB,8BAA8B;IAC9B,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;YAC5B,IAAI,CAAC;gBACH,oBAAoB;gBACpB,MAAM,OAAO,CAAC,GAAG,CAAC;oBAChB,YAAY,EAAE;oBACd,iBAAiB,EAAE;oBACnB,cAAc,EAAE;iBACjB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC;QAEF,UAAU,EAAE,CAAC;IACf,CAAC,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC,CAAC;IAEtD,4CAA4C;IAC5C,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,OAAO,uBAAC,uBAAa,KAAG,CAAC;IAC3B,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAE,6DAA6D,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,aAE1G,uBAAC,kBAAQ,KAAG,EAGZ,iCAAK,SAAS,EAAC,6BAA6B,aAEzC,WAAW,IAAI,CACd,gCAAK,SAAS,EAAC,6CAA6C,YAC1D,uBAAC,iBAAO,KAAG,GACP,CACP,EAGD,iCAAK,SAAS,EAAC,+BAA+B,aAC3C,WAAW,KAAK,MAAM,IAAI,uBAAC,uBAAa,KAAG,EAC3C,WAAW,KAAK,UAAU,IAAI,uBAAC,uBAAa,KAAG,EAC/C,WAAW,KAAK,MAAM,IAAI,uBAAC,SAAS,KAAG,EAGvC,YAAY,IAAI,CACf,gCAAK,SAAS,EAAC,oDAAoD,YACjE,gCAAK,SAAS,EAAC,6CAA6C,YAC1D,gCAAK,SAAS,EAAC,sGAAsG,YACnH,uBAAC,uBAAa,KAAG,GACb,GACF,GACF,CACP,IACG,IACF,EAGL,KAAK,IAAI,uBAAC,oBAAU,IAAC,OAAO,EAAE,KAAK,GAAI,EAGxC,uBAAC,wBAAc,IAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,GAAI,EAGvD,gCAAK,SAAS,EAAC,6BAA6B,YAC1C,uBAAC,2BAAiB,KAAG,GACjB,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,uCAAuC;AACvC,MAAM,SAAS,GAAa,GAAG,EAAE;IAC/B,OAAO,CACL,gCAAK,SAAS,EAAC,YAAY,YACzB,iCAAK,SAAS,EAAC,mBAAmB,aAChC,+BAAI,SAAS,EAAC,uCAAuC,4BAAiB,EACtE,gCAAK,SAAS,EAAC,mDAAmD,YAChE,iCAAK,SAAS,EAAC,2CAA2C,aACxD,gCAAK,SAAS,EAAC,kBAAkB,wDAA8C,EAC/E,gCAAK,SAAS,EAAC,kBAAkB,4CAAkC,EACnE,gCAAK,SAAS,EAAC,kBAAkB,uCAA6B,EAC9D,gCAAK,SAAS,EAAC,kBAAkB,iDAAuC,EACxE,gCAAK,SAAS,EAAC,kBAAkB,4CAAkC,EACnE,gCAAK,SAAS,EAAC,kBAAkB,6CAAmC,IAChE,GACF,IACF,GACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,GAAG,CAAC;;;;;;;;;;;;;;AC5HnB,kFAA2D;AAC3D,2GAAuD;AACvD,uHAAoE;AAEpE,MAAM,SAAS,GAAa,GAAG,EAAE;IAC/B,MAAM,EACJ,mBAAmB,EACnB,WAAW,EACX,UAAU,EACV,WAAW,EACX,cAAc,GACf,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,oBAAQ,EAAC,EAAE,CAAC,CAAC;IACvC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IACtD,MAAM,WAAW,GAAG,kBAAM,EAAsB,IAAI,CAAC,CAAC;IACtD,MAAM,YAAY,GAAG,kBAAM,EAAmB,IAAI,CAAC,CAAC;IAEpD,uBAAuB;IACvB,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YAC1C,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC;QAC5F,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,wCAAwC;IACxC,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,mBAAmB,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YAC/C,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAE1B,MAAM,YAAY,GAAG,KAAK,EAAE,CAAkB,EAAE,EAAE;QAChD,CAAC,CAAC,cAAc,EAAE,CAAC;QAEnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,IAAI,WAAW,EAAE,CAAC;YACzD,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAC7B,QAAQ,CAAC,EAAE,CAAC,CAAC;QAEb,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,mBAAmB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,CAAsB,EAAE,EAAE;QAC/C,IAAI,CAAC,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,cAAc,EAAE,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,CAAsC,EAAE,EAAE;QAClE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QAC/C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,4CAA4C;YAC5C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,WAAW,EAAE,CAAC;YAChB,yCAAyC;YACzC,cAAc,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,cAAc,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,mBAAmB,IAAI,CAAC,WAAW,CAAC;IACpE,MAAM,wBAAwB,GAAG,UAAU,EAAE,wBAAwB,CAAC;IACtE,MAAM,UAAU,GAAG,WAAW,IAAI,wBAAwB,CAAC;IAE3D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,gCAAK,SAAS,EAAC,0CAA0C,YACvD,iCAAK,SAAS,EAAC,mBAAmB,aAE/B,wBAAwB,IAAI,CAC3B,gCAAK,SAAS,EAAC,0FAA0F,YACvG,iCAAK,SAAS,EAAC,yBAAyB,aACtC,gCAAK,SAAS,EAAC,mDAAmD,GAAO,EACzE,6GAA0E,IACtE,GACF,CACP,EAEA,WAAW,IAAI,CACd,gCAAK,SAAS,EAAC,iFAAiF,YAC9F,iCAAK,SAAS,EAAC,mCAAmC,aAChD,iCAAK,SAAS,EAAC,yBAAyB,aACtC,gCAAK,SAAS,EAAC,gDAAgD,GAAO,EACtE,0EAAuC,IACnC,EACN,oCACE,OAAO,EAAE,UAAU,EACnB,SAAS,EAAC,2EAA2E,aAErF,uBAAC,qBAAM,IAAC,SAAS,EAAC,qBAAqB,GAAG,YAEnC,IACL,GACF,CACP,EAGD,kCAAM,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAC,UAAU,aAChD,iCAAK,SAAS,EAAC,sBAAsB,aAEnC,mCACE,IAAI,EAAC,QAAQ,EACb,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,EAC5C,QAAQ,EAAE,UAAU,EACpB,SAAS,EAAE,oCACT,UAAU;wCACR,CAAC,CAAC,kCAAkC;wCACpC,CAAC,CAAC,qDACN,EAAE,EACF,KAAK,EAAC,cAAc,YAEpB,uBAAC,wBAAS,IAAC,SAAS,EAAC,SAAS,GAAG,GAC1B,EAGT,gCAAK,SAAS,EAAC,iBAAiB,YAC9B,qCACE,GAAG,EAAE,WAAW,EAChB,KAAK,EAAE,KAAK,EACZ,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACzC,SAAS,EAAE,aAAa,EACxB,WAAW,EACT,UAAU;4CACR,CAAC,CAAC,gBAAgB;4CAClB,CAAC,CAAC,gEAAgE,EAEtE,QAAQ,EAAE,UAAU,EACpB,SAAS,EAAE,gGACT,UAAU;4CACR,CAAC,CAAC,+BAA+B;4CACjC,CAAC,CAAC,8DACN,qCAAqC,EACrC,IAAI,EAAE,CAAC,EACP,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,GAC7B,GACE,EAGN,mCACE,IAAI,EAAC,QAAQ,EACb,OAAO,EAAE,eAAe,EACxB,QAAQ,EAAE,UAAU,EACpB,SAAS,EAAE,oCACT,WAAW;wCACT,CAAC,CAAC,uBAAuB;wCACzB,CAAC,CAAC,UAAU;4CACZ,CAAC,CAAC,kCAAkC;4CACpC,CAAC,CAAC,qDACN,EAAE,EACF,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,uBAAuB,YAE9D,WAAW,CAAC,CAAC,CAAC,CACb,uBAAC,qBAAM,IAAC,SAAS,EAAC,SAAS,GAAG,CAC/B,CAAC,CAAC,CAAC,CACF,uBAAC,kBAAG,IAAC,SAAS,EAAC,SAAS,GAAG,CAC5B,GACM,EAGT,mCACE,IAAI,EAAC,QAAQ,EACb,QAAQ,EAAE,CAAC,OAAO,EAClB,SAAS,EAAE,oCACT,OAAO;wCACL,CAAC,CAAC,gDAAgD;wCAClD,CAAC,CAAC,8CACN,EAAE,EACF,KAAK,EAAC,sBAAsB,YAE5B,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,GACrB,IACL,EAGN,kCACE,GAAG,EAAE,YAAY,EACjB,IAAI,EAAC,MAAM,EACX,QAAQ,QACR,QAAQ,EAAE,gBAAgB,EAC1B,SAAS,EAAC,QAAQ,EAClB,MAAM,EAAC,0DAA0D,GACjE,IACG,EAGP,iCAAK,SAAS,EAAC,wCAAwC,uBAC/C,gCAAK,SAAS,EAAC,iCAAiC,sBAAY,eAAU,GAAG,EAC/E,gCAAK,SAAS,EAAC,iCAAiC,sBAAY,QAAG,GAAG,EAClE,gCAAK,SAAS,EAAC,iCAAiC,sBAAY,qBACxD,IACF,GACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,SAAS,CAAC;;;;;;;;;;;;;;;;;ACzNzB,kFAAiD;AACjD,2GAA2E;AAC3E,wIAA4C;AAC5C,4HAAoC;AACpC,iJAAkD;AAClD,oJAAoD;AACpD,iJAAkD;AAClD,uHAAkD;AAElD,MAAM,aAAa,GAAa,GAAG,EAAE;IACnC,MAAM,EACJ,mBAAmB,EACnB,WAAW,EACX,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,UAAU,GACX,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,QAAQ,GAAG,iCAAkB,GAAE,CAAC;IACtC,MAAM,cAAc,GAAG,kBAAM,EAAiB,IAAI,CAAC,CAAC;IAEpD,iDAAiD;IACjD,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC,CAAC;IAE9C,qDAAqD;IACrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACzB,OAAO,CACL,gCAAK,SAAS,EAAC,qDAAqD,YAClE,iCAAK,SAAS,EAAC,sBAAsB,aACnC,gCAAK,SAAS,EAAC,kIAAkI,YAC/I,uBAAC,kBAAG,IAAC,SAAS,EAAC,sBAAsB,GAAG,GACpC,EAEN,+BAAI,SAAS,EAAC,uCAAuC,wCAEhD,EAEL,8BAAG,SAAS,EAAC,oBAAoB,8HAE7B,EAEJ,iCAAK,SAAS,EAAC,iCAAiC,aAC9C,8EAAmC,EACnC,yFAAoC,EACpC,oFAAoC,EACpC,kFAAkC,IAC9B,IACF,GACF,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAC,kCAAkC,aAE/C,iCAAK,SAAS,EAAC,kFAAkF,aAC/F,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,uBAAC,4BAAa,IAAC,SAAS,EAAC,uBAAuB,GAAG,EACnD,+BAAI,SAAS,EAAC,qCAAqC,YAChD,mBAAmB,CAAC,KAAK,GACvB,EACJ,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CACtB,kCAAM,SAAS,EAAC,uBAAuB,aACpC,QAAQ,CAAC,MAAM,cAAU,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IACrD,CACR,IACG,EAEN,gCAAK,SAAS,EAAC,mDAAmD,YAC/D,UAAU,EAAE,wBAAwB,IAAI,CACvC,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,gCAAK,SAAS,EAAC,mDAAmD,GAAO,EACzE,wEAAqC,IACjC,CACP,GACG,IACF,EAGN,iCAAK,SAAS,EAAC,sCAAsC,aAClD,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CACvB,iCAAK,SAAS,EAAC,mBAAmB,aAChC,uBAAC,kBAAG,IAAC,SAAS,EAAC,sCAAsC,GAAG,EACxD,+BAAI,SAAS,EAAC,wCAAwC,uCAEjD,EACL,8BAAG,SAAS,EAAC,eAAe,oEAExB,IACA,CACP,CAAC,CAAC,CAAC,CACF,6DACG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CACzB,uBAAC,uBAAa,IAAkB,OAAO,EAAE,OAAO,IAA5B,OAAO,CAAC,EAAE,CAAsB,CACrD,CAAC,EAGD,WAAW,IAAI,gBAAgB,IAAI,CAClC,uBAAC,0BAAgB,IAAC,OAAO,EAAE,gBAAgB,GAAI,CAChD,EAGA,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CACzB,iCAAK,SAAS,EAAC,WAAW,aACxB,gCAAI,SAAS,EAAC,+DAA+D,aAC3E,sEAAmC,EACnC,gCAAK,SAAS,EAAC,yBAAyB,GAAO,IAC5C,EACJ,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAC3B,uBAAC,2BAAiB,IAAiB,MAAM,EAAE,MAAM,IAAzB,MAAM,CAAC,EAAE,CAAoB,CACtD,CAAC,IACE,CACP,EAGA,WAAW,IAAI,UAAU,EAAE,wBAAwB,IAAI,CACtD,uBAAC,0BAAgB,IAAC,IAAI,EAAE,WAAW,GAAI,CACxC,IACA,CACJ,EAGD,gCAAK,GAAG,EAAE,cAAc,GAAI,IACxB,EAGN,uBAAC,mBAAS,KAAG,IACT,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,aAAa,CAAC;;;;;;;;;;;;;;;;;ACxI7B,kFAAwC;AAExC,uHAA0E;AAC1E,4FAAkC;AAClC,8HAA2C;AAC3C,oJAAsE;AACtE,oKAAyE;AAMzE,MAAM,aAAa,GAAiC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;IAClE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;IACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC;IAE3C,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;QAC5B,IAAI,CAAC;YACH,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,SAAS;gBACZ,OAAO,uBAAC,oBAAK,IAAC,SAAS,EAAC,qCAAqC,GAAG,CAAC;YACnE,KAAK,OAAO;gBACV,OAAO,uBAAC,0BAAW,IAAC,SAAS,EAAC,sBAAsB,GAAG,CAAC;YAC1D;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,SAAe,EAAE,EAAE;QACrC,OAAO,qBAAM,EAAC,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,CACL,gCAAK,SAAS,EAAC,qBAAqB,YAClC,gCAAK,SAAS,EAAC,mEAAmE,YAC/E,OAAO,CAAC,OAAO,GACZ,GACF,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAE,cAAc,MAAM,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,EAAE,aAEtE,gCAAK,SAAS,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,YACzD,gCAAK,SAAS,EAAE,yDACd,MAAM;wBACJ,CAAC,CAAC,2BAA2B;wBAC7B,CAAC,CAAC,0DACN,EAAE,YACC,MAAM,CAAC,CAAC,CAAC,CACR,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,CAC7B,CAAC,CAAC,CAAC,CACF,uBAAC,kBAAG,IAAC,SAAS,EAAC,SAAS,GAAG,CAC5B,GACG,GACF,EAGN,iCAAK,SAAS,EAAE,oBAAoB,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,gBAAgB,aACtF,iCACE,SAAS,EAAE,kDACT,MAAM;4BACJ,CAAC,CAAC,iCAAiC;4BACnC,CAAC,CAAC,iCACN,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,aAGhE,gCAAK,SAAS,EAAC,2BAA2B,YACvC,MAAM,CAAC,CAAC,CAAC,CACR,gCAAK,SAAS,EAAC,iCAAiC,YAC7C,OAAO,CAAC,OAAO,GACZ,CACP,CAAC,CAAC,CAAC,CACF,uBAAC,wBAAa,IACZ,UAAU,EAAE;wCACV,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE;4CAClD,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;4CACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;4CAEvC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;gDACxB,OAAO,CACL,iCAAK,SAAS,EAAC,UAAU,aACvB,uBAAC,gCAAiB,IAChB,KAAK,EAAE,eAAO,EACd,QAAQ,EAAE,QAAQ,EAClB,MAAM,EAAC,KAAK,EACZ,SAAS,EAAC,iBAAiB,KACvB,KAAK,YAER,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAClB,EACpB,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAC9D,SAAS,EAAC,gFAAgF,EAC1F,KAAK,EAAC,WAAW,YAEjB,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,GACrB,IACL,CACP,CAAC;4CACJ,CAAC;4CAED,OAAO,CACL,iCAAM,SAAS,EAAC,yCAAyC,KAAK,KAAK,YAChE,QAAQ,GACJ,CACR,CAAC;wCACJ,CAAC;wCACD,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACnB,gCAAK,SAAS,EAAC,wCAAwC,YACpD,QAAQ,GACL,CACP;wCACD,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACpB,+BAAI,SAAS,EAAC,8CAA8C,YACzD,QAAQ,GACN,CACN;wCACD,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACpB,+BAAI,SAAS,EAAC,iDAAiD,YAC5D,QAAQ,GACN,CACN;wCACD,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC5B,uCAAY,SAAS,EAAC,8DAA8D,YACjF,QAAQ,GACE,CACd;qCACF,EACD,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,YAEtC,OAAO,CAAC,OAAO,GACF,CACjB,GACG,EAGN,mCACE,OAAO,EAAE,UAAU,EACnB,SAAS,EAAE,2FACT,MAAM;oCACJ,CAAC,CAAC,wDAAwD;oCAC1D,CAAC,CAAC,qDACN,EAAE,EACF,KAAK,EAAC,cAAc,YAEnB,MAAM,CAAC,CAAC,CAAC,CACR,uBAAC,oBAAK,IAAC,SAAS,EAAC,SAAS,GAAG,CAC9B,CAAC,CAAC,CAAC,CACF,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,CAC7B,GACM,IACL,EAGN,iCAAK,SAAS,EAAE,sDACd,MAAM,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAChC,EAAE,aACA,2CAAO,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,GAAQ,EAC3C,aAAa,EAAE,EACf,OAAO,CAAC,MAAM,IAAI,CACjB,kCAAM,SAAS,EAAC,eAAe,aAC5B,OAAO,CAAC,MAAM,eACV,CACR,EACA,OAAO,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAC3B,iCAAM,SAAS,EAAC,eAAe,YAC5B,OAAO,CAAC,KAAK,GACT,CACR,IACG,EAGL,OAAO,CAAC,MAAM,KAAK,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,CAC9C,iCAAK,SAAS,EAAC,oFAAoF,aACjG,iCAAK,SAAS,EAAC,8BAA8B,aAC3C,uBAAC,0BAAW,IAAC,SAAS,EAAC,SAAS,GAAG,EACnC,iCAAM,SAAS,EAAC,aAAa,sBAAa,IACtC,EACN,gCAAK,SAAS,EAAC,cAAc,YAC1B,OAAO,CAAC,KAAK,GACV,IACF,CACP,IACG,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,aAAa,CAAC;;;;;;;;;;;;;;ACxM7B,2GAAuD;AAEvD,uHAA0G;AAM1G,MAAM,gBAAgB,GAAoC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;IACrE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,0BAAW,GAAE,CAAC;IAElD,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;QACnC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM;gBACT,OAAO,uBAAC,qBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YACtC,KAAK,MAAM;gBACT,OAAO,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YACtC,KAAK,MAAM;gBACT,OAAO,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YAC1C;gBACE,OAAO,uBAAC,oBAAK,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;QACzC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,IAA+B,EAAE,EAAE;QACvD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,KAAK;gBACR,OAAO,iDAAiD,CAAC;YAC3D,KAAK,QAAQ;gBACX,OAAO,oDAAoD,CAAC;YAC9D,KAAK,MAAM;gBACT,OAAO,2CAA2C,CAAC;QACvD,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,iCAAK,SAAS,EAAC,6DAA6D,aAE1E,iCAAK,SAAS,EAAC,mCAAmC,aAChD,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,uBAAC,4BAAa,IAAC,SAAS,EAAC,0BAA0B,GAAG,EACtD,+BAAI,SAAS,EAAC,qCAAqC,2CAE9C,IACD,EACN,iCAAK,SAAS,EAAE,oCAAoC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,aAC/E,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aACzB,IACF,EAGN,gCAAK,SAAS,EAAC,uBAAuB,YACpC,wCAAI,IAAI,CAAC,WAAW,GAAK,GACrB,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,+BAAI,SAAS,EAAC,mCAAmC,iCAAsB,EACvE,gCAAK,SAAS,EAAC,WAAW,YACvB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAC/B,iCAEE,SAAS,EAAC,uDAAuD,aAEjE,gCAAK,SAAS,EAAC,sBAAsB,YACnC,gCAAK,SAAS,EAAC,yFAAyF,YACrG,KAAK,GAAG,CAAC,GACN,GACF,EACN,iCAAK,SAAS,EAAC,gBAAgB,aAC7B,iCAAK,SAAS,EAAC,kCAAkC,aAC9C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EACvB,iCAAM,SAAS,EAAC,mCAAmC,YAChD,IAAI,CAAC,MAAM,GACP,EACN,IAAI,CAAC,IAAI,IAAI,CACZ,iCAAM,SAAS,EAAC,uDAAuD,YACpE,IAAI,CAAC,IAAI,GACL,CACR,IACG,EACN,8BAAG,SAAS,EAAC,uBAAuB,YAAE,IAAI,CAAC,WAAW,GAAK,EAC1D,IAAI,CAAC,OAAO,IAAI,CACf,gCAAK,SAAS,EAAC,8DAA8D,YAC1E,IAAI,CAAC,OAAO,GACT,CACP,IACG,KA1BD,KAAK,CA2BN,CACP,CAAC,GACE,IACF,EAGL,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAC5C,iCAAK,SAAS,EAAC,WAAW,aACxB,gCAAI,SAAS,EAAC,kEAAkE,aAC9E,uBAAC,4BAAa,IAAC,SAAS,EAAC,SAAS,GAAG,EACrC,mEAAgC,IAC7B,EACL,+BAAI,SAAS,EAAC,WAAW,YACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CACrC,gCAAgB,SAAS,EAAC,qDAAqD,aAC7E,iCAAM,SAAS,EAAC,uBAAuB,uBAAS,EAChD,2CAAO,OAAO,GAAQ,KAFf,KAAK,CAGT,CACN,CAAC,GACC,IACD,CACP,EAGA,IAAI,CAAC,iBAAiB,IAAI,CACzB,iCAAK,SAAS,EAAC,uBAAuB,aACpC,uBAAC,oBAAK,IAAC,SAAS,EAAC,qBAAqB,GAAG,iCACb,IAAI,CAAC,iBAAiB,IAC9C,CACP,EAGD,iCAAK,SAAS,EAAC,uEAAuE,aACpF,oCACE,OAAO,EAAE,YAAY,EACrB,SAAS,EAAC,gHAAgH,aAE1H,uBAAC,sBAAO,IAAC,SAAS,EAAC,SAAS,GAAG,EAC/B,sDAAmB,IACZ,EACT,oCACE,OAAO,EAAE,aAAa,EACtB,SAAS,EAAC,mHAAmH,aAE7H,uBAAC,0BAAW,IAAC,SAAS,EAAC,SAAS,GAAG,EACnC,iEAA8B,IACvB,IACL,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,gBAAgB,CAAC;;;;;;;;;;;;;;;;;ACpJhC,uHAAmC;AACnC,8HAA2C;AAC3C,oJAAsE;AACtE,oKAAyE;AAMzE,MAAM,gBAAgB,GAAoC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE;IACxE,OAAO,CACL,iCAAK,SAAS,EAAC,qBAAqB,aAElC,gCAAK,SAAS,EAAC,oBAAoB,YACjC,gCAAK,SAAS,EAAC,gHAAgH,YAC7H,uBAAC,kBAAG,IAAC,SAAS,EAAC,SAAS,GAAG,GACvB,GACF,EAGN,gCAAK,SAAS,EAAC,4CAA4C,YACzD,iCAAK,SAAS,EAAC,0EAA0E,aAEvF,gCAAK,SAAS,EAAC,2BAA2B,YACvC,OAAO,CAAC,CAAC,CAAC,CACT,uBAAC,wBAAa,IACZ,UAAU,EAAE;oCACV,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE;wCAClD,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;wCACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wCAEvC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;4CACxB,OAAO,CACL,uBAAC,gCAAiB,IAChB,KAAK,EAAE,eAAO,EACd,QAAQ,EAAE,QAAQ,EAClB,MAAM,EAAC,KAAK,EACZ,SAAS,EAAC,iBAAiB,KACvB,KAAK,YAER,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAClB,CACrB,CAAC;wCACJ,CAAC;wCAED,OAAO,CACL,iCAAM,SAAS,EAAC,yCAAyC,KAAK,KAAK,YAChE,QAAQ,GACJ,CACR,CAAC;oCACJ,CAAC;oCACD,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACnB,gCAAK,SAAS,EAAC,wCAAwC,YACpD,QAAQ,GACL,CACP;oCACD,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACpB,+BAAI,SAAS,EAAC,8CAA8C,YACzD,QAAQ,GACN,CACN;oCACD,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CACpB,+BAAI,SAAS,EAAC,iDAAiD,YAC5D,QAAQ,GACN,CACN;oCACD,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC5B,uCAAY,SAAS,EAAC,8DAA8D,YACjF,QAAQ,GACE,CACd;iCACF,YAEA,OAAO,GACM,CACjB,CAAC,CAAC,CAAC,CACF,iCAAK,SAAS,EAAC,uBAAuB,aACpC,wDAAqB,EACrB,iCAAM,SAAS,EAAC,oBAAoB,oBAAW,IAC3C,CACP,GACG,EAGN,iCAAK,SAAS,EAAC,8CAA8C,aAC3D,iCAAK,SAAS,EAAC,gBAAgB,aAC7B,gCAAK,SAAS,EAAC,iDAAiD,GAAO,EACvE,gCAAK,SAAS,EAAC,iDAAiD,EAAC,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,GAAQ,EAC1G,gCAAK,SAAS,EAAC,iDAAiD,EAAC,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,GAAQ,IACtG,EACN,iCAAM,SAAS,EAAC,MAAM,uCAA8B,IAChD,IACF,GACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,gBAAgB,CAAC;;;;;;;;;;;;;;ACnGhC,kFAAwC;AAExC,uHAcsB;AAMtB,MAAM,iBAAiB,GAAqC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;IACzE,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,oBAAQ,EAAC,IAAI,CAAC,CAAC;IACnD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAE5C,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAE,EAAE;QACvC,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/B,KAAK,WAAW,CAAC;YACjB,KAAK,YAAY,CAAC;YAClB,KAAK,aAAa;gBAChB,OAAO,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YAC1C,KAAK,cAAc,CAAC;YACpB,KAAK,cAAc;gBACjB,OAAO,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YACtC,KAAK,UAAU,CAAC;YAChB,KAAK,OAAO;gBACV,OAAO,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YAC1C,KAAK,UAAU,CAAC;YAChB,KAAK,KAAK;gBACR,OAAO,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YAC1C,KAAK,YAAY,CAAC;YAClB,KAAK,YAAY;gBACf,OAAO,uBAAC,oBAAK,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;YACvC;gBACE,OAAO,uBAAC,qBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC;QACxC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,SAAS;gBACZ,OAAO,uBAAC,0BAAW,IAAC,SAAS,EAAC,wBAAwB,GAAG,CAAC;YAC5D,KAAK,OAAO;gBACV,OAAO,uBAAC,sBAAO,IAAC,SAAS,EAAC,sBAAsB,GAAG,CAAC;YACtD,KAAK,SAAS;gBACZ,OAAO,uBAAC,oBAAK,IAAC,SAAS,EAAC,qCAAqC,GAAG,CAAC;YACnE;gBACE,OAAO,uBAAC,oBAAK,IAAC,SAAS,EAAC,uBAAuB,GAAG,CAAC;QACvD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,SAAS;gBACZ,OAAO,kCAAkC,CAAC;YAC5C,KAAK,OAAO;gBACV,OAAO,8BAA8B,CAAC;YACxC,KAAK,SAAS;gBACZ,OAAO,gCAAgC,CAAC;YAC1C;gBACE,OAAO,6BAA6B,CAAC;QACzC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;QAC5B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;gBAClD,CAAC,CAAC,MAAM,CAAC,MAAM;gBACf,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3C,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAChD,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,MAAW,EAAE,EAAE;QACnC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,QAAgB,EAAE,EAAE;QAC1C,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,OAAO,GAAG,QAAQ,IAAI,CAAC;QACzB,CAAC;QACD,OAAO,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5C,CAAC,CAAC;IAEF,OAAO,CACL,iCAAK,SAAS,EAAE,qBAAqB,cAAc,EAAE,EAAE,aAErD,iCAAK,SAAS,EAAC,KAAK,aAClB,iCAAK,SAAS,EAAC,mCAAmC,aAChD,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,EACzC,SAAS,EAAC,mEAAmE,YAE5E,UAAU,CAAC,CAAC,CAAC,CACZ,uBAAC,0BAAW,IAAC,SAAS,EAAC,SAAS,GAAG,CACpC,CAAC,CAAC,CAAC,CACF,uBAAC,2BAAY,IAAC,SAAS,EAAC,SAAS,GAAG,CACrC,GACM,EAET,iCAAK,SAAS,EAAC,6BAA6B,aACzC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAC7B,iCAAM,SAAS,EAAC,mCAAmC,YAChD,MAAM,CAAC,QAAQ,GACX,IACH,EAEL,aAAa,EAAE,IACZ,EAEN,iCAAK,SAAS,EAAC,mDAAmD,aAC/D,MAAM,CAAC,QAAQ,IAAI,CAClB,2CAAO,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAQ,CAC/C,EACA,MAAM,CAAC,SAAS,IAAI,CACnB,2CACG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,GAC3C,CACR,IACG,IACF,EAGL,MAAM,CAAC,KAAK,IAAI,CACf,iCAAK,SAAS,EAAC,4BAA4B,aACzC,iCAAM,SAAS,EAAC,aAAa,wBAAe,EAC5C,iCAAM,SAAS,EAAC,mBAAmB,YAChC,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ;oCAC/B,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;wCACzB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;wCACxC,CAAC,CAAC,MAAM,CAAC,KAAK;oCAChB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG;wCACzC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;wCACxD,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,GAE7B,IACH,CACP,IACG,EAGL,UAAU,IAAI,CACb,iCAAK,SAAS,EAAC,0BAA0B,aAEtC,MAAM,CAAC,MAAM,KAAK,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAC5C,gCAAK,SAAS,EAAC,mBAAmB,YAChC,iCAAK,SAAS,EAAC,4BAA4B,aACzC,uBAAC,sBAAO,IAAC,SAAS,EAAC,2CAA2C,GAAG,EACjE,iCAAK,SAAS,EAAC,QAAQ,aACrB,gCAAK,SAAS,EAAC,uCAAuC,gCAEhD,EACN,gCAAK,SAAS,EAAC,oDAAoD,YAChE,MAAM,CAAC,KAAK,GACT,IACF,IACF,GACF,CACP,EAGA,MAAM,CAAC,MAAM,IAAI,CAChB,iCAAK,SAAS,EAAC,KAAK,aAClB,iCAAK,SAAS,EAAC,wCAAwC,aACrD,iCAAM,SAAS,EAAC,mCAAmC,wBAAe,EAClE,mCACE,OAAO,EAAE,UAAU,EACnB,SAAS,EAAC,6HAA6H,EACvI,KAAK,EAAC,aAAa,YAElB,MAAM,CAAC,CAAC,CAAC,CACR,6DACE,uBAAC,oBAAK,IAAC,SAAS,EAAC,SAAS,GAAG,EAC7B,sDAAmB,IAClB,CACJ,CAAC,CAAC,CAAC,CACF,6DACE,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,EAC5B,oDAAiB,IAChB,CACJ,GACM,IACL,EAEN,gCAAK,SAAS,EAAC,yEAAyE,YACtF,gCAAK,SAAS,EAAC,qDAAqD,YACjE,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,GACxB,GACF,IACF,CACP,EAGA,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAC/D,iCAAK,SAAS,EAAC,6CAA6C,aAC1D,gCAAK,SAAS,EAAC,wCAAwC,0BAAgB,EACvE,gCAAK,SAAS,EAAC,wBAAwB,YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CACrD,iCAAe,SAAS,EAAC,SAAS,aAChC,kCAAM,SAAS,EAAC,eAAe,aAAE,GAAG,SAAS,EAC7C,iCAAM,SAAS,EAAC,8BAA8B,YAC3C,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GACrD,KAJC,GAAG,CAKP,CACP,CAAC,GACE,IACF,CACP,IACG,CACP,IACG,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,iBAAiB,CAAC;;;;;;;;;;;;;;ACzOjC,kFAAyC;AACzC,uHAA4E;AAC5E,2GAAyE;AAkBzE,MAAM,iBAAiB,GAAqC,CAAC,EAC3D,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,IAAI,GAAG,MAAM,EACb,WAAW,GAAG,SAAS,EACvB,UAAU,GAAG,QAAQ,EACrB,OAAO,EACP,WAAW,GAAG,KAAK,EACnB,SAAS,GAAG,KAAK,EACjB,OAAO,GAAG,KAAK,GAChB,EAAE,EAAE;IACH,MAAM,YAAY,GAAG,2BAAY,EAAC,MAAM,CAAC,CAAC;IAC1C,MAAM,QAAQ,GAAG,8BAAe,EAAC,GAAG,EAAE;QACpC,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,MAAM,EAAE,CAAC;YACX,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;QACzC,CAAC;QAED,OAAO,GAAG,EAAE;YACV,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,IAAI,CAAC,MAAM;QAAE,OAAO,IAAI,CAAC;IAEzB,MAAM,OAAO,GAAG,GAAG,EAAE;QACnB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,uBAAC,4BAAa,IAAC,SAAS,EAAC,yBAAyB,GAAG,CAAC;YAC/D,KAAK,OAAO;gBACV,OAAO,uBAAC,sBAAO,IAAC,SAAS,EAAC,sBAAsB,GAAG,CAAC;YACtD,KAAK,SAAS;gBACZ,OAAO,uBAAC,0BAAW,IAAC,SAAS,EAAC,wBAAwB,GAAG,CAAC;YAC5D;gBACE,OAAO,uBAAC,mBAAI,IAAC,SAAS,EAAC,uBAAuB,GAAG,CAAC;QACtD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,mBAAmB,CAAC;YAC7B,KAAK,OAAO;gBACV,OAAO,gBAAgB,CAAC;YAC1B,KAAK,SAAS;gBACZ,OAAO,kBAAkB,CAAC;YAC5B;gBACE,OAAO,iBAAiB,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,GAAG,EAAE;QACjC,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,gDAAgD,CAAC;QAC1D,CAAC;QACD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,yDAAyD,CAAC;YACnE,KAAK,OAAO;gBACV,OAAO,gDAAgD,CAAC;YAC1D,KAAK,SAAS;gBACZ,OAAO,sDAAsD,CAAC;YAChE;gBACE,OAAO,mDAAmD,CAAC;QAC/D,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;QACd,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,QAAQ,EAAE,CAAC;gBACb,QAAQ,EAAE,CAAC;YACb,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,gCAAK,SAAS,EAAC,4EAA4E,YACzF,gCACE,GAAG,EAAE,QAAQ,EACb,SAAS,EAAC,wDAAwD,YAElE,iCAAK,GAAG,EAAE,YAAY,aAEpB,iCAAK,SAAS,EAAE,6EAA6E,cAAc,EAAE,EAAE,aAC7G,iCAAK,SAAS,EAAC,6BAA6B,aACzC,OAAO,EAAE,EACV,+BAAI,SAAS,EAAC,qCAAqC,YAAE,KAAK,GAAM,IAC5D,EACL,CAAC,OAAO,IAAI,CACX,mCACE,OAAO,EAAE,OAAO,EAChB,SAAS,EAAC,qDAAqD,EAC/D,QAAQ,EAAE,OAAO,YAEjB,uBAAC,gBAAC,IAAC,SAAS,EAAC,SAAS,GAAG,GAClB,CACV,IACG,EAGN,iCAAK,SAAS,EAAC,KAAK,aAClB,8BAAG,SAAS,EAAC,oCAAoC,YAAE,OAAO,GAAK,EAE9D,OAAO,IAAI,WAAW,IAAI,CACzB,iCAAK,SAAS,EAAC,iCAAiC,aAC9C,+BAAI,SAAS,EAAC,wCAAwC,yBAAc,EACpE,gCAAK,SAAS,EAAC,2DAA2D,YACvE,OAAO,GACJ,IACF,CACP,EAEA,SAAS,IAAI,CACZ,gCAAK,SAAS,EAAC,oEAAoE,YACjF,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,uBAAC,4BAAa,IAAC,SAAS,EAAC,oCAAoC,GAAG,EAChE,8BAAG,SAAS,EAAC,sBAAsB,2EAE/B,IACA,GACF,CACP,IACG,EAGN,iCAAK,SAAS,EAAC,sEAAsE,aACnF,mCACE,OAAO,EAAE,YAAY,EACrB,QAAQ,EAAE,OAAO,EACjB,SAAS,EAAC,yIAAyI,YAElJ,UAAU,GACJ,EACT,mCACE,OAAO,EAAE,aAAa,EACtB,QAAQ,EAAE,OAAO,EACjB,SAAS,EAAE,oLAAoL,qBAAqB,EAAE,EAAE,YAEvN,OAAO,CAAC,CAAC,CAAC,CACT,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,gCAAK,SAAS,EAAC,8EAA8E,GAAO,EACpG,6DAA0B,IACtB,CACP,CAAC,CAAC,CAAC,CACF,WAAW,CACZ,GACM,IACL,IACF,GACF,GACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,iBAAiB,CAAC;;;;;;;;;;;;;;AC/LjC,kFAAmD;AACnD,2GAAuD;AACvD,uHAAqG;AACrG,6HAA6D;AAC7D,8FAAmD;AAGnD,MAAM,aAAa,GAAa,GAAG,EAAE;IACnC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,0BAAW,GAAE,CAAC;IACpD,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,gCAAW,GAAE,CAAC;IACnE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,oBAAQ,EAAqB,IAAI,CAAC,CAAC;IAC7E,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,oBAAQ,EAAwC,MAAM,CAAC,CAAC;IAE5F,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,QAAQ,EAAE,CAAC;YACb,gBAAgB,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;QACpC,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,aAAa,IAAI,QAAQ,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC3E,aAAa,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;IACH,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE9B,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;QAC5B,IAAI,CAAC,aAAa,IAAI,CAAC,UAAU;YAAE,OAAO;QAE1C,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxB,IAAI,CAAC;YACH,MAAM,cAAc,CAAC,aAAa,CAAC,CAAC;YACpC,aAAa,CAAC,OAAO,CAAC,CAAC;YACvB,UAAU,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,aAAa,CAAC,OAAO,CAAC,CAAC;YACvB,UAAU,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC7B,IAAI,MAAM,CAAC,OAAO,CAAC,0DAA0D,CAAC,EAAE,CAAC;YAC/E,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC;gBACH,MAAM,aAAa,EAAE,CAAC;gBACtB,aAAa,CAAC,OAAO,CAAC,CAAC;gBACvB,UAAU,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,aAAa,CAAC,OAAO,CAAC,CAAC;gBACvB,UAAU,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,KAAU,EAAE,EAAE;QACtD,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,WAAW,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;QACzC,IAAI,OAAO,GAAQ,WAAW,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3C,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACvC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,CACL,gCAAK,SAAS,EAAC,uCAAuC,YACpD,gCAAK,SAAS,EAAC,iEAAiE,GAAO,GACnF,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAC,kCAAkC,aAE/C,iCAAK,SAAS,EAAC,kFAAkF,aAC/F,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,uBAAC,uBAAQ,IAAC,SAAS,EAAC,uBAAuB,GAAG,EAC9C,+BAAI,SAAS,EAAC,qCAAqC,yBAAc,IAC7D,EACN,iCAAK,SAAS,EAAC,6BAA6B,aACzC,UAAU,KAAK,OAAO,IAAI,CACzB,iCAAK,SAAS,EAAC,4CAA4C,aACzD,uBAAC,0BAAW,IAAC,SAAS,EAAC,SAAS,GAAG,EACnC,iCAAM,SAAS,EAAC,SAAS,sBAAa,IAClC,CACP,EACA,UAAU,KAAK,OAAO,IAAI,CACzB,iCAAK,SAAS,EAAC,0CAA0C,aACvD,uBAAC,4BAAa,IAAC,SAAS,EAAC,SAAS,GAAG,EACrC,iCAAM,SAAS,EAAC,SAAS,6BAAoB,IACzC,CACP,EACA,UAAU,IAAI,CACb,6DACE,oCACE,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,SAAS,IAAI,UAAU,KAAK,QAAQ,EAC9C,SAAS,EAAC,qHAAqH,aAE/H,uBAAC,wBAAS,IAAC,SAAS,EAAC,qBAAqB,GAAG,aAEtC,EACT,mCACE,OAAO,EAAE,UAAU,EACnB,QAAQ,EAAE,SAAS,IAAI,UAAU,KAAK,QAAQ,EAC9C,SAAS,EAAC,mHAAmH,YAE5H,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,CACzB,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,gCAAK,SAAS,EAAC,4EAA4E,GAAO,EAClG,yDAAsB,IAClB,CACP,CAAC,CAAC,CAAC,CACF,6DACE,uBAAC,mBAAI,IAAC,SAAS,EAAC,qBAAqB,GAAG,YAEvC,CACJ,GACM,IACR,CACJ,EACD,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,EACrC,SAAS,EAAC,sFAAsF,YAEhG,uBAAC,gBAAC,IAAC,SAAS,EAAC,SAAS,GAAG,GAClB,IACL,IACF,EAGN,gCAAK,SAAS,EAAC,4BAA4B,YACzC,iCAAK,SAAS,EAAC,6BAA6B,aAE1C,iCAAK,SAAS,EAAC,mDAAmD,aAChE,+BAAI,SAAS,EAAC,0CAA0C,+BAAoB,EAC5E,iCAAK,SAAS,EAAC,WAAW,aACxB,4CACE,kCAAO,SAAS,EAAC,8CAA8C,yBAAiB,EAChF,oCACE,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,EACjC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACnE,SAAS,EAAC,0HAA0H,aAEpI,mCAAQ,KAAK,EAAC,QAAQ,uBAAgB,EACtC,mCAAQ,KAAK,EAAC,WAAW,0BAAmB,EAC5C,mCAAQ,KAAK,EAAC,UAAU,yBAAkB,IACnC,IACL,EACN,4CACE,kCAAO,SAAS,EAAC,8CAA8C,sBAAc,EAC7E,mCACE,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,EAC9B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAChE,SAAS,EAAC,0HAA0H,YAEnI,MAAM,CAAC,OAAO,CAAC,0BAAc,CAAC,aAAa,CAAC,GAAG,CAAC,QAAuC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CACvH,mCAAoB,KAAK,EAAE,KAAK,YAC7B,KAAK,IADK,KAAK,CAET,CACV,CAAC,GACK,IACL,EACN,4CACE,kCAAO,SAAS,EAAC,8CAA8C,wBAAgB,EAC/E,iCAAK,SAAS,EAAC,UAAU,aACvB,kCACE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,EACtC,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,MAAM,EAC/B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACjE,SAAS,EAAC,gIAAgI,EAC1I,WAAW,EAAC,oBAAoB,GAChC,EACF,mCACE,IAAI,EAAC,QAAQ,EACb,OAAO,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,EACzC,SAAS,EAAC,uFAAuF,YAEhG,UAAU,CAAC,CAAC,CAAC,uBAAC,qBAAM,IAAC,SAAS,EAAC,SAAS,GAAG,CAAC,CAAC,CAAC,uBAAC,kBAAG,IAAC,SAAS,EAAC,SAAS,GAAG,GACnE,IACL,IACF,EACN,iCAAK,SAAS,EAAC,wBAAwB,aACrC,4CACE,kCAAO,SAAS,EAAC,8CAA8C,2BAAmB,EAClF,kCACE,IAAI,EAAC,QAAQ,EACb,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,EAClC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAC9E,GAAG,EAAC,GAAG,EACP,GAAG,EAAC,OAAO,EACX,SAAS,EAAC,0HAA0H,GACpI,IACE,EACN,4CACE,kCAAO,SAAS,EAAC,8CAA8C,4BAAoB,EACnF,kCACE,IAAI,EAAC,QAAQ,EACb,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,EACpC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAClF,GAAG,EAAC,GAAG,EACP,GAAG,EAAC,GAAG,EACP,IAAI,EAAC,KAAK,EACV,SAAS,EAAC,0HAA0H,GACpI,IACE,IACF,IACF,IACF,EAGN,iCAAK,SAAS,EAAC,mDAAmD,aAChE,+BAAI,SAAS,EAAC,0CAA0C,0BAAe,EACvE,iCAAK,SAAS,EAAC,WAAW,aACxB,4CACE,kCAAO,SAAS,EAAC,8CAA8C,sBAAc,EAC7E,oCACE,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,KAAK,EAC7B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAC/D,SAAS,EAAC,0HAA0H,aAEpI,mCAAQ,KAAK,EAAC,QAAQ,uBAAgB,EACtC,mCAAQ,KAAK,EAAC,OAAO,sBAAe,EACpC,mCAAQ,KAAK,EAAC,MAAM,qBAAc,IAC3B,IACL,EACN,4CACE,kCAAO,SAAS,EAAC,8CAA8C,0BAAkB,EACjF,oCACE,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,QAAQ,EAChC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAClE,SAAS,EAAC,0HAA0H,aAEpI,mCAAQ,KAAK,EAAC,OAAO,sBAAe,EACpC,mCAAQ,KAAK,EAAC,QAAQ,uBAAgB,EACtC,mCAAQ,KAAK,EAAC,OAAO,sBAAe,IAC7B,IACL,EACN,iCAAK,SAAS,EAAC,mBAAmB,aAChC,kCACE,IAAI,EAAC,UAAU,EACf,EAAE,EAAC,aAAa,EAChB,OAAO,EAAE,aAAa,CAAC,EAAE,CAAC,WAAW,EACrC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EACvE,SAAS,EAAC,qFAAqF,GAC/F,EACF,kCAAO,OAAO,EAAC,aAAa,EAAC,SAAS,EAAC,4BAA4B,6BAE3D,IACJ,IACF,IACF,EAGN,iCAAK,SAAS,EAAC,mDAAmD,aAChE,+BAAI,SAAS,EAAC,0CAA0C,+BAAoB,EAC5E,iCAAK,SAAS,EAAC,WAAW,aACxB,4CACE,kCAAO,SAAS,EAAC,8CAA8C,+BAAuB,EACtF,oCACE,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,oBAAoB,EAC/C,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,4BAA4B,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACjF,SAAS,EAAC,0HAA0H,aAEpI,mCAAQ,KAAK,EAAC,SAAS,qCAA8B,EACrD,mCAAQ,KAAK,EAAC,MAAM,kDAA2C,IACxD,EACR,aAAa,CAAC,KAAK,CAAC,oBAAoB,KAAK,MAAM,IAAI,CACtD,gCAAK,SAAS,EAAC,oEAAoE,YACjF,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,uBAAC,4BAAa,IAAC,SAAS,EAAC,oCAAoC,GAAG,EAChE,8BAAG,SAAS,EAAC,sBAAsB,uGAE/B,IACA,GACF,CACP,IACG,EACN,iCAAK,SAAS,EAAC,mBAAmB,aAChC,kCACE,IAAI,EAAC,UAAU,EACf,EAAE,EAAC,UAAU,EACb,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC,qBAAqB,EAClD,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EACpF,SAAS,EAAC,qFAAqF,GAC/F,EACF,kCAAO,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,4BAA4B,wCAExD,IACJ,EACN,4CACE,kCAAO,SAAS,EAAC,8CAA8C,mCAA2B,EAC1F,kCACE,IAAI,EAAC,QAAQ,EACb,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,gBAAgB,EAC3C,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EACvF,GAAG,EAAC,MAAM,EACV,GAAG,EAAC,QAAQ,EACZ,SAAS,EAAC,0HAA0H,GACpI,EACF,8BAAG,SAAS,EAAC,4BAA4B,yEAErC,IACA,IACF,IACF,IACF,GACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,aAAa,CAAC;;;;;;;;;;;;;;ACjU7B,kFAAmD;AACnD,2GAAuD;AACvD,uHAA8C;AAO9C,MAAM,UAAU,GAA8B,CAAC,EAC7C,OAAO,EACP,QAAQ,GAAG,IAAI,EAChB,EAAE,EAAE;IACH,MAAM,EAAE,UAAU,EAAE,GAAG,0BAAW,GAAE,CAAC;IACrC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,oBAAQ,EAAC,IAAI,CAAC,CAAC;IAEjD,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,WAAW,EAAE,CAAC;QAChB,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEb,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,YAAY,CAAC,KAAK,CAAC,CAAC;QACpB,UAAU,CAAC,GAAG,EAAE;YACd,UAAU,EAAE,CAAC;QACf,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,gCAAK,SAAS,EAAC,qCAAqC,YAClD,iCAAK,SAAS,EAAC,2EAA2E,aACxF,iCAAK,SAAS,EAAC,4BAA4B,aACzC,uBAAC,0BAAW,IAAC,SAAS,EAAC,8BAA8B,GAAG,EACxD,4CACE,+BAAI,SAAS,EAAC,qBAAqB,sBAAW,EAC9C,8BAAG,SAAS,EAAC,8BAA8B,YACxC,OAAO,GACN,IACA,IACF,EAEN,mCACE,OAAO,EAAE,WAAW,EACpB,SAAS,EAAC,+EAA+E,YAEzF,uBAAC,gBAAC,IAAC,SAAS,EAAC,SAAS,GAAG,GAClB,IACL,GACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,UAAU,CAAC;;;;;;;;;;;;;;ACrD1B,MAAM,aAAa,GAAiC,CAAC,EACnD,OAAO,GAAG,8BAA8B,EACzC,EAAE,EAAE;IACH,OAAO,CACL,gCAAK,SAAS,EAAC,gEAAgE,YAC7E,iCAAK,SAAS,EAAC,aAAa,aAE1B,gCAAK,SAAS,EAAC,MAAM,YACnB,gCAAK,SAAS,EAAC,6HAA6H,YAC1I,gCAAK,SAAS,EAAC,oBAAoB,EAAC,IAAI,EAAC,cAAc,EAAC,OAAO,EAAC,WAAW,YACzE,iCAAM,CAAC,EAAC,+CAA+C,GAAG,GACtD,GACF,GACF,EAGN,gCAAK,SAAS,EAAC,MAAM,YACnB,gCAAK,SAAS,EAAC,mBAAmB,YAChC,gCAAK,SAAS,EAAC,+BAA+B,GAAO,GACjD,GACF,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,+BAAI,SAAS,EAAC,qCAAqC,qCAE9C,EACL,8BAAG,SAAS,EAAC,uBAAuB,YACjC,OAAO,GACN,IACA,EAGN,iCAAK,SAAS,EAAC,oCAAoC,aACjD,gCAAK,SAAS,EAAC,mDAAmD,GAAO,EACzE,gCAAK,SAAS,EAAC,mDAAmD,EAAC,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,GAAQ,EAC5G,gCAAK,SAAS,EAAC,mDAAmD,EAAC,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,GAAQ,IACxG,IACF,GACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,aAAa,CAAC;;;;;;;;;;;;;;ACjD7B,kFAAwC;AACxC,2GAAyE;AACzE,6HAAkE;AAClE,uHASsB;AAEtB,MAAM,OAAO,GAAa,GAAG,EAAE;IAC7B,MAAM,EACJ,aAAa,EACb,qBAAqB,EACrB,sBAAsB,EACtB,oBAAoB,EACpB,cAAc,EACd,WAAW,GACZ,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,EAAE,kBAAkB,EAAE,GAAG,qCAAgB,GAAE,CAAC;IAClD,MAAM,aAAa,GAAG,+BAAgB,GAAE,CAAC;IACzC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,oBAAQ,EAAgB,IAAI,CAAC,CAAC;IAElE,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,oBAAoB,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC,CAAC;IAEF,MAAM,wBAAwB,GAAG,KAAK,EAAE,EAAU,EAAE,CAAmB,EAAE,EAAE;QACzE,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,oDAAoD,CAAC,EAAE,CAAC;YAClE,MAAM,kBAAkB,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC;QACD,aAAa,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,IAAU,EAAE,EAAE;QAChC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEtD,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,WAAW,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO,GAAG,IAAI,WAAW,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,iCAAK,SAAS,EAAC,kCAAkC,aAE/C,gCAAK,SAAS,EAAC,8BAA8B,YAC3C,oCACE,OAAO,EAAE,aAAa,EACtB,SAAS,EAAC,0EAA0E,aAEpF,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,EAC5B,wDAAqB,IACd,GACL,EAGN,gCAAK,SAAS,EAAC,oCAAoC,YACjD,iCAAK,SAAS,EAAC,WAAW,aACxB,oCACE,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,EACrC,SAAS,EAAE,iGACT,WAAW,KAAK,MAAM;gCACpB,CAAC,CAAC,2BAA2B;gCAC7B,CAAC,CAAC,kDACN,EAAE,aAEF,uBAAC,4BAAa,IAAC,SAAS,EAAC,SAAS,GAAG,EACrC,oDAAiB,IACV,EAET,oCACE,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,EACzC,SAAS,EAAE,iGACT,WAAW,KAAK,UAAU;gCACxB,CAAC,CAAC,2BAA2B;gCAC7B,CAAC,CAAC,kDACN,EAAE,aAEF,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,EAChC,wDAAqB,IACd,EAET,oCACE,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,EACrC,SAAS,EAAE,iGACT,WAAW,KAAK,MAAM;gCACpB,CAAC,CAAC,2BAA2B;gCAC7B,CAAC,CAAC,kDACN,EAAE,aAEF,uBAAC,uBAAQ,IAAC,SAAS,EAAC,SAAS,GAAG,EAChC,oDAAiB,IACV,IACL,GACF,EAGN,gCAAK,SAAS,EAAC,oCAAoC,YACjD,gCAAK,SAAS,EAAE,oDACd,aAAa,KAAK,MAAM;wBACtB,CAAC,CAAC,2CAA2C;wBAC7C,CAAC,CAAC,6CACN,EAAE,YACC,aAAa,KAAK,MAAM,CAAC,CAAC,CAAC,CAC1B,6DACE,uBAAC,kBAAG,IAAC,SAAS,EAAC,yBAAyB,GAAG,EAC3C,iCAAM,SAAS,EAAC,qCAAqC,0BAAiB,IACrE,CACJ,CAAC,CAAC,CAAC,CACF,6DACE,uBAAC,qBAAM,IAAC,SAAS,EAAC,0BAA0B,GAAG,EAC/C,iCAAM,SAAS,EAAC,sCAAsC,6BAAoB,IACzE,CACJ,GACG,GACF,EAGN,gCAAK,SAAS,EAAC,wBAAwB,YACrC,iCAAK,SAAS,EAAC,WAAW,aACxB,+BAAI,SAAS,EAAC,gEAAgE,qCAEzE,EAEJ,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5B,iCAAK,SAAS,EAAC,kBAAkB,aAC/B,uBAAC,4BAAa,IAAC,SAAS,EAAC,oCAAoC,GAAG,EAChE,8BAAG,SAAS,EAAC,uBAAuB,qCAAyB,EAC7D,8BAAG,SAAS,EAAC,4BAA4B,0CAA8B,IACnE,CACP,CAAC,CAAC,CAAC,CACF,gCAAK,SAAS,EAAC,WAAW,YACvB,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,CACnC,iCAEE,SAAS,EAAE,8FACT,qBAAqB,KAAK,YAAY,CAAC,EAAE;oCACvC,CAAC,CAAC,2BAA2B;oCAC7B,CAAC,CAAC,iCACN,EAAE,EACF,OAAO,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,YAAY,CAAC,EAAE,CAAC,aAEtD,uBAAC,4BAAa,IAAC,SAAS,EAAC,uBAAuB,GAAG,EAEnD,iCAAK,SAAS,EAAC,gBAAgB,aAC7B,8BAAG,SAAS,EAAC,8BAA8B,YACxC,YAAY,CAAC,KAAK,GACjB,EACJ,+BAAG,SAAS,EAAC,8BAA8B,aACxC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,cAAK,YAAY,CAAC,QAAQ,CAAC,MAAM,iBAClE,IACA,EAEN,iCAAK,SAAS,EAAC,UAAU,aACvB,mCACE,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oDACb,CAAC,CAAC,eAAe,EAAE,CAAC;oDACpB,aAAa,CAAC,UAAU,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gDACzE,CAAC,EACD,SAAS,EAAC,gFAAgF,YAE1F,uBAAC,6BAAc,IAAC,SAAS,EAAC,SAAS,GAAG,GAC/B,EAER,UAAU,KAAK,YAAY,CAAC,EAAE,IAAI,CACjC,gCAAK,SAAS,EAAC,0CAA0C,YACvD,oCACE,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,wBAAwB,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAC5D,SAAS,EAAC,wEAAwE,aAElF,uBAAC,qBAAM,IAAC,SAAS,EAAC,cAAc,GAAG,cAE5B,GACL,CACP,IACG,KAzCD,YAAY,CAAC,EAAE,CA0ChB,CACP,CAAC,GACE,CACP,IACG,GACF,EAGN,gCAAK,SAAS,EAAC,8BAA8B,YAC3C,iCAAK,SAAS,EAAC,mCAAmC,aAChD,iEAA2B,EAC3B,8BAAG,SAAS,EAAC,MAAM,uBAAW,IAC1B,GACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,OAAO,CAAC;;;;;;;;;;;;;;AC/MvB,2GAAuD;AACvD,6HAAkF;AAClF,uHAAsF;AAEtF,MAAM,QAAQ,GAAa,GAAG,EAAE;IAC9B,MAAM,EACJ,WAAW,EACX,cAAc,EACd,eAAe,EACf,WAAW,EACX,YAAY,EACb,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,sCAAiB,GAAE,CAAC;IAC1D,MAAM,UAAU,GAAG,kCAAa,GAAE,CAAC;IAEnC,OAAO,CACL,iCAAK,SAAS,EAAC,0GAA0G,aAEvH,iCAAK,SAAS,EAAC,qCAAqC,aAClD,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,EAC3C,SAAS,EAAC,oDAAoD,EAC9D,KAAK,EAAC,gBAAgB,YAEtB,uBAAC,mBAAI,IAAC,SAAS,EAAC,uBAAuB,GAAG,GACnC,EAET,iCAAK,SAAS,EAAC,6BAA6B,aAC1C,gCAAK,SAAS,EAAC,oGAAoG,YACjH,gCAAK,SAAS,EAAC,oBAAoB,EAAC,IAAI,EAAC,cAAc,EAAC,OAAO,EAAC,WAAW,YACzE,iCAAM,CAAC,EAAC,+CAA+C,GAAG,GACtD,GACF,EACN,iCAAM,SAAS,EAAC,mCAAmC,6BAAoB,IACnE,IACF,EAGN,iCAAK,SAAS,EAAC,+DAA+D,aAC3E,UAAU,IAAI,CACb,iCAAK,SAAS,EAAC,+HAA+H,aAC5I,uBAAC,4BAAa,IAAC,SAAS,EAAC,SAAS,GAAG,EACrC,oDAAiB,IACb,CACP,EAEA,YAAY,IAAI,CACf,iCAAK,SAAS,EAAC,sDAAsD,aACnE,gCAAK,SAAS,EAAC,yBAAyB,GAAO,EAC/C,6DAA0B,IACtB,CACP,EAED,iCAAK,SAAS,EAAC,+CAA+C,aAC3D,WAAW,KAAK,MAAM,IAAI,MAAM,EAChC,WAAW,KAAK,UAAU,IAAI,UAAU,EACxC,WAAW,KAAK,MAAM,IAAI,aAAa,IACpC,IACF,EAGN,iCAAK,SAAS,EAAC,qCAAqC,aAClD,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,EACpC,SAAS,EAAC,oDAAoD,EAC9D,KAAK,EAAC,UAAU,YAEhB,uBAAC,uBAAQ,IAAC,SAAS,EAAC,uBAAuB,GAAG,GACvC,EAET,gCAAK,SAAS,EAAC,2BAA2B,GAAG,EAE7C,mCACE,OAAO,EAAE,QAAQ,EACjB,SAAS,EAAC,oDAAoD,EAC9D,KAAK,EAAC,UAAU,YAEhB,uBAAC,wBAAS,IAAC,SAAS,EAAC,uBAAuB,GAAG,GACxC,EAET,mCACE,OAAO,EAAE,QAAQ,EACjB,SAAS,EAAC,oDAAoD,EAC9D,KAAK,EAAC,UAAU,YAEhB,uBAAC,wBAAS,IAAC,SAAS,EAAC,uBAAuB,GAAG,GACxC,EAET,mCACE,OAAO,EAAE,KAAK,EACd,SAAS,EAAC,uEAAuE,EACjF,KAAK,EAAC,OAAO,YAEb,uBAAC,gBAAC,IAAC,SAAS,EAAC,uBAAuB,GAAG,GAChC,IACL,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,QAAQ,CAAC;;;;;;;;;;;;;;ACtGxB,kFAAyC;AACzC,uHAA4E;AAC5E,mHAAwD;AAcxD,MAAM,cAAc,GAAkC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC7E,OAAO,CACL,gCAAK,SAAS,EAAC,oCAAoC,YACjD,uBAAC,+BAAe,cACb,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CACrB,uBAAC,SAAS,IAAgB,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,IAA1C,KAAK,CAAC,EAAE,CAAsC,CAC/D,CAAC,GACc,GACd,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,SAAS,GAA+D,CAAC,EAC7E,KAAK,EACL,QAAQ,GACT,EAAE,EAAE;IACH,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACrB,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEnB,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEzC,MAAM,OAAO,GAAG,GAAG,EAAE;QACnB,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,SAAS;gBACZ,OAAO,uBAAC,0BAAW,IAAC,SAAS,EAAC,wBAAwB,GAAG,CAAC;YAC5D,KAAK,OAAO;gBACV,OAAO,uBAAC,sBAAO,IAAC,SAAS,EAAC,sBAAsB,GAAG,CAAC;YACtD,KAAK,SAAS;gBACZ,OAAO,uBAAC,4BAAa,IAAC,SAAS,EAAC,yBAAyB,GAAG,CAAC;YAC/D;gBACE,OAAO,uBAAC,mBAAI,IAAC,SAAS,EAAC,uBAAuB,GAAG,CAAC;QACtD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,SAAS;gBACZ,OAAO,+BAA+B,CAAC;YACzC,KAAK,OAAO;gBACV,OAAO,2BAA2B,CAAC;YACrC,KAAK,SAAS;gBACZ,OAAO,iCAAiC,CAAC;YAC3C;gBACE,OAAO,6BAA6B,CAAC;QACzC,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,uBAAC,sBAAM,CAAC,GAAG,IACT,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAC3C,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EACvC,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,EACvE,SAAS,EAAE,GAAG,kBAAkB,EAAE,uFAAuF,YAEzH,iCAAK,SAAS,EAAC,4BAA4B,aACzC,gCAAK,SAAS,EAAC,eAAe,YAAE,OAAO,EAAE,GAAO,EAChD,gCAAK,SAAS,EAAC,gBAAgB,YAC7B,8BAAG,SAAS,EAAC,uCAAuC,YAAE,KAAK,CAAC,OAAO,GAAK,GACpE,EACN,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EACjC,SAAS,EAAC,mEAAmE,YAE7E,uBAAC,gBAAC,IAAC,SAAS,EAAC,SAAS,GAAG,GAClB,IACL,GACK,CACd,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,cAAc,CAAC;;;;;;;;;;;;;;;;;AC3F9B,kFAAwC;AACxC,uHAAoE;AACpE,mHAAwD;AACxD,2GAAuD;AACvD,6HAA6D;AAC7D,gLAA8E;AAE9E,MAAM,iBAAiB,GAAa,GAAG,EAAE;IACvC,MAAM,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IACnC,MAAM,EAAE,cAAc,EAAE,GAAG,gCAAW,GAAE,CAAC;IACzC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAC5D,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAEtD,MAAM,UAAU,GAAG,QAAQ,EAAE,KAAK,EAAE,oBAAoB,KAAK,MAAM,CAAC;IAEpE,MAAM,mBAAmB,GAAG,KAAK,IAAI,EAAE;QACrC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QAEhD,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;YACvB,cAAc,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,CAAC;gBACnB,GAAG,QAAQ;gBACX,KAAK,EAAE;oBACL,GAAG,QAAQ,CAAC,KAAK;oBACjB,oBAAoB,EAAE,SAAS;iBAChC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;QACjC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,cAAc,CAAC;YACnB,GAAG,QAAQ;YACX,KAAK,EAAE;gBACL,GAAG,QAAQ,CAAC,KAAK;gBACjB,oBAAoB,EAAE,MAAM;aAC7B;SACF,CAAC,CAAC;QACH,cAAc,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;IAEF,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,CACL,6DACE,gCAAK,SAAS,EAAC,6BAA6B,YAC1C,oCACE,OAAO,EAAE,mBAAmB,EAC5B,SAAS,EAAC,6KAA6K,EACvL,KAAK,EAAC,qBAAqB,aAE3B,uBAAC,qBAAM,IAAC,SAAS,EAAC,SAAS,GAAG,EAC9B,iCAAM,SAAS,EAAC,qBAAqB,0BAAiB,IAC/C,GACL,EAEN,uBAAC,2BAAiB,IAChB,MAAM,EAAE,WAAW,EACnB,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EACpC,SAAS,EAAE,eAAe,EAC1B,QAAQ,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EACrC,KAAK,EAAC,kBAAkB,EACxB,OAAO,EAAC,6KAA6K,EACrL,IAAI,EAAC,SAAS,EACd,SAAS,EAAE,IAAI,EACf,WAAW,EAAC,kBAAkB,EAC9B,UAAU,EAAC,gBAAgB,EAC3B,OAAO,EAAE;;;;;;;2EAOwD,EACjE,WAAW,EAAE,IAAI,GACjB,IACD,CACJ,CAAC;IACJ,CAAC;IAED,OAAO,CACL,6DAEE,uBAAC,sBAAM,CAAC,GAAG,IACT,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EACnC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EACjC,SAAS,EAAC,6BAA6B,YAEvC,wBAAC,sBAAM,CAAC,MAAM,IACZ,OAAO,EAAE,mBAAmB,EAC5B,SAAS,EAAC,qKAAqK,EAC/K,KAAK,EAAC,qBAAqB,EAC3B,OAAO,EAAE;wBACP,SAAS,EAAE;4BACT,gCAAgC;4BAChC,kCAAkC;4BAClC,gCAAgC;yBACjC;qBACF,EACD,UAAU,EAAE;wBACV,QAAQ,EAAE,CAAC;wBACX,MAAM,EAAE,QAAQ;qBACjB,aAED,uBAAC,sBAAM,CAAC,GAAG,IACT,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EACpC,UAAU,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,YAE/D,uBAAC,4BAAa,IAAC,SAAS,EAAC,SAAS,GAAG,GAC1B,EACb,iCAAM,SAAS,EAAC,mBAAmB,0BAAiB,EACpD,uBAAC,kBAAG,IAAC,SAAS,EAAC,SAAS,GAAG,IACb,GACL,EAGb,4CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCP,GAAS,IACT,CACJ,CAAC;AACJ,CAAC,CAAC;AAEF,qBAAe,iBAAiB,CAAC;;;;;;;;;;;;;;AC9JjC,kFAAyD;AAEzD,2GAAuD;AAEvD,sCAAsC;AAC/B,MAAM,cAAc,GAAG,GAAG,EAAE;IACjC,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;IAE/B,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;IAC3F,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AARW,sBAAc,kBAQzB;AAEF,+BAA+B;AACxB,MAAM,WAAW,GAAG,GAAG,EAAE;IAC9B,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,0BAAW,GAAE,CAAC;IAC5D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAElD,MAAM,YAAY,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QAC1C,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YAC1C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,WAAW,CAAC,QAAQ,CAAC,IAAmB,CAAC,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,yBAAyB,CAAC,CAAC;YACvH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1C,MAAM,cAAc,GAAG,uBAAW,EAAC,KAAK,EAAE,OAA6B,EAAE,EAAE;QACzE,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,YAAY,EAAE,CAAC,CAAC,+BAA+B;YACvD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,2BAA2B,CAAC,CAAC;YACzH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE3C,MAAM,aAAa,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QAC3C,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC5C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,YAAY,EAAE,CAAC,CAAC,8BAA8B;YACtD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,0BAA0B,CAAC,CAAC;YACxH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE3C,OAAO;QACL,YAAY;QACZ,cAAc;QACd,aAAa;QACb,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AA3DW,mBAAW,eA2DtB;AAEF,mCAAmC;AAC5B,MAAM,gBAAgB,GAAG,GAAG,EAAE;IACnC,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IACrD,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAElD,MAAM,iBAAiB,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QAC/C,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAChD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,gBAAgB,CAAC,QAAQ,CAAC,IAAsB,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,8BAA8B,CAAC,CAAC;YAC5H,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEpD,MAAM,kBAAkB,GAAG,uBAAW,EAAC,KAAK,EAAE,KAAa,EAAE,EAAE;QAC7D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,iBAAiB,EAAE,CAAC,CAAC,uBAAuB;gBAClD,OAAO,QAAQ,CAAC,IAAoB,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,+BAA+B,CAAC,CAAC;gBAC3H,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAErD,MAAM,kBAAkB,GAAG,uBAAW,EAAC,KAAK,EAAE,EAAU,EAAE,EAAE;QAC1D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,iBAAiB,EAAE,CAAC,CAAC,uBAAuB;gBAClD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,+BAA+B,CAAC,CAAC;gBAC3H,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAErD,OAAO;QACL,iBAAiB;QACjB,kBAAkB;QAClB,kBAAkB;QAClB,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AA3DW,wBAAgB,oBA2D3B;AAEF,8BAA8B;AACvB,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IAClD,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAElD,MAAM,cAAc,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QAC5C,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACzC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,aAAa,CAAC,QAAQ,CAAC,IAAkB,CAAC,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,mCAAmC,CAAC,CAAC;YACjI,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1C,OAAO;QACL,cAAc;QACd,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AAzBW,qBAAa,iBAyBxB;AAEF,6BAA6B;AACtB,MAAM,MAAM,GAAG,GAAG,EAAE;IACzB,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EACJ,YAAY,EACZ,mBAAmB,EACnB,sBAAsB,EACtB,qBAAqB,EACrB,QAAQ,EACT,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,WAAW,GAAG,uBAAW,EAAC,KAAK,EACnC,QAAe,EACf,YAAqB,EACG,EAAE;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;YAChE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,wBAAwB,CAAC,CAAC;gBACpH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IAExB,MAAM,aAAa,GAAG,uBAAW,EAAC,KAAK,EACrC,QAAe,EACf,YAAqB,EACrB,OAAiC,EAClB,EAAE;QACjB,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,qBAAqB,EAAE,CAAC;QAExB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;YAClE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,4BAA4B;gBAC5B,mFAAmF;gBACnF,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,0BAA0B,CAAC,CAAC;YACxH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE7D,OAAO;QACL,WAAW;QACX,aAAa;KACd,CAAC;AACJ,CAAC,CAAC;AAxDW,cAAM,UAwDjB;AAEF,4BAA4B;AACrB,MAAM,QAAQ,GAAG,GAAG,EAAE;IAC3B,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EACJ,cAAc,EACd,aAAa,EACb,aAAa,EACb,QAAQ,EACT,GAAG,0BAAW,GAAE,CAAC;IAElB,MAAM,UAAU,GAAG,uBAAW,EAAC,KAAK,EAAE,cAAsB,EAAE,aAAkC,EAAE,EAAE;QAClG,aAAa,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,aAAa,EAAE,CAAC,CAAC;YACzE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,uBAAuB,CAAC,CAAC;gBACnH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,aAAa,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,cAAc,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEzD,MAAM,WAAW,GAAG,uBAAW,EAAC,KAAK,EAAE,cAAsB,EAAE,MAAe,EAAE,EAAE;QAChF,aAAa,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC;YACrE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAuB,CAAC;gBACtD,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;gBACnD,CAAC;gBACD,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,wBAAwB,CAAC,CAAC;gBACpH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,aAAa,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IAExD,MAAM,gBAAgB,GAAG,uBAAW,EAAC,KAAK,EAAE,cAAsB,EAAE,QAAiB,EAAE,EAAE;QACvF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,CAAC;YACvE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,6BAA6B,CAAC,CAAC;gBACzH,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1B,OAAO;QACL,UAAU;QACV,WAAW;QACX,gBAAgB;KACjB,CAAC;AACJ,CAAC,CAAC;AAtEW,gBAAQ,YAsEnB;AAEF,iBAAiB;AACV,MAAM,QAAQ,GAAG,GAAG,EAAE;IAC3B,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IACnC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,oBAAQ,EAAQ,EAAE,CAAC,CAAC;IAEhE,MAAM,SAAS,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QACvC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,sBAAsB,CAAC,CAAC;YACpH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1B,MAAM,WAAW,GAAG,uBAAW,EAAC,KAAK,EAAE,IAAY,EAAE,IAAyB,EAAE,EAAE;QAChF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACpE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,QAAQ,CAAC,IAAkB,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,uBAAuB,CAAC,CAAC;gBACnH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1B,qBAAS,EAAC,GAAG,EAAE;QACb,SAAS,EAAE,CAAC;IACd,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAEhB,OAAO;QACL,cAAc;QACd,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC,CAAC;AA1CW,gBAAQ,YA0CnB;AAEF,kCAAkC;AAC3B,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IAEnC,MAAM,QAAQ,GAAG,uBAAW,EAAC,KAAK,EAAE,IAAY,EAAE,EAAE;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,qBAAqB,CAAC,CAAC;gBACjH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEvB,MAAM,SAAS,GAAG,uBAAW,EAAC,KAAK,EAAE,IAAY,EAAE,OAAe,EAAE,EAAE;QACpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;YACvD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,sBAAsB,CAAC,CAAC;gBAClH,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEvB,MAAM,aAAa,GAAG,uBAAW,EAAC,KAAK,EAAE,IAAY,EAAE,EAAE;QACvD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,0BAA0B,CAAC,CAAC;gBACtH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEvB,OAAO;QACL,QAAQ;QACR,SAAS;QACT,aAAa;KACd,CAAC;AACJ,CAAC,CAAC;AAtDW,qBAAa,iBAsDxB;AAEF,2BAA2B;AACpB,MAAM,iBAAiB,GAAG,GAAG,EAAE;IACpC,MAAM,GAAG,GAAG,0BAAc,GAAE,CAAC;IAE7B,MAAM,QAAQ,GAAG,uBAAW,EAAC,GAAG,EAAE;QAChC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjB,MAAM,QAAQ,GAAG,uBAAW,EAAC,GAAG,EAAE;QAChC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjB,MAAM,KAAK,GAAG,uBAAW,EAAC,GAAG,EAAE;QAC7B,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjB,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,KAAK;KACN,CAAC;AACJ,CAAC,CAAC;AApBW,yBAAiB,qBAoB5B;AAEF,+BAA+B;AACxB,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IACnC,OAAO,QAAQ,EAAE,KAAK,EAAE,oBAAoB,KAAK,MAAM,CAAC;AAC1D,CAAC,CAAC;AAHW,qBAAa,iBAGxB;AAEF,8BAA8B;AACvB,MAAM,oBAAoB,GAAG,GAAG,EAAE;IACvC,MAAM,EAAE,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,0BAAW,GAAE,CAAC;IACzE,MAAM,EAAE,kBAAkB,EAAE,GAAG,4BAAgB,GAAE,CAAC;IAClD,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,6BAAiB,GAAE,CAAC;IAE1D,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,aAAa,GAAG,CAAC,KAAoB,EAAE,EAAE;YAC7C,iCAAiC;YACjC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YACzC,CAAC;YAED,8BAA8B;YAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,sBAAW,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC;YACxD,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,sBAAW,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;YACtD,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,KAAK,EAAE,CAAC;YACV,CAAC;YAED,gCAAgC;YAChC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,QAAQ,EAAE,CAAC;YACb,CAAC;YAED,uBAAuB;YACvB,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,EAAE,CAAC;gBACxB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,QAAQ,EAAE,CAAC;YACb,CAAC;YAED,gCAAgC;YAChC,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC3B,IAAI,YAAY,EAAE,CAAC;oBACjB,sBAAW,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAClD,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC,EAAE,CAAC,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC;AAtDW,4BAAoB,wBAsD/B;AAEF,sCAAsC;AAC/B,MAAM,mBAAmB,GAAG,GAAG,EAAE;IACtC,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,0BAAW,GAAE,CAAC;IACxD,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,oBAAQ,EAAC,EAAE,CAAC,CAAC;IAEzD,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,WAAW,IAAI,gBAAgB,EAAE,CAAC;YACpC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACxB,iBAAiB,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEpC,OAAO;QACL,WAAW;QACX,cAAc;KACf,CAAC;AACJ,CAAC,CAAC;AAhBW,2BAAmB,uBAgB9B;AAEF,wBAAwB;AACjB,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,oBAAQ,EAI9B,IAAI,CAAC,CAAC;IAEhB,MAAM,QAAQ,GAAG,uBAAW,EAAC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,EAAE;QACxF,WAAW,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;IACpD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,QAAQ,GAAG,uBAAW,EAAC,GAAG,EAAE;QAChC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,QAAQ;KACT,CAAC;AACJ,CAAC,CAAC;AApBW,qBAAa,iBAoBxB;AAEF,+BAA+B;AACxB,MAAM,QAAQ,GAAG,GAAG,EAAE;IAC3B,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,oBAAQ,EAKhC,EAAE,CAAC,CAAC;IAER,MAAM,SAAS,GAAG,uBAAW,EAAC,CAC5B,IAA8C,EAC9C,OAAe,EACf,QAAQ,GAAG,IAAI,EACf,EAAE;QACF,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;QAE9C,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAEpC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,UAAU,CAAC,GAAG,EAAE;gBACd,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC,EAAE,QAAQ,CAAC,CAAC;QACf,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,SAAS,GAAG,uBAAW,EAAC,CAAC,EAAU,EAAE,EAAE;QAC3C,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,WAAW,GAAG,uBAAW,EAAC,GAAG,EAAE;QACnC,SAAS,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,MAAM;QACN,SAAS;QACT,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC,CAAC;AAvCW,gBAAQ,YAuCnB;;;;;;;;;;;;;;ACzjBF,kFAAiE;AACjE,2GAAuD;AAEvD,4BAA4B;AACrB,MAAM,WAAW,GAAG,CAAI,KAAQ,EAAE,KAAa,EAAK,EAAE;IAC3D,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,oBAAQ,EAAI,KAAK,CAAC,CAAC;IAE/D,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9B,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,OAAO,GAAG,EAAE;YACV,YAAY,CAAC,OAAO,CAAC,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IAEnB,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAdW,mBAAW,eActB;AAEF,yBAAyB;AAClB,MAAM,eAAe,GAAG,CAC7B,GAAW,EACX,YAAe,EAC4B,EAAE;IAC7C,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,oBAAQ,EAAI,GAAG,EAAE;QACrD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,uBAAW,EAAC,CAAC,KAA0B,EAAE,EAAE;QAC1D,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5E,cAAc,CAAC,YAAY,CAAC,CAAC;YAC7B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC;IAEvB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACjC,CAAC,CAAC;AAzBW,uBAAe,mBAyB1B;AAEF,gCAAgC;AACzB,MAAM,YAAY,GAAG,GAAG,EAAE;IAC/B,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,oBAAQ,EAAC,KAAK,CAAC,CAAC;IAE5C,MAAM,eAAe,GAAG,uBAAW,EAAC,KAAK,EAAE,IAAY,EAAE,EAAE;QACzD,IAAI,CAAC;YACH,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1C,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,iBAAiB,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QAC/C,IAAI,CAAC;YACH,OAAO,MAAM,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,MAAM;QACN,eAAe;QACf,iBAAiB;KAClB,CAAC;AACJ,CAAC,CAAC;AA7BW,oBAAY,gBA6BvB;AAEF,iCAAiC;AAC1B,MAAM,eAAe,GAAG,GAAG,EAAE;IAClC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,oBAAQ,EAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAE3D,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAE/C,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAChD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAElD,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACnD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACvD,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAjBW,uBAAe,mBAiB1B;AAEF,uBAAuB;AAChB,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,oBAAQ,EAAC;QAC3C,KAAK,EAAE,MAAM,CAAC,UAAU;QACxB,MAAM,EAAE,MAAM,CAAC,WAAW;KAC3B,CAAC,CAAC;IAEH,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,aAAa,CAAC;gBACZ,KAAK,EAAE,MAAM,CAAC,UAAU;gBACxB,MAAM,EAAE,MAAM,CAAC,WAAW;aAC3B,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAChD,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAClE,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAnBW,qBAAa,iBAmBxB;AAEF,0BAA0B;AACnB,MAAM,WAAW,GAAG,CAAI,KAAQ,EAAiB,EAAE;IACxD,MAAM,GAAG,GAAG,kBAAM,GAAK,CAAC;IAExB,qBAAS,EAAC,GAAG,EAAE;QACb,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC,OAAO,CAAC;AACrB,CAAC,CAAC;AARW,mBAAW,eAQtB;AAEF,oBAAoB;AACb,MAAM,WAAW,GAAG,CAAC,QAAoB,EAAE,KAAoB,EAAE,EAAE;IACxE,MAAM,aAAa,GAAG,kBAAM,EAAC,QAAQ,CAAC,CAAC;IAEvC,qBAAS,EAAC,GAAG,EAAE;QACb,aAAa,CAAC,OAAO,GAAG,QAAQ,CAAC;IACnC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO;QAE3B,MAAM,EAAE,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACd,CAAC,CAAC;AAbW,mBAAW,eAatB;AAEF,mBAAmB;AACZ,MAAM,UAAU,GAAG,CAAC,QAAoB,EAAE,KAAoB,EAAE,EAAE;IACvE,MAAM,aAAa,GAAG,kBAAM,EAAC,QAAQ,CAAC,CAAC;IAEvC,qBAAS,EAAC,GAAG,EAAE;QACb,aAAa,CAAC,OAAO,GAAG,QAAQ,CAAC;IACnC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO;QAE3B,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5D,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACd,CAAC,CAAC;AAbW,kBAAU,cAarB;AAEF,2BAA2B;AACpB,MAAM,QAAQ,GAAG,CACtB,aAA+B,EAC/B,SAAS,GAAG,IAAI,EAChB,EAAE;IACF,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,oBAAQ,EAA2C,MAAM,CAAC,CAAC;IACvF,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,oBAAQ,EAAW,IAAI,CAAC,CAAC;IACnD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,oBAAQ,EAAW,IAAI,CAAC,CAAC;IAEnD,MAAM,OAAO,GAAG,uBAAW,EAAC,KAAK,IAAI,EAAE;QACrC,SAAS,CAAC,SAAS,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,aAAa,EAAE,CAAC;YACvC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,SAAS,CAAC,SAAS,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,KAAK,CAAC,CAAC;YAChB,SAAS,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC;IACH,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;IAEpB,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;IAEzB,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC3C,CAAC,CAAC;AA9BW,gBAAQ,YA8BnB;AAEF,2BAA2B;AACpB,MAAM,QAAQ,GAAG,GAAG,EAAE;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,0BAAW,GAAE,CAAC;IACnC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,oBAAQ,EAAmB,MAAM,CAAC,CAAC;IAEzE,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,8BAA8B,CAAC,CAAC;QACrE,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEtD,MAAM,YAAY,GAAG,CAAC,CAAsB,EAAE,EAAE;YAC9C,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC,CAAC;QAEF,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACpD,OAAO,GAAG,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,MAAM,CAAC;IAEpG,OAAO;QACL,KAAK,EAAE,YAAY;QACnB,WAAW;QACX,MAAM,EAAE,YAAY,KAAK,MAAM;KAChC,CAAC;AACJ,CAAC,CAAC;AAvBW,gBAAQ,YAuBnB;AAEF,sBAAsB;AACf,MAAM,YAAY,GAAG,CAAC,QAAiB,EAAE,EAAE;IAChD,MAAM,YAAY,GAAG,kBAAM,EAAc,IAAI,CAAC,CAAC;IAE/C,qBAAS,EAAC,GAAG,EAAE;QACb,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,OAAO;YAAE,OAAO;QAE/C,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;QACvC,MAAM,iBAAiB,GAAG,SAAS,CAAC,gBAAgB,CAClD,0EAA0E,CAC3E,CAAC;QACF,MAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAgB,CAAC;QACzD,MAAM,WAAW,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAgB,CAAC;QAEnF,MAAM,YAAY,GAAG,CAAC,CAAgB,EAAE,EAAE;YACxC,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK;gBAAE,OAAO;YAE5B,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACf,IAAI,QAAQ,CAAC,aAAa,KAAK,YAAY,EAAE,CAAC;oBAC5C,WAAW,EAAE,KAAK,EAAE,CAAC;oBACrB,CAAC,CAAC,cAAc,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,QAAQ,CAAC,aAAa,KAAK,WAAW,EAAE,CAAC;oBAC3C,YAAY,EAAE,KAAK,EAAE,CAAC;oBACtB,CAAC,CAAC,cAAc,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACpD,YAAY,EAAE,KAAK,EAAE,CAAC;QAEtB,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAtCW,oBAAY,gBAsCvB;AAEF,mCAAmC;AAC5B,MAAM,eAAe,GAAG,CAAC,QAAoB,EAAE,EAAE;IACtD,MAAM,GAAG,GAAG,kBAAM,EAAc,IAAI,CAAC,CAAC;IAEtC,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,CAAC,KAAiB,EAAE,EAAE;YACxC,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE,CAAC;gBAC/D,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC;QAEF,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QACpD,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAfW,uBAAe,mBAe1B;AAEF,2BAA2B;AACpB,MAAM,iBAAiB,GAAG,GAAG,EAAE;IACpC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,oBAAQ,EAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAErE,qBAAS,EAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,iBAAiB,CAAC;gBAChB,CAAC,EAAE,MAAM,CAAC,OAAO;gBACjB,CAAC,EAAE,MAAM,CAAC,OAAO;aAClB,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAChD,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAClE,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAhBW,yBAAiB,qBAgB5B;;;;;;;;;;;;;;;;;AC5SF,mGAA0B;AAC1B,mGAA8C;AAC9C,0FAAwB;AACxB,qFAA8B;AAE9B,iCAAiC;AACjC,MAAM,aAAc,SAAQ,eAAK,CAAC,SAGjC;IACC,YAAY,KAAoC;QAC9C,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,wBAAwB,CAAC,KAAY;QAC1C,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACnC,CAAC;IAED,iBAAiB,CAAC,KAAY,EAAE,SAA0B;QACxD,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAC3E,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,CACL,gCAAK,SAAS,EAAC,+DAA+D,YAC5E,iCAAK,SAAS,EAAC,mEAAmE,aAChF,iCAAK,SAAS,EAAC,kCAAkC,aAC/C,gCAAK,SAAS,EAAC,qEAAqE,YAClF,gCAAK,SAAS,EAAC,oBAAoB,EAAC,IAAI,EAAC,cAAc,EAAC,OAAO,EAAC,WAAW,YACzE,iCAAM,QAAQ,EAAC,SAAS,EAAC,CAAC,EAAC,mHAAmH,EAAC,QAAQ,EAAC,SAAS,GAAG,GAChK,GACF,EACN,+BAAI,SAAS,EAAC,qCAAqC,kCAAuB,IACtE,EAEN,8BAAG,SAAS,EAAC,oBAAoB,wGAE7B,EAEH,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CACnB,qCAAS,SAAS,EAAC,qDAAqD,aACtE,oCAAS,SAAS,EAAC,kDAAkD,8BAE3D,EACV,iCAAK,SAAS,EAAC,4CAA4C,aACxD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EACxB,MAAM,EACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IACnB,IACE,CACX,EAED,iCAAK,SAAS,EAAC,gBAAgB,aAC7B,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EACvC,SAAS,EAAC,wBAAwB,2BAG3B,EACT,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,EACnE,SAAS,EAAC,0BAA0B,0BAG7B,IACL,IACF,GACF,CACP,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B,CAAC;CACF;AAED,2BAA2B;AAC3B,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAClD,IAAI,CAAC,SAAS,EAAE,CAAC;IACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,IAAI,GAAG,uBAAU,EAAC,SAAS,CAAC,CAAC;AAEnC,IAAI,CAAC,MAAM,CACT,uBAAC,eAAK,CAAC,UAAU,cACf,uBAAC,aAAa,cACZ,uBAAC,aAAG,KAAG,GACO,GACC,CACpB,CAAC;;;;;;;;;;;;;;AC3FF,4FAAiC;AACjC,+GAA2D;AAgG3D,MAAM,YAAY,GAAa;IAC7B,WAAW,EAAE,IAAI;IACjB,YAAY,EAAE,KAAK;IACnB,WAAW,EAAE,MAAM;IACnB,KAAK,EAAE,QAAQ;IACf,OAAO,EAAE,KAAK;IAEd,QAAQ,EAAE,IAAI;IAEd,aAAa,EAAE,EAAE;IACjB,qBAAqB,EAAE,IAAI;IAC3B,mBAAmB,EAAE,IAAI;IAEzB,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,KAAK;IACnB,WAAW,EAAE,IAAI;IACjB,WAAW,EAAE,EAAE;IAEf,UAAU,EAAE,IAAI;IAEhB,KAAK,EAAE,IAAI;IAEX,gBAAgB,EAAE,EAAE;IACpB,WAAW,EAAE,KAAK;CACnB,CAAC;AAEW,mBAAW,GAAG,oBAAM,GAAyB,CACxD,sCAAqB,EAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IACnC,GAAG,YAAY;IAEf,aAAa;IACb,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACpD,eAAe,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;IACtD,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACpD,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;IACnC,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC;IAEzC,mBAAmB;IACnB,WAAW,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC;IAC5C,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE;QAC1B,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;QACvC,IAAI,eAAe,EAAE,CAAC;YACpB,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,gBAAgB,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,CAAC;IAC3D,eAAe,EAAE,CAAC,YAAY,EAAE,EAAE;QAChC,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC;QAC1C,GAAG,CAAC;YACF,aAAa,EAAE,CAAC,YAAY,EAAE,GAAG,aAAa,CAAC;YAC/C,qBAAqB,EAAE,YAAY,CAAC,EAAE;YACtC,mBAAmB,EAAE,YAAY;SAClC,CAAC,CAAC;IACL,CAAC;IACD,kBAAkB,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE;QAClC,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC;QAC1C,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACpD,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAChD,CAAC;QACF,GAAG,CAAC,EAAE,aAAa,EAAE,oBAAoB,EAAE,CAAC,CAAC;QAE7C,4DAA4D;QAC5D,IAAI,GAAG,EAAE,CAAC,qBAAqB,KAAK,EAAE,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YACzE,GAAG,CAAC,EAAE,mBAAmB,EAAE,cAAc,IAAI,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IACD,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE;QACzB,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC;QAC1C,MAAM,qBAAqB,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC3E,GAAG,CAAC,EAAE,aAAa,EAAE,qBAAqB,EAAE,CAAC,CAAC;QAE9C,+CAA+C;QAC/C,IAAI,GAAG,EAAE,CAAC,qBAAqB,KAAK,EAAE,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,qBAAqB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;YACpD,GAAG,CAAC;gBACF,qBAAqB,EAAE,UAAU,EAAE,EAAE,IAAI,IAAI;gBAC7C,mBAAmB,EAAE,UAAU;aAChC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,sBAAsB,EAAE,CAAC,EAAE,EAAE,EAAE;QAC7B,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;YAChB,GAAG,CAAC,EAAE,qBAAqB,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC;YAChE,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QACtE,GAAG,CAAC;YACF,qBAAqB,EAAE,EAAE;YACzB,mBAAmB,EAAE,YAAY,IAAI,IAAI;SAC1C,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,UAAU,EAAE,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE;QACtC,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC;QAC1C,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpD,IAAI,IAAI,CAAC,EAAE,KAAK,cAAc,EAAE,CAAC;gBAC/B,OAAO;oBACL,GAAG,IAAI;oBACP,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;oBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,EAAE,aAAa,EAAE,oBAAoB,EAAE,CAAC,CAAC;QAE7C,qDAAqD;QACrD,IAAI,GAAG,EAAE,CAAC,qBAAqB,KAAK,cAAc,EAAE,CAAC;YACnD,MAAM,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;YACrF,GAAG,CAAC,EAAE,mBAAmB,EAAE,cAAc,IAAI,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IACD,aAAa,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;QACpD,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,aAAa,CAAC;QAC1C,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpD,IAAI,IAAI,CAAC,EAAE,KAAK,cAAc,EAAE,CAAC;gBAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC9C,GAAG,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CACpD,CAAC;gBACF,OAAO,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;YAChD,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,EAAE,aAAa,EAAE,oBAAoB,EAAE,CAAC,CAAC;QAE7C,qDAAqD;QACrD,IAAI,GAAG,EAAE,CAAC,qBAAqB,KAAK,cAAc,EAAE,CAAC;YACnD,MAAM,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;YACrF,GAAG,CAAC,EAAE,mBAAmB,EAAE,cAAc,IAAI,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,gBAAgB;IAChB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;IACpD,aAAa,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;IAChE,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;IACpD,aAAa,EAAE,CAAC,MAAM,EAAE,EAAE;QACxB,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC;QACtC,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC,GAAG,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;IACjD,CAAC;IACD,gBAAgB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;IAChD,WAAW,EAAE,CAAC,MAAM,EAAE,EAAE;QACtB,0CAA0C;QAC1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QACvC,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,UAAU,EAAE,wBAAwB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IACnH,CAAC;IACD,UAAU,EAAE,CAAC,MAAM,EAAE,EAAE;QACrB,uCAAuC;QACvC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QACtC,GAAG,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,UAAU,EAAE,wBAAwB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IACnH,CAAC;IACD,cAAc,EAAE,GAAG,EAAE;QACnB,wCAAwC;QACxC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,GAAG,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,iBAAiB;IACjB,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;IAElD,gBAAgB;IAChB,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;IACnC,UAAU,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAEtC,oBAAoB;IACpB,mBAAmB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC;IACpE,sBAAsB,EAAE,CAAC,KAAK,EAAE,EAAE;QAChC,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC,gBAAgB,CAAC;QACvC,GAAG,CAAC,EAAE,gBAAgB,EAAE,OAAO,GAAG,KAAK,EAAE,CAAC,CAAC;IAC7C,CAAC;IACD,YAAY,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IAC5D,qBAAqB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC;IAE1D,oBAAoB;IACpB,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE;QAC9B,MAAM,eAAe,GAAiB;YACpC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,KAAK;YACL,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,GAAG,EAAE,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC;IACD,WAAW,EAAE,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE;QACvC,MAAM,OAAO,GAAY;YACvB,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,IAAI,EAAE,MAAM;YACZ,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,GAAG,EAAE,CAAC,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAE1C,kDAAkD;QAClD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IACD,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;CAC/B,CAAC,CAAC,CACJ,CAAC;AAEF,gCAAgC;AACzB,MAAM,kBAAkB,GAAG,GAAG,EAAE,CACrC,uBAAW,EAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;AADrD,0BAAkB,sBACmC;AAE3D,MAAM,wBAAwB,GAAG,GAAG,EAAE,CAC3C,uBAAW,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;AADvC,gCAAwB,4BACe;AAE7C,MAAM,gBAAgB,GAAG,GAAG,EAAE,CACnC,uBAAW,EAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,oBAAoB,IAAI,SAAS,CAAC,CAAC;AADnE,wBAAgB,oBACmD;AAEzE,MAAM,aAAa,GAAG,GAAG,EAAE,CAChC,uBAAW,EAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,oBAAoB,KAAK,MAAM,CAAC,CAAC;AADjE,qBAAa,iBACoD;AAEvE,MAAM,WAAW,GAAG,GAAG,EAAE,CAC9B,uBAAW,EAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC;AADpD,mBAAW,eACyC;AAE1D,MAAM,QAAQ,GAAG,GAAG,EAAE,CAC3B,uBAAW,EAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC;AADhD,gBAAQ,YACwC;AAE7D,iCAAiC;AAC1B,MAAM,uBAAuB,GAAG,CAAC,QAAiC,EAAE,EAAE,CAC3E,mBAAW,CAAC,SAAS,CACnB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,EACtB,QAAQ,CACT,CAAC;AAJS,+BAAuB,2BAIhC;AAEG,MAAM,8BAA8B,GAAG,CAAC,QAAqD,EAAE,EAAE,CACtG,mBAAW,CAAC,SAAS,CACnB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,mBAAmB,EACpC,QAAQ,CACT,CAAC;AAJS,sCAA8B,kCAIvC;AAEG,MAAM,0BAA0B,GAAG,CAAC,QAAgD,EAAE,EAAE,CAC7F,mBAAW,CAAC,SAAS,CACnB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,EACzB,QAAQ,CACT,CAAC;AAJS,kCAA0B,8BAInC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrVJ,MAAqG;AACrG,MAA2F;AAC3F,MAAkG;AAClG,MAAqH;AACrH,MAA8G;AAC9G,MAA8G;AAC9G,MAA4J;AAC5J;AACA;;AAEA;;AAEA,4BAA4B,qGAAmB;AAC/C,wBAAwB,kHAAa;;AAErC,uBAAuB,uGAAa;AACpC;AACA,iBAAiB,+FAAM;AACvB,6BAA6B,sGAAkB;;AAE/C,aAAa,0GAAG,CAAC,gIAAO;;;;AAIsG;AAC9H,OAAO,iEAAe,gIAAO,IAAI,gIAAO,UAAU,gIAAO,mBAAmB,EAAC;;;;;;;;;;;;;;ACxB7E,wBAAwB;AACX,gBAAQ,GAAG,cAAc,CAAC;AAC1B,mBAAW,GAAG,OAAO,CAAC;AACtB,uBAAe,GAAG,6CAA6C,CAAC;AAE7E,qBAAqB;AACR,oBAAY,GAAG;IAC1B,aAAa,EAAE,iBAAiB;IAChC,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,2BAA2B;IACjE,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,yBAAyB;IACpE,oBAAoB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,0BAA0B;IAC1E,cAAc,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,iBAAiB;IACpD,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,yBAAyB;CACpE,CAAC;AAEF,yBAAyB;AACZ,qBAAa,GAAG;IAC3B,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;CACZ,CAAC;AAEX,mCAAmC;AACtB,sBAAc,GAAG;IAC5B,CAAC,qBAAa,CAAC,MAAM,CAAC,EAAE;QACtB,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,aAAa;QAC5B,eAAe,EAAE,eAAe;KACjC;IACD,CAAC,qBAAa,CAAC,SAAS,CAAC,EAAE;QACzB,wBAAwB,EAAE,eAAe;QACzC,0BAA0B,EAAE,iBAAiB;QAC7C,yBAAyB,EAAE,gBAAgB;KAC5C;IACD,CAAC,qBAAa,CAAC,QAAQ,CAAC,EAAE;QACxB,eAAe,EAAE,eAAe;QAChC,gBAAgB,EAAE,gBAAgB;KACnC;CACO,CAAC;AAEX,gBAAgB;AACH,qBAAa,GAAG;IAC3B,CAAC,qBAAa,CAAC,MAAM,CAAC,EAAE,2BAA2B;IACnD,CAAC,qBAAa,CAAC,SAAS,CAAC,EAAE,8BAA8B;IACzD,CAAC,qBAAa,CAAC,QAAQ,CAAC,EAAE,6BAA6B;CAC/C,CAAC;AAEX,oCAAoC;AACvB,oBAAY,GAAG;IAC1B,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,IAAI;IACrB,wBAAwB,EAAE,MAAM;IAChC,0BAA0B,EAAE,MAAM;IAClC,yBAAyB,EAAE,MAAM;IACjC,eAAe,EAAE,KAAK;IACtB,gBAAgB,EAAE,KAAK;CACf,CAAC;AAEX,eAAe;AACF,oBAAY,GAAG;IAC1B,aAAa,EAAE,GAAG;IAClB,uBAAuB,EAAE,EAAE;IAC3B,aAAa,EAAE,EAAE;IACjB,iBAAiB,EAAE,GAAG;IACtB,kBAAkB,EAAE,GAAG;IACvB,cAAc,EAAE,GAAG;IACnB,cAAc,EAAE,IAAI;IACpB,sBAAsB,EAAE,IAAI;CACpB,CAAC;AAEX,qBAAqB;AACR,0BAAkB,GAAG;IAChC,gBAAgB,EAAE,QAAQ;IAC1B,cAAc,EAAE,QAAQ;IACxB,eAAe,EAAE,QAAQ;IACzB,WAAW,EAAE,YAAY;IACzB,WAAW,EAAE,QAAQ;IACrB,iBAAiB,EAAE,KAAK;IACxB,eAAe,EAAE,KAAK;IACtB,UAAU,EAAE,cAAc;CAClB,CAAC;AAEX,wBAAwB;AACX,uBAAe,GAAG;IAC7B,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,MAAM;CACJ,CAAC;AAEX,kBAAkB;AACL,uBAAe,GAAG;IAC7B,WAAW,EAAE,aAAa;IAC1B,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;CACR,CAAC;AAEX,qBAAqB;AACR,kBAAU,GAAG;IACxB,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAC/E,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;IACjG,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC5C,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1C,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAC1D,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;IAC9D,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;CACzC,CAAC;AAEX,+BAA+B;AAClB,wBAAgB,GAAgB;IAC3C,GAAG,EAAE;QACH,QAAQ,EAAE,QAAiB;QAC3B,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,EAAE;QACV,WAAW,EAAE,GAAG;QAChB,SAAS,EAAE,IAAI;KAChB;IACD,EAAE,EAAE;QACF,KAAK,EAAE,QAAiB;QACxB,QAAQ,EAAE,QAAiB;QAC3B,WAAW,EAAE,KAAK;KACnB;IACD,KAAK,EAAE;QACL,oBAAoB,EAAE,SAAkB;QACxC,qBAAqB,EAAE,IAAI;QAC3B,gBAAgB,EAAE,KAAK;KACxB;IACD,KAAK,EAAE;QACL,YAAY,EAAE;YACZ,mBAAmB;YACnB,WAAW;YACX,YAAY;YACZ,gBAAgB;YAChB,YAAY;YACZ,MAAM;YACN,iBAAiB;SAClB;QACD,YAAY,EAAE,EAAE;KACjB;CACF,CAAC;AAEF,iBAAiB;AACJ,sBAAc,GAAG;IAC5B,aAAa,EAAE,mEAAmE;IAClF,eAAe,EAAE,gDAAgD;IACjE,eAAe,EAAE,iDAAiD;IAClE,mBAAmB,EAAE,8CAA8C;IACnE,mBAAmB,EAAE,kCAAkC;IACvD,qBAAqB,EAAE,0CAA0C;IACjE,cAAc,EAAE,iBAAiB;IACjC,iBAAiB,EAAE,oBAAoB;IACvC,aAAa,EAAE,yBAAyB;IACxC,aAAa,EAAE,4BAA4B;CACnC,CAAC;AAEX,mBAAmB;AACN,wBAAgB,GAAG;IAC9B,cAAc,EAAE,8BAA8B;IAC9C,oBAAoB,EAAE,2BAA2B;IACjD,oBAAoB,EAAE,uBAAuB;IAC7C,UAAU,EAAE,0BAA0B;IACtC,aAAa,EAAE,6BAA6B;IAC5C,iBAAiB,EAAE,iCAAiC;CAC5C,CAAC;AAEX,mBAAmB;AACN,wBAAgB,GAAG;IAC9B,kBAAkB,EAAE,EAAE;IACtB,6BAA6B,EAAE,GAAG;IAClC,kBAAkB,EAAE,KAAK;IACzB,mBAAmB,EAAE,GAAG;IACxB,aAAa,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IACxC,uBAAuB,EAAE,CAAC;CAClB,CAAC;AAEX,gBAAgB;AACH,qBAAa,GAAG;IAC3B,kBAAkB,EAAE,KAAK;IACzB,mBAAmB,EAAE,KAAK;IAC1B,cAAc,EAAE,KAAK;IACrB,mBAAmB,EAAE,KAAK;IAC1B,oBAAoB,EAAE,KAAK;IAC3B,iBAAiB,EAAE,KAAK;CAChB,CAAC;AAEX,wBAAwB;AACX,qBAAa,GAAG;IAC3B,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,UAAU,EAAE,IAAI;IAChB,gBAAgB,EAAE,IAAI;CACd,CAAC;AAEX,uBAAuB;AACV,sBAAc,GAAG;IAC5B,kBAAkB,EAAE,+BAA+B;IACnD,qBAAqB,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;IACvD,qBAAqB,EAAE,6BAA6B;IACpD,kBAAkB,EAAE,+BAA+B;CAC3C,CAAC;AAEX,sBAAsB;AACT,sBAAc,GAAG;IAC5B,KAAK,EAAE,4BAA4B;IACnC,GAAG,EAAE,gBAAgB;IACrB,OAAO,EAAE,kBAAkB;IAC3B,QAAQ,EAAE,kBAAkB;IAC5B,OAAO,EAAE,iBAAiB;CAClB,CAAC;AAEX,eAAe;AACF,oBAAY,GAAG;IAC1B,IAAI,EAAE;QACJ,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE,SAAS;QACf,aAAa,EAAE,SAAS;QACxB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;KACjB;IACD,KAAK,EAAE;QACL,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE,SAAS;QACf,aAAa,EAAE,SAAS;QACxB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;KACjB;CACO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/OX,sC;;;;;;;;;;ACAA,yC;;;;;;;;;;ACAA,qC;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WC5BA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,E;;;;;WC3BA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,sDAAsD;WACtD,sCAAsC,mGAAmG;WACzI;WACA;WACA;WACA;WACA;WACA,E;;;;;WCzBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,EAAE;WACF,E;;;;;WCRA;WACA;WACA;WACA;WACA,E;;;;;WCJA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC,I;;;;;WCPD,wF;;;;;WCAA;WACA;WACA;WACA;WACA,uBAAuB,4BAA4B;WACnD;WACA;WACA;WACA,iBAAiB,oBAAoB;WACrC;WACA,mGAAmG,YAAY;WAC/G;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,mEAAmE,iCAAiC;WACpG;WACA;WACA;WACA,E;;;;;WCzCA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;WCNA;WACA;WACA;WACA;WACA,E;;;;;WCJA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,kC;;;;;WClBA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;WACA,iCAAiC;;WAEjC;WACA;WACA;WACA,KAAK;WACL,eAAe;WACf;WACA;WACA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA,4G;;;;;WCrFA,mC;;;;;UEAA;UACA;UACA;UACA;UACA", "sources": ["webpack://ai-assistant-desktop/./src/renderer/styles/globals.css", "webpack://ai-assistant-desktop/./src/renderer/App.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/chat/ChatInput.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/chat/ChatInterface.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/chat/MessageBubble.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/chat/PlanConfirmation.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/chat/StreamingMessage.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/chat/ToolResultDisplay.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/modals/ConfirmationModal.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/settings/SettingsPanel.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/ui/ErrorToast.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/ui/LoadingScreen.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/ui/Sidebar.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/ui/TitleBar.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/ui/ToastContainer.tsx", "webpack://ai-assistant-desktop/./src/renderer/components/ui/YoloModeIndicator.tsx", "webpack://ai-assistant-desktop/./src/renderer/hooks/useElectronAPI.ts", "webpack://ai-assistant-desktop/./src/renderer/hooks/useUtils.ts", "webpack://ai-assistant-desktop/./src/renderer/index.tsx", "webpack://ai-assistant-desktop/./src/renderer/store/appStore.ts", "webpack://ai-assistant-desktop/./src/renderer/styles/globals.css?80c6", "webpack://ai-assistant-desktop/./src/shared/constants.ts", "webpack://ai-assistant-desktop/external node-commonjs \"node:path\"", "webpack://ai-assistant-desktop/external node-commonjs \"node:process\"", "webpack://ai-assistant-desktop/external node-commonjs \"node:url\"", "webpack://ai-assistant-desktop/webpack/bootstrap", "webpack://ai-assistant-desktop/webpack/runtime/chunk loaded", "webpack://ai-assistant-desktop/webpack/runtime/compat get default export", "webpack://ai-assistant-desktop/webpack/runtime/create fake namespace object", "webpack://ai-assistant-desktop/webpack/runtime/define property getters", "webpack://ai-assistant-desktop/webpack/runtime/ensure chunk", "webpack://ai-assistant-desktop/webpack/runtime/get javascript chunk filename", "webpack://ai-assistant-desktop/webpack/runtime/global", "webpack://ai-assistant-desktop/webpack/runtime/hasOwnProperty shorthand", "webpack://ai-assistant-desktop/webpack/runtime/load script", "webpack://ai-assistant-desktop/webpack/runtime/make namespace object", "webpack://ai-assistant-desktop/webpack/runtime/node module decorator", "webpack://ai-assistant-desktop/webpack/runtime/publicPath", "webpack://ai-assistant-desktop/webpack/runtime/jsonp chunk loading", "webpack://ai-assistant-desktop/webpack/runtime/nonce", "webpack://ai-assistant-desktop/webpack/before-startup", "webpack://ai-assistant-desktop/webpack/startup", "webpack://ai-assistant-desktop/webpack/after-startup"], "sourcesContent": ["// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/api.js\";\nimport ___CSS_LOADER_GET_URL_IMPORT___ from \"../../../node_modules/css-loader/dist/runtime/getUrl.js\";\nvar ___CSS_LOADER_URL_IMPORT_0___ = new URL(\"data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%2371717a%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e\", import.meta.url);\nvar ___CSS_LOADER_URL_IMPORT_1___ = new URL(\"data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e\", import.meta.url);\nvar ___CSS_LOADER_URL_IMPORT_2___ = new URL(\"data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e\", import.meta.url);\nvar ___CSS_LOADER_URL_IMPORT_3___ = new URL(\"data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e\", import.meta.url);\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap);\"]);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_1___);\nvar ___CSS_LOADER_URL_REPLACEMENT_2___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_2___);\nvar ___CSS_LOADER_URL_REPLACEMENT_3___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_3___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e4e4e7; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured \\`sans\\` font-family by default.\n5. Use the user's configured \\`sans\\` font-feature-settings by default.\n6. Use the user's configured \\`sans\\` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: Inter, system-ui, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from \\`html\\` so users can set them as a class directly on the \\`html\\` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured \\`mono\\` font-family by default.\n2. Use the user's configured \\`mono\\` font-feature-settings by default.\n3. Use the user's configured \\`mono\\` font-variation-settings by default.\n4. Correct the odd \\`em\\` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: JetBrains Mono, Consolas, monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent \\`sub\\` and \\`sup\\` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional \\`:invalid\\` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to \\`inherit\\` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #a1a1aa; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #a1a1aa; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements \\`display: block\\` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add \\`vertical-align: middle\\` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  background-color: #fff;\n  border-color: #71717a;\n  border-width: 1px;\n  border-radius: 0px;\n  padding-top: 0.5rem;\n  padding-right: 0.75rem;\n  padding-bottom: 0.5rem;\n  padding-left: 0.75rem;\n  font-size: 1rem;\n  line-height: 1.5rem;\n  --tw-shadow: 0 0 #0000;\n}\n\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: #2563eb;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  border-color: #2563eb;\n}\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  color: #71717a;\n  opacity: 1;\n}\n\ninput::placeholder,textarea::placeholder {\n  color: #71717a;\n  opacity: 1;\n}\n\n::-webkit-datetime-edit-fields-wrapper {\n  padding: 0;\n}\n\n::-webkit-date-and-time-value {\n  min-height: 1.5em;\n  text-align: inherit;\n}\n\n::-webkit-datetime-edit {\n  display: inline-flex;\n}\n\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\nselect {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_0___});\n  background-position: right 0.5rem center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5rem;\n  -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n}\n\n[multiple],[size]:where(select:not([size=\"1\"])) {\n  background-image: initial;\n  background-position: initial;\n  background-repeat: unset;\n  background-size: initial;\n  padding-right: 0.75rem;\n  -webkit-print-color-adjust: unset;\n          print-color-adjust: unset;\n}\n\n[type='checkbox'],[type='radio'] {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  padding: 0;\n  -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n  display: inline-block;\n  vertical-align: middle;\n  background-origin: border-box;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  flex-shrink: 0;\n  height: 1rem;\n  width: 1rem;\n  color: #2563eb;\n  background-color: #fff;\n  border-color: #71717a;\n  border-width: 1px;\n  --tw-shadow: 0 0 #0000;\n}\n\n[type='checkbox'] {\n  border-radius: 0px;\n}\n\n[type='radio'] {\n  border-radius: 100%;\n}\n\n[type='checkbox']:focus,[type='radio']:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: #2563eb;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n\n[type='checkbox']:checked,[type='radio']:checked {\n  border-color: transparent;\n  background-color: currentColor;\n  background-size: 100% 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n[type='checkbox']:checked {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_1___});\n}\n\n@media (forced-colors: active)  {\n\n  [type='checkbox']:checked {\n    -webkit-appearance: auto;\n       -moz-appearance: auto;\n            appearance: auto;\n  }\n}\n\n[type='radio']:checked {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_2___});\n}\n\n@media (forced-colors: active)  {\n\n  [type='radio']:checked {\n    -webkit-appearance: auto;\n       -moz-appearance: auto;\n            appearance: auto;\n  }\n}\n\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\n  border-color: transparent;\n  background-color: currentColor;\n}\n\n[type='checkbox']:indeterminate {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_3___});\n  border-color: transparent;\n  background-color: currentColor;\n  background-size: 100% 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n@media (forced-colors: active)  {\n\n  [type='checkbox']:indeterminate {\n    -webkit-appearance: auto;\n       -moz-appearance: auto;\n            appearance: auto;\n  }\n}\n\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\n  border-color: transparent;\n  background-color: currentColor;\n}\n\n[type='file'] {\n  background: unset;\n  border-color: inherit;\n  border-width: 0;\n  border-radius: 0;\n  padding: 0;\n  font-size: unset;\n  line-height: inherit;\n}\n\n[type='file']:focus {\n  outline: 1px solid ButtonText;\n  outline: 1px auto -webkit-focus-ring-color;\n}\r\n  * {\r\n    border-color: #e4e4e7;\r\n  }\r\n\r\n  html {\r\n    font-family: 'Inter', system-ui, sans-serif;\r\n  }\r\n\r\n  body {\n  --tw-bg-opacity: 1;\n  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(250 250 250 / var(--tw-text-opacity, 1));\n}\r\n\r\n  /* Custom scrollbar styles */\r\n  * {\r\n    scrollbar-width: thin;\r\n    scrollbar-color: #52525b #27272a;\r\n  }\r\n\r\n  *::-webkit-scrollbar {\r\n    width: 8px;\r\n    height: 8px;\r\n  }\r\n\r\n  *::-webkit-scrollbar-track {\n  border-radius: 0.25rem;\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n}\r\n\r\n  *::-webkit-scrollbar-thumb {\n  border-radius: 0.25rem;\n  --tw-bg-opacity: 1;\n  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));\n}\r\n\r\n  *::-webkit-scrollbar-thumb:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));\n}\r\n\r\n  *::-webkit-scrollbar-corner {\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n}\r\n.\\\\!container {\n  width: 100% !important;\n}\r\n.container {\n  width: 100%;\n}\r\n@media (min-width: 640px) {\n\n  .\\\\!container {\n    max-width: 640px !important;\n  }\n\n  .container {\n    max-width: 640px;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .\\\\!container {\n    max-width: 768px !important;\n  }\n\n  .container {\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .\\\\!container {\n    max-width: 1024px !important;\n  }\n\n  .container {\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1280px) {\n\n  .\\\\!container {\n    max-width: 1280px !important;\n  }\n\n  .container {\n    max-width: 1280px;\n  }\n}\r\n@media (min-width: 1536px) {\n\n  .\\\\!container {\n    max-width: 1536px !important;\n  }\n\n  .container {\n    max-width: 1536px;\n  }\n}\r\n.prose {\n  color: var(--tw-prose-body);\n  max-width: 65ch;\n}\r\n.prose :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n}\r\n.prose :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-lead);\n  font-size: 1.25em;\n  line-height: 1.6;\n  margin-top: 1.2em;\n  margin-bottom: 1.2em;\n}\r\n.prose :where(a):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-links);\n  text-decoration: underline;\n  font-weight: 500;\n}\r\n.prose :where(strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-bold);\n  font-weight: 600;\n}\r\n.prose :where(a strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(blockquote strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(thead th strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: decimal;\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n  padding-inline-start: 1.625em;\n}\r\n.prose :where(ol[type=\"A\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: upper-alpha;\n}\r\n.prose :where(ol[type=\"a\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: lower-alpha;\n}\r\n.prose :where(ol[type=\"A\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: upper-alpha;\n}\r\n.prose :where(ol[type=\"a\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: lower-alpha;\n}\r\n.prose :where(ol[type=\"I\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: upper-roman;\n}\r\n.prose :where(ol[type=\"i\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: lower-roman;\n}\r\n.prose :where(ol[type=\"I\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: upper-roman;\n}\r\n.prose :where(ol[type=\"i\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: lower-roman;\n}\r\n.prose :where(ol[type=\"1\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: decimal;\n}\r\n.prose :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  list-style-type: disc;\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n  padding-inline-start: 1.625em;\n}\r\n.prose :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n  font-weight: 400;\n  color: var(--tw-prose-counters);\n}\r\n.prose :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n  color: var(--tw-prose-bullets);\n}\r\n.prose :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  margin-top: 1.25em;\n}\r\n.prose :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  border-color: var(--tw-prose-hr);\n  border-top-width: 1px;\n  margin-top: 3em;\n  margin-bottom: 3em;\n}\r\n.prose :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-weight: 500;\n  font-style: italic;\n  color: var(--tw-prose-quotes);\n  border-inline-start-width: 0.25rem;\n  border-inline-start-color: var(--tw-prose-quote-borders);\n  quotes: \"\\\\201C\"\"\\\\201D\"\"\\\\2018\"\"\\\\2019\";\n  margin-top: 1.6em;\n  margin-bottom: 1.6em;\n  padding-inline-start: 1em;\n}\r\n.prose :where(blockquote p:first-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n  content: open-quote;\n}\r\n.prose :where(blockquote p:last-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n  content: close-quote;\n}\r\n.prose :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 800;\n  font-size: 2.25em;\n  margin-top: 0;\n  margin-bottom: 0.8888889em;\n  line-height: 1.1111111;\n}\r\n.prose :where(h1 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-weight: 900;\n  color: inherit;\n}\r\n.prose :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 700;\n  font-size: 1.5em;\n  margin-top: 2em;\n  margin-bottom: 1em;\n  line-height: 1.3333333;\n}\r\n.prose :where(h2 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-weight: 800;\n  color: inherit;\n}\r\n.prose :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  font-size: 1.25em;\n  margin-top: 1.6em;\n  margin-bottom: 0.6em;\n  line-height: 1.6;\n}\r\n.prose :where(h3 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-weight: 700;\n  color: inherit;\n}\r\n.prose :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  margin-top: 1.5em;\n  margin-bottom: 0.5em;\n  line-height: 1.5;\n}\r\n.prose :where(h4 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-weight: 700;\n  color: inherit;\n}\r\n.prose :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\r\n.prose :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  display: block;\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\r\n.prose :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\r\n.prose :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-weight: 500;\n  font-family: inherit;\n  color: var(--tw-prose-kbd);\n  box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows), 0 3px 0 var(--tw-prose-kbd-shadows);\n  font-size: 0.875em;\n  border-radius: 0.3125rem;\n  padding-top: 0.1875em;\n  padding-inline-end: 0.375em;\n  padding-bottom: 0.1875em;\n  padding-inline-start: 0.375em;\n}\r\n.prose :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-code);\n  font-weight: 600;\n  font-size: 0.875em;\n}\r\n.prose :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n  content: \"\\`\";\n}\r\n.prose :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n  content: \"\\`\";\n}\r\n.prose :where(a code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(h1 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n  font-size: 0.875em;\n}\r\n.prose :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n  font-size: 0.9em;\n}\r\n.prose :where(h4 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(blockquote code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(thead th code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: inherit;\n}\r\n.prose :where(pre):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-pre-code);\n  background-color: var(--tw-prose-pre-bg);\n  overflow-x: auto;\n  font-weight: 400;\n  font-size: 0.875em;\n  line-height: 1.7142857;\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n  border-radius: 0.375rem;\n  padding-top: 0.8571429em;\n  padding-inline-end: 1.1428571em;\n  padding-bottom: 0.8571429em;\n  padding-inline-start: 1.1428571em;\n}\r\n.prose :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  background-color: transparent;\n  border-width: 0;\n  border-radius: 0;\n  padding: 0;\n  font-weight: inherit;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n  line-height: inherit;\n}\r\n.prose :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n  content: none;\n}\r\n.prose :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n  content: none;\n}\r\n.prose :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  width: 100%;\n  table-layout: auto;\n  margin-top: 2em;\n  margin-bottom: 2em;\n  font-size: 0.875em;\n  line-height: 1.7142857;\n}\r\n.prose :where(thead):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  border-bottom-width: 1px;\n  border-bottom-color: var(--tw-prose-th-borders);\n}\r\n.prose :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  vertical-align: bottom;\n  padding-inline-end: 0.5714286em;\n  padding-bottom: 0.5714286em;\n  padding-inline-start: 0.5714286em;\n}\r\n.prose :where(tbody tr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  border-bottom-width: 1px;\n  border-bottom-color: var(--tw-prose-td-borders);\n}\r\n.prose :where(tbody tr:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  border-bottom-width: 0;\n}\r\n.prose :where(tbody td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  vertical-align: baseline;\n}\r\n.prose :where(tfoot):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  border-top-width: 1px;\n  border-top-color: var(--tw-prose-th-borders);\n}\r\n.prose :where(tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  vertical-align: top;\n}\r\n.prose :where(th, td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  text-align: start;\n}\r\n.prose :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\r\n.prose :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  color: var(--tw-prose-captions);\n  font-size: 0.875em;\n  line-height: 1.4285714;\n  margin-top: 0.8571429em;\n}\r\n.prose {\n  --tw-prose-body: #374151;\n  --tw-prose-headings: #111827;\n  --tw-prose-lead: #4b5563;\n  --tw-prose-links: #111827;\n  --tw-prose-bold: #111827;\n  --tw-prose-counters: #6b7280;\n  --tw-prose-bullets: #d1d5db;\n  --tw-prose-hr: #e5e7eb;\n  --tw-prose-quotes: #111827;\n  --tw-prose-quote-borders: #e5e7eb;\n  --tw-prose-captions: #6b7280;\n  --tw-prose-kbd: #111827;\n  --tw-prose-kbd-shadows: rgb(17, 24, 39 / 10%);\n  --tw-prose-code: #111827;\n  --tw-prose-pre-code: #e5e7eb;\n  --tw-prose-pre-bg: #1f2937;\n  --tw-prose-th-borders: #d1d5db;\n  --tw-prose-td-borders: #e5e7eb;\n  --tw-prose-invert-body: #d1d5db;\n  --tw-prose-invert-headings: #fff;\n  --tw-prose-invert-lead: #9ca3af;\n  --tw-prose-invert-links: #fff;\n  --tw-prose-invert-bold: #fff;\n  --tw-prose-invert-counters: #9ca3af;\n  --tw-prose-invert-bullets: #4b5563;\n  --tw-prose-invert-hr: #374151;\n  --tw-prose-invert-quotes: #f3f4f6;\n  --tw-prose-invert-quote-borders: #374151;\n  --tw-prose-invert-captions: #9ca3af;\n  --tw-prose-invert-kbd: #fff;\n  --tw-prose-invert-kbd-shadows: rgb(255, 255, 255 / 10%);\n  --tw-prose-invert-code: #fff;\n  --tw-prose-invert-pre-code: #d1d5db;\n  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);\n  --tw-prose-invert-th-borders: #4b5563;\n  --tw-prose-invert-td-borders: #374151;\n  font-size: 1rem;\n  line-height: 1.75;\n}\r\n.prose :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\r\n.prose :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.5em;\n  margin-bottom: 0.5em;\n}\r\n.prose :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0.375em;\n}\r\n.prose :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0.375em;\n}\r\n.prose :where(.prose > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.75em;\n  margin-bottom: 0.75em;\n}\r\n.prose :where(.prose > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.25em;\n}\r\n.prose :where(.prose > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-bottom: 1.25em;\n}\r\n.prose :where(.prose > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.25em;\n}\r\n.prose :where(.prose > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-bottom: 1.25em;\n}\r\n.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.75em;\n  margin-bottom: 0.75em;\n}\r\n.prose :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n}\r\n.prose :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.5em;\n  padding-inline-start: 1.625em;\n}\r\n.prose :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose :where(thead th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0;\n}\r\n.prose :where(thead th:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-end: 0;\n}\r\n.prose :where(tbody td, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-top: 0.5714286em;\n  padding-inline-end: 0.5714286em;\n  padding-bottom: 0.5714286em;\n  padding-inline-start: 0.5714286em;\n}\r\n.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0;\n}\r\n.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-end: 0;\n}\r\n.prose :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\r\n.prose :where(.prose > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose :where(.prose > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-bottom: 0;\n}\r\n.prose-sm {\n  font-size: 0.875rem;\n  line-height: 1.7142857;\n}\r\n.prose-sm :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n  margin-bottom: 1.1428571em;\n}\r\n.prose-sm :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 1.2857143em;\n  line-height: 1.5555556;\n  margin-top: 0.8888889em;\n  margin-bottom: 0.8888889em;\n}\r\n.prose-sm :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.3333333em;\n  margin-bottom: 1.3333333em;\n  padding-inline-start: 1.1111111em;\n}\r\n.prose-sm :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 2.1428571em;\n  margin-top: 0;\n  margin-bottom: 0.8em;\n  line-height: 1.2;\n}\r\n.prose-sm :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 1.4285714em;\n  margin-top: 1.6em;\n  margin-bottom: 0.8em;\n  line-height: 1.4;\n}\r\n.prose-sm :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 1.2857143em;\n  margin-top: 1.5555556em;\n  margin-bottom: 0.4444444em;\n  line-height: 1.5555556;\n}\r\n.prose-sm :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.4285714em;\n  margin-bottom: 0.5714286em;\n  line-height: 1.4285714;\n}\r\n.prose-sm :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n}\r\n.prose-sm :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n}\r\n.prose-sm :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\r\n.prose-sm :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n}\r\n.prose-sm :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.8571429em;\n  border-radius: 0.3125rem;\n  padding-top: 0.1428571em;\n  padding-inline-end: 0.3571429em;\n  padding-bottom: 0.1428571em;\n  padding-inline-start: 0.3571429em;\n}\r\n.prose-sm :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.8571429em;\n}\r\n.prose-sm :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.9em;\n}\r\n.prose-sm :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.8888889em;\n}\r\n.prose-sm :where(pre):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.8571429em;\n  line-height: 1.6666667;\n  margin-top: 1.6666667em;\n  margin-bottom: 1.6666667em;\n  border-radius: 0.25rem;\n  padding-top: 0.6666667em;\n  padding-inline-end: 1em;\n  padding-bottom: 0.6666667em;\n  padding-inline-start: 1em;\n}\r\n.prose-sm :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n  margin-bottom: 1.1428571em;\n  padding-inline-start: 1.5714286em;\n}\r\n.prose-sm :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n  margin-bottom: 1.1428571em;\n  padding-inline-start: 1.5714286em;\n}\r\n.prose-sm :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.2857143em;\n  margin-bottom: 0.2857143em;\n}\r\n.prose-sm :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0.4285714em;\n}\r\n.prose-sm :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0.4285714em;\n}\r\n.prose-sm :where(.prose-sm > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.5714286em;\n  margin-bottom: 0.5714286em;\n}\r\n.prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n}\r\n.prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-bottom: 1.1428571em;\n}\r\n.prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n}\r\n.prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-bottom: 1.1428571em;\n}\r\n.prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.5714286em;\n  margin-bottom: 0.5714286em;\n}\r\n.prose-sm :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n  margin-bottom: 1.1428571em;\n}\r\n.prose-sm :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.1428571em;\n}\r\n.prose-sm :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0.2857143em;\n  padding-inline-start: 1.5714286em;\n}\r\n.prose-sm :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 2.8571429em;\n  margin-bottom: 2.8571429em;\n}\r\n.prose-sm :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose-sm :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose-sm :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose-sm :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose-sm :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.8571429em;\n  line-height: 1.5;\n}\r\n.prose-sm :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-end: 1em;\n  padding-bottom: 0.6666667em;\n  padding-inline-start: 1em;\n}\r\n.prose-sm :where(thead th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0;\n}\r\n.prose-sm :where(thead th:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-end: 0;\n}\r\n.prose-sm :where(tbody td, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-top: 0.6666667em;\n  padding-inline-end: 1em;\n  padding-bottom: 0.6666667em;\n  padding-inline-start: 1em;\n}\r\n.prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-start: 0;\n}\r\n.prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  padding-inline-end: 0;\n}\r\n.prose-sm :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n}\r\n.prose-sm :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\r\n.prose-sm :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  font-size: 0.8571429em;\n  line-height: 1.3333333;\n  margin-top: 0.6666667em;\n}\r\n.prose-sm :where(.prose-sm > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-top: 0;\n}\r\n.prose-sm :where(.prose-sm > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n  margin-bottom: 0;\n}\r\n.prose-invert {\n  --tw-prose-body: var(--tw-prose-invert-body);\n  --tw-prose-headings: var(--tw-prose-invert-headings);\n  --tw-prose-lead: var(--tw-prose-invert-lead);\n  --tw-prose-links: var(--tw-prose-invert-links);\n  --tw-prose-bold: var(--tw-prose-invert-bold);\n  --tw-prose-counters: var(--tw-prose-invert-counters);\n  --tw-prose-bullets: var(--tw-prose-invert-bullets);\n  --tw-prose-hr: var(--tw-prose-invert-hr);\n  --tw-prose-quotes: var(--tw-prose-invert-quotes);\n  --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);\n  --tw-prose-captions: var(--tw-prose-invert-captions);\n  --tw-prose-kbd: var(--tw-prose-invert-kbd);\n  --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);\n  --tw-prose-code: var(--tw-prose-invert-code);\n  --tw-prose-pre-code: var(--tw-prose-invert-pre-code);\n  --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);\n  --tw-prose-th-borders: var(--tw-prose-invert-th-borders);\n  --tw-prose-td-borders: var(--tw-prose-invert-td-borders);\n}\r\n/* Button variants */\r\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.btn:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #18181b;\n}\r\n.btn:disabled {\n  pointer-events: none;\n  opacity: 0.5;\n}\r\n.btn-primary {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.btn-primary:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #18181b;\n}\r\n.btn-primary:disabled {\n  pointer-events: none;\n  opacity: 0.5;\n}\r\n.btn-primary {\n  --tw-bg-opacity: 1;\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.btn-primary:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\n}\r\n.btn-primary:active {\n  --tw-bg-opacity: 1;\n  background-color: rgb(7 89 133 / var(--tw-bg-opacity, 1));\n}\r\n.btn-secondary {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.btn-secondary:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #18181b;\n}\r\n.btn-secondary:disabled {\n  pointer-events: none;\n  opacity: 0.5;\n}\r\n.btn-secondary {\n  --tw-bg-opacity: 1;\n  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(228 228 231 / var(--tw-text-opacity, 1));\n}\r\n.btn-secondary:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));\n}\r\n.btn-secondary:active {\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n}\r\n.btn-sm {\n  height: 2rem;\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.btn-md {\n  height: 2.5rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n/* Input styles */\r\n.\\\\!input {\n  display: flex;\n  height: 2.5rem;\n  width: 100%;\n  border-radius: 0.375rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(244 244 245 / var(--tw-text-opacity, 1));\n}\r\n.\\\\!input::-moz-placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.\\\\!input::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.\\\\!input:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #18181b;\n}\r\n.\\\\!input:disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\r\n.input {\n  display: flex;\n  height: 2.5rem;\n  width: 100%;\n  border-radius: 0.375rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(244 244 245 / var(--tw-text-opacity, 1));\n}\r\n.input::-moz-placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.input::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.input:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #18181b;\n}\r\n.input:disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\r\n.textarea {\n  display: flex;\n  min-height: 80px;\n  width: 100%;\n  border-radius: 0.375rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(244 244 245 / var(--tw-text-opacity, 1));\n}\r\n.textarea::-moz-placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.textarea::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.textarea:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #18181b;\n}\r\n.textarea:disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\r\n/* Card styles */\r\n/* Badge styles */\r\n/* Loading states */\r\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\r\n.loading-spinner {\n  animation: spin 1s linear infinite;\n  border-radius: 9999px;\n  border-width: 2px;\n  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));\n  --tw-border-opacity: 1;\n  border-top-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n}\r\n@keyframes loading-dots {\r\n    0%, 20% { content: ''; }\r\n    40% { content: '.'; }\r\n    60% { content: '..'; }\r\n    80%, 100% { content: '...'; }\r\n  }\r\n/* Animations */\r\n.slide-down {\r\n    animation: slide-down 0.3s ease-out;\r\n  }\r\n@keyframes fade-in {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n  }\r\n@keyframes slide-up {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(10px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n@keyframes slide-down {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(-10px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n/* Syntax highlighting for code blocks */\r\n/* Diff viewer styles */\r\n/* Message bubble styles */\r\n.message-bubble {\n  max-width: 80%;\n  overflow-wrap: break-word;\n  border-radius: 0.5rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n/* Execution mode indicator */\r\n.yolo-mode {\n  border-width: 2px;\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\r\n.yolo-mode::before {\r\n    content: '';\r\n    pointer-events: none;\r\n    position: absolute;\r\n    inset: 0px;\r\n  }\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.yolo-mode::before {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n  border-radius: 0.5rem;\n  border-width: 2px;\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\r\n/* Tool result styles */\r\n/* Context menu styles */\r\n.context-menu {\n  position: absolute;\n  z-index: 50;\n  min-width: 8rem;\n  overflow: hidden;\n  border-radius: 0.375rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n  padding: 0.25rem;\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.context-menu-item {\n  position: relative;\n  display: flex;\n  cursor: pointer;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  align-items: center;\n  border-radius: 0.125rem;\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(228 228 231 / var(--tw-text-opacity, 1));\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.context-menu-item:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));\n}\r\n.context-menu-item:focus {\n  --tw-bg-opacity: 1;\n  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));\n}\r\n.static {\n  position: static;\n}\r\n.fixed {\n  position: fixed;\n}\r\n.absolute {\n  position: absolute;\n}\r\n.relative {\n  position: relative;\n}\r\n.inset-0 {\n  inset: 0px;\n}\r\n.bottom-4 {\n  bottom: 1rem;\n}\r\n.right-0 {\n  right: 0px;\n}\r\n.right-2 {\n  right: 0.5rem;\n}\r\n.right-3 {\n  right: 0.75rem;\n}\r\n.right-4 {\n  right: 1rem;\n}\r\n.top-1\\\\/2 {\n  top: 50%;\n}\r\n.top-2 {\n  top: 0.5rem;\n}\r\n.top-4 {\n  top: 1rem;\n}\r\n.top-8 {\n  top: 2rem;\n}\r\n.z-40 {\n  z-index: 40;\n}\r\n.z-50 {\n  z-index: 50;\n}\r\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\r\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-2 {\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n}\r\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\r\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\r\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\r\n.mb-4 {\n  margin-bottom: 1rem;\n}\r\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\r\n.mb-8 {\n  margin-bottom: 2rem;\n}\r\n.ml-1 {\n  margin-left: 0.25rem;\n}\r\n.ml-12 {\n  margin-left: 3rem;\n}\r\n.ml-2 {\n  margin-left: 0.5rem;\n}\r\n.ml-3 {\n  margin-left: 0.75rem;\n}\r\n.mr-1 {\n  margin-right: 0.25rem;\n}\r\n.mr-12 {\n  margin-right: 3rem;\n}\r\n.mr-2 {\n  margin-right: 0.5rem;\n}\r\n.mr-3 {\n  margin-right: 0.75rem;\n}\r\n.mt-0\\\\.5 {\n  margin-top: 0.125rem;\n}\r\n.mt-1 {\n  margin-top: 0.25rem;\n}\r\n.mt-2 {\n  margin-top: 0.5rem;\n}\r\n.mt-6 {\n  margin-top: 1.5rem;\n}\r\n.block {\n  display: block;\n}\r\n.\\\\!inline {\n  display: inline !important;\n}\r\n.inline {\n  display: inline;\n}\r\n.flex {\n  display: flex;\n}\r\n.grid {\n  display: grid;\n}\r\n.hidden {\n  display: none;\n}\r\n.h-0\\\\.5 {\n  height: 0.125rem;\n}\r\n.h-1 {\n  height: 0.25rem;\n}\r\n.h-10 {\n  height: 2.5rem;\n}\r\n.h-12 {\n  height: 3rem;\n}\r\n.h-16 {\n  height: 4rem;\n}\r\n.h-2 {\n  height: 0.5rem;\n}\r\n.h-20 {\n  height: 5rem;\n}\r\n.h-3 {\n  height: 0.75rem;\n}\r\n.h-4 {\n  height: 1rem;\n}\r\n.h-5 {\n  height: 1.25rem;\n}\r\n.h-5\\\\/6 {\n  height: 83.333333%;\n}\r\n.h-6 {\n  height: 1.5rem;\n}\r\n.h-64 {\n  height: 16rem;\n}\r\n.h-8 {\n  height: 2rem;\n}\r\n.h-full {\n  height: 100%;\n}\r\n.h-px {\n  height: 1px;\n}\r\n.h-screen {\n  height: 100vh;\n}\r\n.max-h-64 {\n  max-height: 16rem;\n}\r\n.max-h-80 {\n  max-height: 20rem;\n}\r\n.max-h-\\\\[80vh\\\\] {\n  max-height: 80vh;\n}\r\n.max-h-\\\\[90vh\\\\] {\n  max-height: 90vh;\n}\r\n.min-h-screen {\n  min-height: 100vh;\n}\r\n.w-0\\\\.5 {\n  width: 0.125rem;\n}\r\n.w-1 {\n  width: 0.25rem;\n}\r\n.w-1\\\\/2 {\n  width: 50%;\n}\r\n.w-10 {\n  width: 2.5rem;\n}\r\n.w-12 {\n  width: 3rem;\n}\r\n.w-16 {\n  width: 4rem;\n}\r\n.w-2 {\n  width: 0.5rem;\n}\r\n.w-20 {\n  width: 5rem;\n}\r\n.w-3 {\n  width: 0.75rem;\n}\r\n.w-3\\\\/4 {\n  width: 75%;\n}\r\n.w-4 {\n  width: 1rem;\n}\r\n.w-5 {\n  width: 1.25rem;\n}\r\n.w-6 {\n  width: 1.5rem;\n}\r\n.w-8 {\n  width: 2rem;\n}\r\n.w-80 {\n  width: 20rem;\n}\r\n.w-full {\n  width: 100%;\n}\r\n.w-px {\n  width: 1px;\n}\r\n.w-screen {\n  width: 100vw;\n}\r\n.min-w-0 {\n  min-width: 0px;\n}\r\n.min-w-80 {\n  min-width: 20rem;\n}\r\n.max-w-24 {\n  max-width: 6rem;\n}\r\n.max-w-2xl {\n  max-width: 42rem;\n}\r\n.max-w-3xl {\n  max-width: 48rem;\n}\r\n.max-w-4xl {\n  max-width: 56rem;\n}\r\n.max-w-6xl {\n  max-width: 72rem;\n}\r\n.max-w-lg {\n  max-width: 32rem;\n}\r\n.max-w-md {\n  max-width: 28rem;\n}\r\n.max-w-none {\n  max-width: none;\n}\r\n.max-w-xs {\n  max-width: 20rem;\n}\r\n.flex-1 {\n  flex: 1 1 0%;\n}\r\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\r\n.-translate-y-1\\\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes bounce {\n\n  0%, 100% {\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\n  }\n\n  50% {\n    transform: none;\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\n  }\n}\r\n.animate-bounce {\n  animation: bounce 1s infinite;\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\r\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\r\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.cursor-pointer {\n  cursor: pointer;\n}\r\n.select-none {\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\r\n.resize-none {\n  resize: none;\n}\r\n.resize {\n  resize: both;\n}\r\n.list-inside {\n  list-style-position: inside;\n}\r\n.list-decimal {\n  list-style-type: decimal;\n}\r\n.list-disc {\n  list-style-type: disc;\n}\r\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\r\n.flex-row {\n  flex-direction: row;\n}\r\n.flex-row-reverse {\n  flex-direction: row-reverse;\n}\r\n.flex-col {\n  flex-direction: column;\n}\r\n.items-start {\n  align-items: flex-start;\n}\r\n.items-end {\n  align-items: flex-end;\n}\r\n.items-center {\n  align-items: center;\n}\r\n.justify-end {\n  justify-content: flex-end;\n}\r\n.justify-center {\n  justify-content: center;\n}\r\n.justify-between {\n  justify-content: space-between;\n}\r\n.gap-2 {\n  gap: 0.5rem;\n}\r\n.gap-3 {\n  gap: 0.75rem;\n}\r\n.gap-4 {\n  gap: 1rem;\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\r\n.overflow-auto {\n  overflow: auto;\n}\r\n.overflow-hidden {\n  overflow: hidden;\n}\r\n.overflow-x-auto {\n  overflow-x: auto;\n}\r\n.overflow-y-auto {\n  overflow-y: auto;\n}\r\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\r\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\r\n.break-words {\n  overflow-wrap: break-word;\n}\r\n.rounded {\n  border-radius: 0.25rem;\n}\r\n.rounded-2xl {\n  border-radius: 1rem;\n}\r\n.rounded-3xl {\n  border-radius: 1.5rem;\n}\r\n.rounded-full {\n  border-radius: 9999px;\n}\r\n.rounded-lg {\n  border-radius: 0.5rem;\n}\r\n.rounded-md {\n  border-radius: 0.375rem;\n}\r\n.rounded-xl {\n  border-radius: 0.75rem;\n}\r\n.border {\n  border-width: 1px;\n}\r\n.border-2 {\n  border-width: 2px;\n}\r\n.border-b {\n  border-bottom-width: 1px;\n}\r\n.border-b-2 {\n  border-bottom-width: 2px;\n}\r\n.border-l-2 {\n  border-left-width: 2px;\n}\r\n.border-l-4 {\n  border-left-width: 4px;\n}\r\n.border-r {\n  border-right-width: 1px;\n}\r\n.border-t {\n  border-top-width: 1px;\n}\r\n.border-t-4 {\n  border-top-width: 4px;\n}\r\n.border-blue-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));\n}\r\n.border-danger-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));\n}\r\n.border-green-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\n}\r\n.border-green-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\r\n.border-green-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\r\n.border-green-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));\n}\r\n.border-primary-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n}\r\n.border-red-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\r\n.border-red-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\r\n.border-red-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\r\n.border-red-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\n}\r\n.border-red-800 {\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\r\n.border-success-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\r\n.border-warning-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(180 83 9 / var(--tw-border-opacity, 1));\n}\r\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));\n}\r\n.border-t-primary-500 {\n  --tw-border-opacity: 1;\n  border-top-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n}\r\n.border-t-transparent {\n  border-top-color: transparent;\n}\r\n.bg-black {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\r\n.bg-black\\\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\r\n.bg-blue-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-900\\\\/20 {\n  background-color: rgb(30 58 138 / 0.2);\n}\r\n.bg-blue-900\\\\/50 {\n  background-color: rgb(30 58 138 / 0.5);\n}\r\n.bg-danger-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\r\n.bg-danger-900\\\\/20 {\n  background-color: rgb(127 29 29 / 0.2);\n}\r\n.bg-gray-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(161 161 170 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-800 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-800\\\\/50 {\n  background-color: rgb(39 39 42 / 0.5);\n}\r\n.bg-gray-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-900\\\\/20 {\n  background-color: rgb(20 83 45 / 0.2);\n}\r\n.bg-green-900\\\\/50 {\n  background-color: rgb(20 83 45 / 0.5);\n}\r\n.bg-primary-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-900\\\\/20 {\n  background-color: rgb(127 29 29 / 0.2);\n}\r\n.bg-red-900\\\\/30 {\n  background-color: rgb(127 29 29 / 0.3);\n}\r\n.bg-red-900\\\\/50 {\n  background-color: rgb(127 29 29 / 0.5);\n}\r\n.bg-success-900\\\\/20 {\n  background-color: rgb(20 83 45 / 0.2);\n}\r\n.bg-warning-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));\n}\r\n.bg-warning-900\\\\/50 {\n  background-color: rgb(120 53 15 / 0.5);\n}\r\n.bg-yellow-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-900\\\\/50 {\n  background-color: rgb(113 63 18 / 0.5);\n}\r\n.bg-opacity-20 {\n  --tw-bg-opacity: 0.2;\n}\r\n.bg-opacity-30 {\n  --tw-bg-opacity: 0.3;\n}\r\n.bg-opacity-40 {\n  --tw-bg-opacity: 0.4;\n}\r\n.bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\r\n.bg-opacity-90 {\n  --tw-bg-opacity: 0.9;\n}\r\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\r\n.from-primary-500 {\n  --tw-gradient-from: #0ea5e9 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(14 165 233 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-purple-500 {\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.to-pink-600 {\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\n}\r\n.to-primary-600 {\n  --tw-gradient-to: #0284c7 var(--tw-gradient-to-position);\n}\r\n.p-1 {\n  padding: 0.25rem;\n}\r\n.p-2 {\n  padding: 0.5rem;\n}\r\n.p-3 {\n  padding: 0.75rem;\n}\r\n.p-4 {\n  padding: 1rem;\n}\r\n.p-6 {\n  padding: 1.5rem;\n}\r\n.p-8 {\n  padding: 2rem;\n}\r\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\r\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.py-0\\\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\r\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\r\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\r\n.pl-3 {\n  padding-left: 0.75rem;\n}\r\n.pr-10 {\n  padding-right: 2.5rem;\n}\r\n.pr-12 {\n  padding-right: 3rem;\n}\r\n.pt-4 {\n  padding-top: 1rem;\n}\r\n.text-center {\n  text-align: center;\n}\r\n.text-right {\n  text-align: right;\n}\r\n.font-mono {\n  font-family: JetBrains Mono, Consolas, monospace;\n}\r\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\r\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\r\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\r\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-bold {\n  font-weight: 700;\n}\r\n.font-medium {\n  font-weight: 500;\n}\r\n.font-semibold {\n  font-weight: 600;\n}\r\n.uppercase {\n  text-transform: uppercase;\n}\r\n.capitalize {\n  text-transform: capitalize;\n}\r\n.italic {\n  font-style: italic;\n}\r\n.leading-relaxed {\n  line-height: 1.625;\n}\r\n.tracking-wide {\n  letter-spacing: 0.025em;\n}\r\n.text-blue-200 {\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-400 {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-500 {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n.text-danger-100 {\n  --tw-text-opacity: 1;\n  color: rgb(254 226 226 / var(--tw-text-opacity, 1));\n}\r\n.text-danger-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n.text-danger-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-100 {\n  --tw-text-opacity: 1;\n  color: rgb(244 244 245 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-200 {\n  --tw-text-opacity: 1;\n  color: rgb(228 228 231 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(212 212 216 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(113 113 122 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(82 82 91 / var(--tw-text-opacity, 1));\n}\r\n.text-green-300 {\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\r\n.text-green-400 {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\r\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-200 {\n  --tw-text-opacity: 1;\n  color: rgb(186 230 253 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-400 {\n  --tw-text-opacity: 1;\n  color: rgb(56 189 248 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-600 {\n  --tw-text-opacity: 1;\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\n}\r\n.text-red-200 {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\r\n.text-red-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n.text-red-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\r\n.text-success-300 {\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\r\n.text-success-400 {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\r\n.text-warning-200 {\n  --tw-text-opacity: 1;\n  color: rgb(253 230 138 / var(--tw-text-opacity, 1));\n}\r\n.text-warning-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 211 77 / var(--tw-text-opacity, 1));\n}\r\n.text-warning-400 {\n  --tw-text-opacity: 1;\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\n}\r\n.text-warning-500 {\n  --tw-text-opacity: 1;\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\n}\r\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-400 {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-500 {\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\r\n.placeholder-gray-400::-moz-placeholder {\n  --tw-placeholder-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-placeholder-opacity, 1));\n}\r\n.placeholder-gray-400::placeholder {\n  --tw-placeholder-opacity: 1;\n  color: rgb(161 161 170 / var(--tw-placeholder-opacity, 1));\n}\r\n.opacity-0 {\n  opacity: 0;\n}\r\n.opacity-50 {\n  opacity: 0.5;\n}\r\n.shadow-2xl {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-red-500\\\\/50 {\n  --tw-ring-color: rgb(239 68 68 / 0.5);\n}\r\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n/* Custom utilities */\r\n/* Focus utilities */\r\n/* Safe area utilities for frameless window */\r\n.drag-region {\r\n    -webkit-app-region: drag;\r\n  }\r\n.no-drag {\r\n    -webkit-app-region: no-drag;\r\n  }\r\n\r\n.last\\\\:mb-0:last-child {\n  margin-bottom: 0px;\n}\r\n\r\n.hover\\\\:bg-blue-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-danger-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-danger-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-gray-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-gray-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-green-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-green-800:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-primary-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-red-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-red-800:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-yellow-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\\\:bg-opacity-40:hover {\n  --tw-bg-opacity: 0.4;\n}\r\n\r\n.hover\\\\:bg-opacity-50:hover {\n  --tw-bg-opacity: 0.5;\n}\r\n\r\n.hover\\\\:text-gray-200:hover {\n  --tw-text-opacity: 1;\n  color: rgb(228 228 231 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\\\:text-gray-300:hover {\n  --tw-text-opacity: 1;\n  color: rgb(212 212 216 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n\r\n.focus\\\\:border-primary-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\n}\r\n\r\n.focus\\\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n\r\n.focus\\\\:ring-1:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\\\:ring-blue-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.focus\\\\:ring-green-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.focus\\\\:ring-primary-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.focus\\\\:ring-red-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.focus\\\\:ring-yellow-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.focus\\\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\r\n\r\n.focus\\\\:ring-offset-gray-800:focus {\n  --tw-ring-offset-color: #27272a;\n}\r\n\r\n.disabled\\\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\r\n\r\n.disabled\\\\:opacity-50:disabled {\n  opacity: 0.5;\n}\r\n\r\n.group:hover .group-hover\\\\:opacity-100 {\n  opacity: 1;\n}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/renderer/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,gDAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,yDAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,UAAc;EAAd,iCAAc;UAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;KAAd,sBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,yDAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yDAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,yDAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,qBAAc;EAAA;;EAAd;IAAA,2CAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd,yDAAc;EAAd,oBAAc;EAAd;AAAc;;EAAd,4BAAc;EAAd;IAAA,qBAAc;IAAd,gCAAc;EAAA;;EAAd;IAAA,UAAc;IAAd,WAAc;EAAA;;EAAd;EAAA,sBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,sBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;;EAApB;IAAA;EAAoB;AAAA;AAApB;EAAA,2BAAoB;EAApB,eAAoB;AAAA;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA,2BAAoB;EAApB,iBAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB;AAAoB;AAApB;EAAA,4BAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,wBAAoB;EAApB,kBAAoB;EAApB,qBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,qBAAoB;EAApB,kBAAoB;EAApB,qBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB;AAAoB;AAApB;EAAA,gCAAoB;EAApB,qBAAoB;EAApB,eAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB,kBAAoB;EAApB,6BAAoB;EAApB,kCAAoB;EAApB,wDAAoB;EAApB,oCAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB,aAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,gBAAoB;EAApB,eAAoB;EAApB,kBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB;AAAoB;AAApB;EAAA,eAAoB;EAApB;AAAoB;AAApB;EAAA,cAAoB;EAApB,eAAoB;EAApB;AAAoB;AAApB;EAAA,eAAoB;EAApB;AAAoB;AAApB;EAAA,gBAAoB;EAApB,oBAAoB;EAApB,0BAAoB;EAApB,sFAAoB;EAApB,kBAAoB;EAApB,wBAAoB;EAApB,qBAAoB;EAApB,2BAAoB;EAApB,wBAAoB;EAApB;AAAoB;AAApB;EAAA,2BAAoB;EAApB,gBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA,cAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,+BAAoB;EAApB,wCAAoB;EAApB,gBAAoB;EAApB,gBAAoB;EAApB,kBAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,0BAAoB;EAApB,uBAAoB;EAApB,wBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,6BAAoB;EAApB,eAAoB;EAApB,gBAAoB;EAApB,UAAoB;EAApB,oBAAoB;EAApB,cAAoB;EAApB,kBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,WAAoB;EAApB,kBAAoB;EAApB,eAAoB;EAApB,kBAAoB;EAApB,kBAAoB;EAApB;AAAoB;AAApB;EAAA,wBAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,gBAAoB;EAApB,sBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,wBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,qBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,+BAAoB;EAApB,kBAAoB;EAApB,sBAAoB;EAApB;AAAoB;AAApB;EAAA,wBAAoB;EAApB,4BAAoB;EAApB,wBAAoB;EAApB,yBAAoB;EAApB,wBAAoB;EAApB,4BAAoB;EAApB,2BAAoB;EAApB,sBAAoB;EAApB,0BAAoB;EAApB,iCAAoB;EAApB,4BAAoB;EAApB,uBAAoB;EAApB,6CAAoB;EAApB,wBAAoB;EAApB,4BAAoB;EAApB,0BAAoB;EAApB,8BAAoB;EAApB,8BAAoB;EAApB,+BAAoB;EAApB,gCAAoB;EAApB,+BAAoB;EAApB,6BAAoB;EAApB,4BAAoB;EAApB,mCAAoB;EAApB,kCAAoB;EAApB,6BAAoB;EAApB,iCAAoB;EAApB,wCAAoB;EAApB,mCAAoB;EAApB,2BAAoB;EAApB,uDAAoB;EAApB,4BAAoB;EAApB,mCAAoB;EAApB,0CAAoB;EAApB,qCAAoB;EAApB,qCAAoB;EAApB,eAAoB;EAApB;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,iBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA,kBAAoB;EAApB;AAAoB;AAApB;EAAA,iBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,wBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,eAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,mBAAoB;EAApB,sBAAoB;AAAA;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,aAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,iBAAoB;EAApB,oBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,wBAAoB;EAApB,wBAAoB;EAApB,+BAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,sBAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,0BAAoB;EAApB,sBAAoB;EAApB,wBAAoB;EAApB,uBAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,0BAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,sBAAoB;EAApB;AAAoB;AAApB;EAAA,uBAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,wBAAoB;EAApB,uBAAoB;EAApB,2BAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,uBAAoB;EAApB;AAAoB;AAApB;EAAA,aAAoB;EAApB;AAAoB;AAApB;EAAA,sBAAoB;EAApB,sBAAoB;EAApB;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA;AAAoB;AAApB;EAAA,4CAAoB;EAApB,oDAAoB;EAApB,4CAAoB;EAApB,8CAAoB;EAApB,4CAAoB;EAApB,oDAAoB;EAApB,kDAAoB;EAApB,wCAAoB;EAApB,gDAAoB;EAApB,8DAAoB;EAApB,oDAAoB;EAApB,0CAAoB;EAApB,0DAAoB;EAApB,4CAAoB;EAApB,oDAAoB;EAApB,gDAAoB;EAApB,wDAAoB;EAApB;AAAoB;AA2ClB,oBAAoB;AAElB;EAAA,oBAA8R;EAA9R,mBAA8R;EAA9R,uBAA8R;EAA9R,uBAA8R;EAA9R,mBAA8R;EAA9R,oBAA8R;EAA9R,gBAA8R;EAA9R,+FAA8R;EAA9R,wDAA8R;EAA9R;AAA8R;AAA9R;EAAA,8BAA8R;EAA9R,mBAA8R;EAA9R,2GAA8R;EAA9R,yGAA8R;EAA9R,4FAA8R;EAA9R,oBAA8R;EAA9R,4DAA8R;EAA9R,2BAA8R;EAA9R;AAA8R;AAA9R;EAAA,oBAA8R;EAA9R;AAA8R;AAI9R;EAAA,oBAA+E;EAA/E,mBAA+E;EAA/E,uBAA+E;EAA/E,uBAA+E;EAA/E,mBAA+E;EAA/E,oBAA+E;EAA/E,gBAA+E;EAA/E,+FAA+E;EAA/E,wDAA+E;EAA/E;AAA+E;AAA/E;EAAA,8BAA+E;EAA/E,mBAA+E;EAA/E,2GAA+E;EAA/E,yGAA+E;EAA/E,4FAA+E;EAA/E,oBAA+E;EAA/E,4DAA+E;EAA/E,2BAA+E;EAA/E;AAA+E;AAA/E;EAAA,oBAA+E;EAA/E;AAA+E;AAA/E;EAAA,kBAA+E;EAA/E,0DAA+E;EAA/E,oBAA+E;EAA/E;AAA+E;AAA/E;EAAA,kBAA+E;EAA/E;AAA+E;AAA/E;EAAA,kBAA+E;EAA/E;AAA+E;AAI/E;EAAA,oBAAyE;EAAzE,mBAAyE;EAAzE,uBAAyE;EAAzE,uBAAyE;EAAzE,mBAAyE;EAAzE,oBAAyE;EAAzE,gBAAyE;EAAzE,+FAAyE;EAAzE,wDAAyE;EAAzE;AAAyE;AAAzE;EAAA,8BAAyE;EAAzE,mBAAyE;EAAzE,2GAAyE;EAAzE,yGAAyE;EAAzE,4FAAyE;EAAzE,oBAAyE;EAAzE,4DAAyE;EAAzE,2BAAyE;EAAzE;AAAyE;AAAzE;EAAA,oBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE,yDAAyE;EAAzE,oBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE;AAAyE;AAAzE;EAAA,kBAAyE;EAAzE;AAAyE;AAYzE;EAAA,YAAuB;EAAvB,qBAAuB;EAAvB,sBAAuB;EAAvB,kBAAuB;EAAvB;AAAuB;AAIvB;EAAA,cAAqB;EAArB,kBAAqB;EAArB,mBAAqB;EAArB,mBAAqB;EAArB;AAAqB;AAOvB,iBAAiB;AAEf;EAAA,aAA8S;EAA9S,cAA8S;EAA9S,WAA8S;EAA9S,uBAA8S;EAA9S,iBAA8S;EAA9S,sBAA8S;EAA9S,yDAA8S;EAA9S,kBAA8S;EAA9S,yDAA8S;EAA9S,qBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,oBAA8S;EAA9S,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,sBAA8S;EAA9S,2DAA8S;EAA9S,8BAA8S;EAA9S,mBAA8S;EAA9S,2GAA8S;EAA9S,yGAA8S;EAA9S,4FAA8S;EAA9S,oBAA8S;EAA9S,4DAA8S;EAA9S,2BAA8S;EAA9S;AAA8S;AAA9S;EAAA,mBAA8S;EAA9S;AAA8S;AAA9S;EAAA,aAA8S;EAA9S,cAA8S;EAA9S,WAA8S;EAA9S,uBAA8S;EAA9S,iBAA8S;EAA9S,sBAA8S;EAA9S,yDAA8S;EAA9S,kBAA8S;EAA9S,yDAA8S;EAA9S,qBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,sBAA8S;EAA9S,mBAA8S;EAA9S,oBAA8S;EAA9S,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,oBAA8S;EAA9S;AAA8S;AAA9S;EAAA,sBAA8S;EAA9S,2DAA8S;EAA9S,8BAA8S;EAA9S,mBAA8S;EAA9S,2GAA8S;EAA9S,yGAA8S;EAA9S,4FAA8S;EAA9S,oBAA8S;EAA9S,4DAA8S;EAA9S,2BAA8S;EAA9S;AAA8S;AAA9S;EAAA,mBAA8S;EAA9S;AAA8S;AAI9S;EAAA,aAAsT;EAAtT,gBAAsT;EAAtT,WAAsT;EAAtT,uBAAsT;EAAtT,iBAAsT;EAAtT,sBAAsT;EAAtT,yDAAsT;EAAtT,kBAAsT;EAAtT,yDAAsT;EAAtT,qBAAsT;EAAtT,sBAAsT;EAAtT,mBAAsT;EAAtT,sBAAsT;EAAtT,mBAAsT;EAAtT,oBAAsT;EAAtT,oBAAsT;EAAtT;AAAsT;AAAtT;EAAA,oBAAsT;EAAtT;AAAsT;AAAtT;EAAA,oBAAsT;EAAtT;AAAsT;AAAtT;EAAA,sBAAsT;EAAtT,2DAAsT;EAAtT,8BAAsT;EAAtT,mBAAsT;EAAtT,2GAAsT;EAAtT,yGAAsT;EAAtT,4FAAsT;EAAtT,oBAAsT;EAAtT,4DAAsT;EAAtT,2BAAsT;EAAtT;AAAsT;AAAtT;EAAA,mBAAsT;EAAtT;AAAsT;AAGxT,gBAAgB;AAqBhB,iBAAiB;AAyBjB,mBAAmB;AAEjB;;EAAA;IAAA;EAA8E;AAAA;AAA9E;EAAA,kCAA8E;EAA9E,qBAA8E;EAA9E,iBAA8E;EAA9E,yDAA8E;EAA9E,sBAA8E;EAA9E;AAA8E;AAQhF;IACE,UAAU,WAAW,EAAE;IACvB,MAAM,YAAY,EAAE;IACpB,MAAM,aAAa,EAAE;IACrB,YAAY,cAAc,EAAE;EAC9B;AAEA,eAAe;AASf;IACE,mCAAmC;EACrC;AAEA;IACE,OAAO,UAAU,EAAE;IACnB,KAAK,UAAU,EAAE;EACnB;AAEA;IACE;MACE,UAAU;MACV,2BAA2B;IAC7B;IACA;MACE,UAAU;MACV,wBAAwB;IAC1B;EACF;AAEA;IACE;MACE,UAAU;MACV,4BAA4B;IAC9B;IACA;MACE,UAAU;MACV,wBAAwB;IAC1B;EACF;AAEA,wCAAwC;AAaxC,uBAAuB;AAiBvB,0BAA0B;AAExB;EAAA,cAAmD;EAAnD,yBAAmD;EAAnD,qBAAmD;EAAnD,kBAAmD;EAAnD,mBAAmD;EAAnD,oBAAmD;EAAnD;AAAmD;AAerD,6BAA6B;AAE3B;EAAA,iBAAiC;EAAjC,sBAAiC;EAAjC;AAAiC;AAGnC;IACE,WAAW;IACX,oBAA+F;IAA/F,kBAA+F;IAA/F,UAA+F;EACjG;AADE;;EAAA;IAAA;EAA+F;AAAA;AAA/F;EAAA,yDAA+F;EAA/F,qBAA+F;EAA/F,iBAA+F;EAA/F,sBAA+F;EAA/F;AAA+F;AAGjG,uBAAuB;AAavB,wBAAwB;AAEtB;EAAA,kBAA6G;EAA7G,WAA6G;EAA7G,eAA6G;EAA7G,gBAA6G;EAA7G,uBAA6G;EAA7G,iBAA6G;EAA7G,sBAA6G;EAA7G,yDAA6G;EAA7G,kBAA6G;EAA7G,yDAA6G;EAA7G,gBAA6G;EAA7G,6EAA6G;EAA7G,iGAA6G;EAA7G;AAA6G;AAI7G;EAAA,kBAA0J;EAA1J,aAA0J;EAA1J,eAA0J;EAA1J,yBAA0J;KAA1J,sBAA0J;UAA1J,iBAA0J;EAA1J,mBAA0J;EAA1J,uBAA0J;EAA1J,oBAA0J;EAA1J,qBAA0J;EAA1J,qBAA0J;EAA1J,wBAA0J;EAA1J,mBAA0J;EAA1J,oBAA0J;EAA1J,oBAA0J;EAA1J,mDAA0J;EAA1J,8BAA0J;EAA1J;AAA0J;AAA1J;EAAA,kBAA0J;EAA1J;AAA0J;AAA1J;EAAA,kBAA0J;EAA1J;AAA0J;AAvQ9J;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,2BAAmB;IAAnB;EAAmB;;EAAnB;IAAA,eAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,wJAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAgRjB,qBAAqB;AAiBrB,oBAAoB;AAKpB,6CAA6C;AAK7C;IACE,wBAAwB;EAC1B;AAEA;IACE,2BAA2B;EAC7B;;AAnTF;EAAA;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA,kBAoTC;EApTD;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,sBAoTC;EApTD;AAoTC;;AApTD;EAAA,8BAoTC;EApTD;AAoTC;;AApTD;EAAA,2GAoTC;EApTD,yGAoTC;EApTD;AAoTC;;AApTD;EAAA,2GAoTC;EApTD,yGAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA,oBAoTC;EApTD;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC;;AApTD;EAAA;AAoTC\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    border-color: theme('colors.gray.200');\\r\\n  }\\r\\n\\r\\n  html {\\r\\n    font-family: 'Inter', system-ui, sans-serif;\\r\\n  }\\r\\n\\r\\n  body {\\r\\n    @apply bg-gray-900 text-gray-50;\\r\\n  }\\r\\n\\r\\n  /* Custom scrollbar styles */\\r\\n  * {\\r\\n    scrollbar-width: thin;\\r\\n    scrollbar-color: theme('colors.gray.600') theme('colors.gray.800');\\r\\n  }\\r\\n\\r\\n  *::-webkit-scrollbar {\\r\\n    width: 8px;\\r\\n    height: 8px;\\r\\n  }\\r\\n\\r\\n  *::-webkit-scrollbar-track {\\r\\n    @apply bg-gray-800 rounded;\\r\\n  }\\r\\n\\r\\n  *::-webkit-scrollbar-thumb {\\r\\n    @apply bg-gray-600 rounded hover:bg-gray-500;\\r\\n  }\\r\\n\\r\\n  *::-webkit-scrollbar-corner {\\r\\n    @apply bg-gray-800;\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer components {\\r\\n  /* Button variants */\\r\\n  .btn {\\r\\n    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900 disabled:pointer-events-none disabled:opacity-50;\\r\\n  }\\r\\n\\r\\n  .btn-primary {\\r\\n    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;\\r\\n  }\\r\\n\\r\\n  .btn-secondary {\\r\\n    @apply btn bg-gray-700 text-gray-200 hover:bg-gray-600 active:bg-gray-800;\\r\\n  }\\r\\n\\r\\n  .btn-ghost {\\r\\n    @apply btn hover:bg-gray-800 hover:text-gray-100;\\r\\n  }\\r\\n\\r\\n  .btn-danger {\\r\\n    @apply btn bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;\\r\\n  }\\r\\n\\r\\n  .btn-sm {\\r\\n    @apply h-8 px-3 text-xs;\\r\\n  }\\r\\n\\r\\n  .btn-md {\\r\\n    @apply h-10 px-4 py-2;\\r\\n  }\\r\\n\\r\\n  .btn-lg {\\r\\n    @apply h-12 px-8 text-base;\\r\\n  }\\r\\n\\r\\n  /* Input styles */\\r\\n  .input {\\r\\n    @apply flex h-10 w-full rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 placeholder:text-gray-400 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:cursor-not-allowed disabled:opacity-50;\\r\\n  }\\r\\n\\r\\n  .textarea {\\r\\n    @apply flex min-h-[80px] w-full rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 placeholder:text-gray-400 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:cursor-not-allowed disabled:opacity-50;\\r\\n  }\\r\\n\\r\\n  /* Card styles */\\r\\n  .card {\\r\\n    @apply rounded-lg border border-gray-700 bg-gray-800 p-6 shadow-lg;\\r\\n  }\\r\\n\\r\\n  .card-header {\\r\\n    @apply flex flex-col space-y-1.5 p-6;\\r\\n  }\\r\\n\\r\\n  .card-title {\\r\\n    @apply text-2xl font-semibold leading-none tracking-tight text-gray-100;\\r\\n  }\\r\\n\\r\\n  .card-description {\\r\\n    @apply text-sm text-gray-400;\\r\\n  }\\r\\n\\r\\n  .card-content {\\r\\n    @apply p-6 pt-0;\\r\\n  }\\r\\n\\r\\n  /* Badge styles */\\r\\n  .badge {\\r\\n    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;\\r\\n  }\\r\\n\\r\\n  .badge-default {\\r\\n    @apply badge bg-gray-700 text-gray-200;\\r\\n  }\\r\\n\\r\\n  .badge-primary {\\r\\n    @apply badge bg-primary-100 text-primary-800;\\r\\n  }\\r\\n\\r\\n  .badge-success {\\r\\n    @apply badge bg-success-100 text-success-800;\\r\\n  }\\r\\n\\r\\n  .badge-warning {\\r\\n    @apply badge bg-warning-100 text-warning-800;\\r\\n  }\\r\\n\\r\\n  .badge-danger {\\r\\n    @apply badge bg-danger-100 text-danger-800;\\r\\n  }\\r\\n\\r\\n  /* Loading states */\\r\\n  .loading-spinner {\\r\\n    @apply animate-spin rounded-full border-2 border-gray-600 border-t-primary-500;\\r\\n  }\\r\\n\\r\\n  .loading-dots::after {\\r\\n    content: '';\\r\\n    animation: loading-dots 1.5s steps(4, end) infinite;\\r\\n  }\\r\\n\\r\\n  @keyframes loading-dots {\\r\\n    0%, 20% { content: ''; }\\r\\n    40% { content: '.'; }\\r\\n    60% { content: '..'; }\\r\\n    80%, 100% { content: '...'; }\\r\\n  }\\r\\n\\r\\n  /* Animations */\\r\\n  .fade-in {\\r\\n    animation: fade-in 0.5s ease-in-out;\\r\\n  }\\r\\n\\r\\n  .slide-up {\\r\\n    animation: slide-up 0.3s ease-out;\\r\\n  }\\r\\n\\r\\n  .slide-down {\\r\\n    animation: slide-down 0.3s ease-out;\\r\\n  }\\r\\n\\r\\n  @keyframes fade-in {\\r\\n    from { opacity: 0; }\\r\\n    to { opacity: 1; }\\r\\n  }\\r\\n\\r\\n  @keyframes slide-up {\\r\\n    from {\\r\\n      opacity: 0;\\r\\n      transform: translateY(10px);\\r\\n    }\\r\\n    to {\\r\\n      opacity: 1;\\r\\n      transform: translateY(0);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  @keyframes slide-down {\\r\\n    from {\\r\\n      opacity: 0;\\r\\n      transform: translateY(-10px);\\r\\n    }\\r\\n    to {\\r\\n      opacity: 1;\\r\\n      transform: translateY(0);\\r\\n    }\\r\\n  }\\r\\n\\r\\n  /* Syntax highlighting for code blocks */\\r\\n  .code-block {\\r\\n    @apply bg-gray-900 rounded-lg p-4 overflow-x-auto;\\r\\n  }\\r\\n\\r\\n  .code-block pre {\\r\\n    @apply text-sm font-mono text-gray-100;\\r\\n  }\\r\\n\\r\\n  .code-block code {\\r\\n    @apply font-mono text-sm;\\r\\n  }\\r\\n\\r\\n  /* Diff viewer styles */\\r\\n  .diff-line {\\r\\n    @apply font-mono text-sm px-4 py-1 border-l-4;\\r\\n  }\\r\\n\\r\\n  .diff-line-add {\\r\\n    @apply diff-line bg-success-900/20 border-success-500 text-success-100;\\r\\n  }\\r\\n\\r\\n  .diff-line-remove {\\r\\n    @apply diff-line bg-danger-900/20 border-danger-500 text-danger-100;\\r\\n  }\\r\\n\\r\\n  .diff-line-normal {\\r\\n    @apply diff-line bg-gray-800 border-gray-600 text-gray-200;\\r\\n  }\\r\\n\\r\\n  /* Message bubble styles */\\r\\n  .message-bubble {\\r\\n    @apply rounded-lg px-4 py-3 max-w-[80%] break-words;\\r\\n  }\\r\\n\\r\\n  .message-bubble-user {\\r\\n    @apply message-bubble bg-primary-600 text-white ml-auto;\\r\\n  }\\r\\n\\r\\n  .message-bubble-assistant {\\r\\n    @apply message-bubble bg-gray-700 text-gray-100 mr-auto;\\r\\n  }\\r\\n\\r\\n  .message-bubble-system {\\r\\n    @apply message-bubble bg-gray-800 text-gray-300 border border-gray-600 mx-auto text-center text-sm;\\r\\n  }\\r\\n\\r\\n  /* Execution mode indicator */\\r\\n  .yolo-mode {\\r\\n    @apply border-2 border-danger-500;\\r\\n  }\\r\\n\\r\\n  .yolo-mode::before {\\r\\n    content: '';\\r\\n    @apply absolute inset-0 pointer-events-none border-2 border-danger-500 rounded-lg animate-pulse;\\r\\n  }\\r\\n\\r\\n  /* Tool result styles */\\r\\n  .tool-result {\\r\\n    @apply border border-gray-600 rounded-lg p-3 bg-gray-800;\\r\\n  }\\r\\n\\r\\n  .tool-result-success {\\r\\n    @apply tool-result border-success-600 bg-success-900/10;\\r\\n  }\\r\\n\\r\\n  .tool-result-error {\\r\\n    @apply tool-result border-danger-600 bg-danger-900/10;\\r\\n  }\\r\\n\\r\\n  /* Context menu styles */\\r\\n  .context-menu {\\r\\n    @apply absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-600 bg-gray-800 p-1 shadow-md;\\r\\n  }\\r\\n\\r\\n  .context-menu-item {\\r\\n    @apply relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm text-gray-200 outline-none hover:bg-gray-700 focus:bg-gray-700;\\r\\n  }\\r\\n\\r\\n  .context-menu-separator {\\r\\n    @apply -mx-1 my-1 h-px bg-gray-600;\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer utilities {\\r\\n  /* Custom utilities */\\r\\n  .text-balance {\\r\\n    text-wrap: balance;\\r\\n  }\\r\\n\\r\\n  .scrollbar-thin {\\r\\n    scrollbar-width: thin;\\r\\n  }\\r\\n\\r\\n  .scrollbar-none {\\r\\n    scrollbar-width: none;\\r\\n  }\\r\\n\\r\\n  .scrollbar-none::-webkit-scrollbar {\\r\\n    display: none;\\r\\n  }\\r\\n\\r\\n  /* Focus utilities */\\r\\n  .focus-ring {\\r\\n    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900;\\r\\n  }\\r\\n\\r\\n  /* Safe area utilities for frameless window */\\r\\n  .safe-area-top {\\r\\n    padding-top: env(titlebar-area-height, 0px);\\r\\n  }\\r\\n\\r\\n  .drag-region {\\r\\n    -webkit-app-region: drag;\\r\\n  }\\r\\n\\r\\n  .no-drag {\\r\\n    -webkit-app-region: no-drag;\\r\\n  }\\r\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "import React, { useEffect } from 'react';\r\nimport { useAppStore } from './store/appStore';\r\nimport { useSettings, useConversations, useSystemInfo, useToast } from './hooks/useElectronAPI';\r\n// import { useHotkeys } from './hooks/useHotkeys';\r\nimport TitleBar from './components/ui/TitleBar';\r\nimport Sidebar from './components/ui/Sidebar';\r\nimport ChatInterface from './components/chat/ChatInterface';\r\nimport SettingsPanel from './components/settings/SettingsPanel';\r\nimport LoadingScreen from './components/ui/LoadingScreen';\r\nimport ErrorToast from './components/ui/ErrorToast';\r\nimport ToastContainer from './components/ui/ToastContainer';\r\nimport YoloModeIndicator from './components/ui/YoloModeIndicator';\r\nimport { useIsYoloMode } from './store/appStore';\r\n\r\nconst App: React.FC = () => {\r\n  const {\r\n    currentView,\r\n    sidebarOpen,\r\n    settingsOpen,\r\n    loading,\r\n    error,\r\n    settings,\r\n  } = useAppStore();\r\n\r\n  const { loadSettings } = useSettings();\r\n  const { loadConversations } = useConversations();\r\n  const { loadSystemInfo } = useSystemInfo();\r\n  const { toasts, hideToast } = useToast();\r\n  const isYoloMode = useIsYoloMode();\r\n\r\n  // Initialize hotkeys\r\n  // useHotkeys();\r\n\r\n  // Initialize the app on mount\r\n  useEffect(() => {\r\n    const initialize = async () => {\r\n      try {\r\n        // Load initial data\r\n        await Promise.all([\r\n          loadSettings(),\r\n          loadConversations(),\r\n          loadSystemInfo(),\r\n        ]);\r\n      } catch (error) {\r\n        console.error('Failed to initialize app:', error);\r\n      }\r\n    };\r\n\r\n    initialize();\r\n  }, [loadSettings, loadConversations, loadSystemInfo]);\r\n\r\n  // Show loading screen during initialization\r\n  if (loading || !settings) {\r\n    return <LoadingScreen />;\r\n  }\r\n\r\n  return (\r\n    <div className={`h-screen w-screen flex flex-col bg-gray-900 text-gray-100 ${isYoloMode ? 'yolo-mode' : ''}`}>\r\n      {/* Title Bar */}\r\n      <TitleBar />\r\n      \r\n      {/* Main Content Area */}\r\n      <div className=\"flex flex-1 overflow-hidden\">\r\n        {/* Sidebar */}\r\n        {sidebarOpen && (\r\n          <div className=\"w-80 flex-shrink-0 border-r border-gray-700\">\r\n            <Sidebar />\r\n          </div>\r\n        )}\r\n        \r\n        {/* Main Content */}\r\n        <div className=\"flex-1 flex flex-col relative\">\r\n          {currentView === 'chat' && <ChatInterface />}\r\n          {currentView === 'settings' && <SettingsPanel />}\r\n          {currentView === 'logs' && <LogsPanel />}\r\n          \r\n          {/* Settings Overlay */}\r\n          {settingsOpen && (\r\n            <div className=\"absolute inset-0 z-50 bg-black/50 backdrop-blur-sm\">\r\n              <div className=\"h-full flex items-center justify-center p-4\">\r\n                <div className=\"bg-gray-800 border border-gray-700 rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-auto\">\r\n                  <SettingsPanel />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Error Toast */}\r\n      {error && <ErrorToast message={error} />}\r\n\r\n      {/* Toast Container */}\r\n      <ToastContainer toasts={toasts} onRemove={hideToast} />\r\n\r\n      {/* YOLO Mode Indicator */}\r\n      <div className=\"fixed bottom-4 right-4 z-40\">\r\n        <YoloModeIndicator />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Placeholder component for logs panel\r\nconst LogsPanel: React.FC = () => {\r\n  return (\r\n    <div className=\"flex-1 p-6\">\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        <h1 className=\"text-2xl font-bold text-gray-100 mb-6\">System Logs</h1>\r\n        <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-4\">\r\n          <div className=\"font-mono text-sm text-gray-300 space-y-1\">\r\n            <div className=\"text-success-400\">[INFO] Application started successfully</div>\r\n            <div className=\"text-primary-400\">[DEBUG] Loading settings...</div>\r\n            <div className=\"text-success-400\">[INFO] Settings loaded</div>\r\n            <div className=\"text-primary-400\">[DEBUG] Loading conversations...</div>\r\n            <div className=\"text-success-400\">[INFO] Conversations loaded</div>\r\n            <div className=\"text-warning-400\">[WARN] No API key configured</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default App;", "import React, { useState, useRef, useEffect } from 'react';\r\nimport { useAppStore } from '@renderer/store/appStore';\r\nimport { Send, Paperclip, Square, Mic, MicOff } from 'lucide-react';\r\n\r\nconst ChatInput: React.FC = () => {\r\n  const {\r\n    currentConversation,\r\n    isStreaming,\r\n    agentState,\r\n    sendMessage,\r\n    stopGeneration,\r\n  } = useAppStore();\r\n\r\n  const [input, setInput] = useState('');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Auto-resize textarea\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 200)}px`;\r\n    }\r\n  }, [input]);\r\n\r\n  // Focus input when conversation changes\r\n  useEffect(() => {\r\n    if (currentConversation && textareaRef.current) {\r\n      textareaRef.current.focus();\r\n    }\r\n  }, [currentConversation]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!input.trim() || !currentConversation || isStreaming) {\r\n      return;\r\n    }\r\n\r\n    const message = input.trim();\r\n    setInput('');\r\n    \r\n    try {\r\n      await sendMessage(currentConversation.id, message);\r\n    } catch (error) {\r\n      console.error('Failed to send message:', error);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSubmit(e);\r\n    }\r\n  };\r\n\r\n  const handleStop = () => {\r\n    stopGeneration();\r\n  };\r\n\r\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = Array.from(e.target.files || []);\r\n    if (files.length > 0) {\r\n      // TODO: Implement file upload functionality\r\n      console.log('Files selected:', files);\r\n    }\r\n  };\r\n\r\n  const toggleRecording = () => {\r\n    if (isRecording) {\r\n      // TODO: Stop recording and process audio\r\n      setIsRecording(false);\r\n    } else {\r\n      // TODO: Start voice recording\r\n      setIsRecording(true);\r\n    }\r\n  };\r\n\r\n  const canSend = input.trim() && currentConversation && !isStreaming;\r\n  const isWaitingForConfirmation = agentState?.isWaitingForConfirmation;\r\n  const isDisabled = isStreaming || isWaitingForConfirmation;\r\n\r\n  if (!currentConversation) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"border-t border-gray-700 bg-gray-800 p-4\">\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        {/* Status Messages */}\r\n        {isWaitingForConfirmation && (\r\n          <div className=\"mb-3 p-3 bg-warning-900/50 border border-warning-700 rounded-lg text-warning-200 text-sm\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-warning-400 rounded-full animate-pulse\"></div>\r\n              <span>The agent is waiting for your confirmation before proceeding.</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {isStreaming && (\r\n          <div className=\"mb-3 p-3 bg-blue-900/50 border border-blue-700 rounded-lg text-blue-200 text-sm\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></div>\r\n                <span>Assistant is responding...</span>\r\n              </div>\r\n              <button\r\n                onClick={handleStop}\r\n                className=\"px-2 py-1 bg-blue-700 hover:bg-blue-600 rounded text-xs transition-colors\"\r\n              >\r\n                <Square className=\"w-3 h-3 inline mr-1\" />\r\n                Stop\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Input Form */}\r\n        <form onSubmit={handleSubmit} className=\"relative\">\r\n          <div className=\"flex items-end gap-2\">\r\n            {/* File Upload */}\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => fileInputRef.current?.click()}\r\n              disabled={isDisabled}\r\n              className={`p-2 rounded-lg transition-colors ${\r\n                isDisabled\r\n                  ? 'text-gray-600 cursor-not-allowed'\r\n                  : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'\r\n              }`}\r\n              title=\"Attach files\"\r\n            >\r\n              <Paperclip className=\"w-5 h-5\" />\r\n            </button>\r\n\r\n            {/* Text Input */}\r\n            <div className=\"flex-1 relative\">\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={input}\r\n                onChange={(e) => setInput(e.target.value)}\r\n                onKeyDown={handleKeyDown}\r\n                placeholder={\r\n                  isDisabled\r\n                    ? 'Please wait...'\r\n                    : 'Type your message... (Enter to send, Shift+Enter for new line)'\r\n                }\r\n                disabled={isDisabled}\r\n                className={`w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl resize-none transition-colors ${\r\n                  isDisabled\r\n                    ? 'opacity-50 cursor-not-allowed'\r\n                    : 'focus:border-primary-500 focus:ring-1 focus:ring-primary-500'\r\n                } text-gray-100 placeholder-gray-400`}\r\n                rows={1}\r\n                style={{ maxHeight: '200px' }}\r\n              />\r\n            </div>\r\n\r\n            {/* Voice Recording */}\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleRecording}\r\n              disabled={isDisabled}\r\n              className={`p-2 rounded-lg transition-colors ${\r\n                isRecording\r\n                  ? 'bg-red-600 text-white'\r\n                  : isDisabled\r\n                  ? 'text-gray-600 cursor-not-allowed'\r\n                  : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'\r\n              }`}\r\n              title={isRecording ? 'Stop recording' : 'Start voice recording'}\r\n            >\r\n              {isRecording ? (\r\n                <MicOff className=\"w-5 h-5\" />\r\n              ) : (\r\n                <Mic className=\"w-5 h-5\" />\r\n              )}\r\n            </button>\r\n\r\n            {/* Send Button */}\r\n            <button\r\n              type=\"submit\"\r\n              disabled={!canSend}\r\n              className={`p-2 rounded-lg transition-colors ${\r\n                canSend\r\n                  ? 'bg-primary-600 hover:bg-primary-700 text-white'\r\n                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'\r\n              }`}\r\n              title=\"Send message (Enter)\"\r\n            >\r\n              <Send className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Hidden File Input */}\r\n          <input\r\n            ref={fileInputRef}\r\n            type=\"file\"\r\n            multiple\r\n            onChange={handleFileUpload}\r\n            className=\"hidden\"\r\n            accept=\".txt,.md,.json,.csv,.pdf,.doc,.docx,.png,.jpg,.jpeg,.gif\"\r\n          />\r\n        </form>\r\n\r\n        {/* Keyboard Shortcuts */}\r\n        <div className=\"mt-2 text-xs text-gray-500 text-center\">\r\n          Press <kbd className=\"px-1 py-0.5 bg-gray-700 rounded\">Enter</kbd> to send,{' '}\r\n          <kbd className=\"px-1 py-0.5 bg-gray-700 rounded\">Shift</kbd> +{' '}\r\n          <kbd className=\"px-1 py-0.5 bg-gray-700 rounded\">Enter</kbd> for new line\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInput;", "import React, { useEffect, useRef } from 'react';\r\nimport { useAppStore, useCurrentMessages } from '@renderer/store/appStore';\r\nimport MessageBubble from './MessageBubble';\r\nimport ChatInput from './ChatInput';\r\nimport PlanConfirmation from './PlanConfirmation';\r\nimport ToolResultDisplay from './ToolResultDisplay';\r\nimport StreamingMessage from './StreamingMessage';\r\nimport { Bot, MessageSquare } from 'lucide-react';\r\n\r\nconst ChatInterface: React.FC = () => {\r\n  const {\r\n    currentConversation,\r\n    currentPlan,\r\n    toolResults,\r\n    isStreaming,\r\n    streamingMessage,\r\n    agentState,\r\n  } = useAppStore();\r\n  \r\n  const messages = useCurrentMessages();\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Auto-scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    if (messagesEndRef.current) {\r\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  }, [messages, streamingMessage, toolResults]);\r\n\r\n  // Show welcome screen if no conversation is selected\r\n  if (!currentConversation) {\r\n    return (\r\n      <div className=\"flex-1 flex items-center justify-center bg-gray-900\">\r\n        <div className=\"text-center max-w-md\">\r\n          <div className=\"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl\">\r\n            <Bot className=\"w-10 h-10 text-white\" />\r\n          </div>\r\n          \r\n          <h2 className=\"text-2xl font-bold text-gray-100 mb-4\">\r\n            Welcome to AI Assistant\r\n          </h2>\r\n          \r\n          <p className=\"text-gray-400 mb-6\">\r\n            Start a new conversation or select an existing one from the sidebar to begin chatting with your AI assistant.\r\n          </p>\r\n          \r\n          <div className=\"text-sm text-gray-500 space-y-2\">\r\n            <p>✨ Multi-LLM provider support</p>\r\n            <p>🛠️ Extensible tooling system</p>\r\n            <p>🤖 Intelligent agent workflow</p>\r\n            <p>💾 Conversation persistence</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex-1 flex flex-col bg-gray-900\">\r\n      {/* Chat Header */}\r\n      <div className=\"h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-6\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <MessageSquare className=\"w-5 h-5 text-gray-400\" />\r\n          <h1 className=\"text-lg font-semibold text-gray-100\">\r\n            {currentConversation.title}\r\n          </h1>\r\n          {messages.length > 0 && (\r\n            <span className=\"text-sm text-gray-500\">\r\n              {messages.length} message{messages.length !== 1 ? 's' : ''}\r\n            </span>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\r\n          {agentState?.isWaitingForConfirmation && (\r\n            <div className=\"flex items-center space-x-1\">\r\n              <div className=\"w-2 h-2 bg-warning-400 rounded-full animate-pulse\"></div>\r\n              <span>Waiting for confirmation</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Messages Area */}\r\n      <div className=\"flex-1 overflow-y-auto p-6 space-y-4\">\r\n        {messages.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <Bot className=\"w-12 h-12 text-gray-600 mx-auto mb-4\" />\r\n            <h3 className=\"text-lg font-medium text-gray-300 mb-2\">\r\n              Start the conversation\r\n            </h3>\r\n            <p className=\"text-gray-500\">\r\n              Ask me anything or give me a task to help you with.\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {messages.map((message) => (\r\n              <MessageBubble key={message.id} message={message} />\r\n            ))}\r\n            \r\n            {/* Streaming Message */}\r\n            {isStreaming && streamingMessage && (\r\n              <StreamingMessage content={streamingMessage} />\r\n            )}\r\n            \r\n            {/* Tool Results */}\r\n            {toolResults.length > 0 && (\r\n              <div className=\"space-y-3\">\r\n                <h4 className=\"text-sm font-medium text-gray-400 flex items-center space-x-2\">\r\n                  <span>Tool Execution Results</span>\r\n                  <div className=\"flex-1 h-px bg-gray-700\"></div>\r\n                </h4>\r\n                {toolResults.map((result) => (\r\n                  <ToolResultDisplay key={result.id} result={result} />\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            {/* Plan Confirmation */}\r\n            {currentPlan && agentState?.isWaitingForConfirmation && (\r\n              <PlanConfirmation plan={currentPlan} />\r\n            )}\r\n          </>\r\n        )}\r\n        \r\n        {/* Auto-scroll anchor */}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {/* Chat Input */}\r\n      <ChatInput />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInterface;", "import React, { useState } from 'react';\r\nimport { Message } from '@shared/types';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Check, Clock, AlertCircle } from 'lucide-react';\r\nimport { format } from 'date-fns';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';\r\nimport { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';\r\n\r\ninterface MessageBubbleProps {\r\n  message: Message;\r\n}\r\n\r\nconst MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {\r\n  const [copied, setCopied] = useState(false);\r\n  const isUser = message.role === 'user';\r\n  const isSystem = message.role === 'system';\r\n  \r\n  const handleCopy = async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(message.content);\r\n      setCopied(true);\r\n      setTimeout(() => setCopied(false), 2000);\r\n    } catch (error) {\r\n      console.error('Failed to copy message:', error);\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = () => {\r\n    switch (message.status) {\r\n      case 'sending':\r\n        return <Clock className=\"w-3 h-3 text-gray-500 animate-pulse\" />;\r\n      case 'error':\r\n        return <AlertCircle className=\"w-3 h-3 text-red-500\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const formatTime = (timestamp: Date) => {\r\n    return format(new Date(timestamp), 'HH:mm');\r\n  };\r\n\r\n  if (isSystem) {\r\n    return (\r\n      <div className=\"flex justify-center\">\r\n        <div className=\"max-w-xs px-3 py-1 text-xs text-gray-500 bg-gray-800 rounded-full\">\r\n          {message.content}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`flex gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>\r\n      {/* Avatar */}\r\n      <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>\r\n        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n          isUser \r\n            ? 'bg-primary-600 text-white' \r\n            : 'bg-gradient-to-br from-purple-500 to-pink-600 text-white'\r\n        }`}>\r\n          {isUser ? (\r\n            <User className=\"w-4 h-4\" />\r\n          ) : (\r\n            <Bot className=\"w-4 h-4\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Message Content */}\r\n      <div className={`flex-1 max-w-3xl ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>\r\n        <div\r\n          className={`px-4 py-3 rounded-2xl shadow-sm relative group ${\r\n            isUser\r\n              ? 'bg-primary-600 text-white ml-12'\r\n              : 'bg-gray-800 text-gray-100 mr-12'\r\n          } ${message.status === 'error' ? 'ring-2 ring-red-500/50' : ''}`}\r\n        >\r\n          {/* Message Content */}\r\n          <div className=\"prose prose-sm max-w-none\">\r\n            {isUser ? (\r\n              <div className=\"whitespace-pre-wrap break-words\">\r\n                {message.content}\r\n              </div>\r\n            ) : (\r\n              <ReactMarkdown\r\n                components={{\r\n                  code({ node, inline, className, children, ...props }) {\r\n                    const match = /language-(\\w+)/.exec(className || '');\r\n                    const language = match ? match[1] : '';\r\n\r\n                    if (!inline && language) {\r\n                      return (\r\n                        <div className=\"relative\">\r\n                          <SyntaxHighlighter\r\n                            style={oneDark}\r\n                            language={language}\r\n                            PreTag=\"div\"\r\n                            className=\"rounded-lg my-2\"\r\n                            {...props}\r\n                          >\r\n                            {String(children).replace(/\\n$/, '')}\r\n                          </SyntaxHighlighter>\r\n                          <button\r\n                            onClick={() => navigator.clipboard.writeText(String(children))}\r\n                            className=\"absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-200 transition-colors\"\r\n                            title=\"Copy code\"\r\n                          >\r\n                            <Copy className=\"w-3 h-3\" />\r\n                          </button>\r\n                        </div>\r\n                      );\r\n                    }\r\n\r\n                    return (\r\n                      <code className=\"bg-gray-700 px-1 py-0.5 rounded text-sm\" {...props}>\r\n                        {children}\r\n                      </code>\r\n                    );\r\n                  },\r\n                  p: ({ children }) => (\r\n                    <div className=\"mb-2 last:mb-0 leading-relaxed text-sm\">\r\n                      {children}\r\n                    </div>\r\n                  ),\r\n                  ul: ({ children }) => (\r\n                    <ul className=\"list-disc list-inside space-y-1 text-sm mb-2\">\r\n                      {children}\r\n                    </ul>\r\n                  ),\r\n                  ol: ({ children }) => (\r\n                    <ol className=\"list-decimal list-inside space-y-1 text-sm mb-2\">\r\n                      {children}\r\n                    </ol>\r\n                  ),\r\n                  blockquote: ({ children }) => (\r\n                    <blockquote className=\"border-l-2 border-gray-600 pl-3 italic text-gray-300 text-sm\">\r\n                      {children}\r\n                    </blockquote>\r\n                  ),\r\n                }}\r\n                className={isUser ? 'prose-invert' : ''}\r\n              >\r\n                {message.content}\r\n              </ReactMarkdown>\r\n            )}\r\n          </div>\r\n\r\n          {/* Copy Button */}\r\n          <button\r\n            onClick={handleCopy}\r\n            className={`absolute top-2 right-2 p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity ${\r\n              isUser \r\n                ? 'text-primary-200 hover:text-white hover:bg-primary-700' \r\n                : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'\r\n            }`}\r\n            title=\"Copy message\"\r\n          >\r\n            {copied ? (\r\n              <Check className=\"w-3 h-3\" />\r\n            ) : (\r\n              <Copy className=\"w-3 h-3\" />\r\n            )}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Message Footer */}\r\n        <div className={`flex items-center gap-2 mt-1 text-xs text-gray-500 ${\r\n          isUser ? 'flex-row-reverse' : 'flex-row'\r\n        }`}>\r\n          <span>{formatTime(message.timestamp)}</span>\r\n          {getStatusIcon()}\r\n          {message.tokens && (\r\n            <span className=\"text-gray-600\">\r\n              {message.tokens} tokens\r\n            </span>\r\n          )}\r\n          {message.model && !isUser && (\r\n            <span className=\"text-gray-600\">\r\n              {message.model}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Error Message */}\r\n        {message.status === 'error' && message.error && (\r\n          <div className=\"mt-2 px-3 py-2 bg-red-900/50 border border-red-800 rounded-lg text-sm text-red-200\">\r\n            <div className=\"flex items-center gap-2 mb-1\">\r\n              <AlertCircle className=\"w-4 h-4\" />\r\n              <span className=\"font-medium\">Error</span>\r\n            </div>\r\n            <div className=\"text-red-300\">\r\n              {message.error}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MessageBubble;", "import React from 'react';\r\nimport { useAppStore } from '@renderer/store/appStore';\r\nimport { AgentPlan } from '@shared/types';\r\nimport { CheckCircle, XCircle, Clock, AlertTriangle, Wrench as Tool, FileText, Code } from 'lucide-react';\r\n\r\ninterface PlanConfirmationProps {\r\n  plan: AgentPlan;\r\n}\r\n\r\nconst PlanConfirmation: React.FC<PlanConfirmationProps> = ({ plan }) => {\r\n  const { confirmPlan, rejectPlan } = useAppStore();\r\n\r\n  const handleConfirm = () => {\r\n    confirmPlan(plan.id);\r\n  };\r\n\r\n  const handleReject = () => {\r\n    rejectPlan(plan.id);\r\n  };\r\n\r\n  const getStepIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'tool':\r\n        return <Tool className=\"w-4 h-4\" />;\r\n      case 'code':\r\n        return <Code className=\"w-4 h-4\" />;\r\n      case 'file':\r\n        return <FileText className=\"w-4 h-4\" />;\r\n      default:\r\n        return <Clock className=\"w-4 h-4\" />;\r\n    }\r\n  };\r\n\r\n  const getRiskColor = (risk: 'low' | 'medium' | 'high') => {\r\n    switch (risk) {\r\n      case 'low':\r\n        return 'text-green-400 bg-green-900/50 border-green-700';\r\n      case 'medium':\r\n        return 'text-yellow-400 bg-yellow-900/50 border-yellow-700';\r\n      case 'high':\r\n        return 'text-red-400 bg-red-900/50 border-red-700';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-gray-800 border border-gray-600 rounded-lg p-4 space-y-4\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <AlertTriangle className=\"w-5 h-5 text-warning-400\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-100\">\r\n            Plan Confirmation Required\r\n          </h3>\r\n        </div>\r\n        <div className={`px-2 py-1 rounded text-xs border ${getRiskColor(plan.riskLevel)}`}>\r\n          {plan.riskLevel.toUpperCase()} RISK\r\n        </div>\r\n      </div>\r\n\r\n      {/* Description */}\r\n      <div className=\"text-sm text-gray-300\">\r\n        <p>{plan.description}</p>\r\n      </div>\r\n\r\n      {/* Steps */}\r\n      <div className=\"space-y-3\">\r\n        <h4 className=\"text-sm font-medium text-gray-400\">Planned Actions:</h4>\r\n        <div className=\"space-y-2\">\r\n          {plan.steps.map((step, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"flex items-start space-x-3 p-3 bg-gray-700 rounded-lg\"\r\n            >\r\n              <div className=\"flex-shrink-0 mt-0.5\">\r\n                <div className=\"w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-xs text-gray-300\">\r\n                  {index + 1}\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <div className=\"flex items-center space-x-2 mb-1\">\r\n                  {getStepIcon(step.type)}\r\n                  <span className=\"text-sm font-medium text-gray-200\">\r\n                    {step.action}\r\n                  </span>\r\n                  {step.type && (\r\n                    <span className=\"text-xs text-gray-500 bg-gray-600 px-2 py-0.5 rounded\">\r\n                      {step.type}\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                <p className=\"text-sm text-gray-400\">{step.description}</p>\r\n                {step.details && (\r\n                  <div className=\"mt-2 p-2 bg-gray-800 rounded text-xs text-gray-500 font-mono\">\r\n                    {step.details}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Warnings */}\r\n      {plan.warnings && plan.warnings.length > 0 && (\r\n        <div className=\"space-y-2\">\r\n          <h4 className=\"text-sm font-medium text-warning-400 flex items-center space-x-1\">\r\n            <AlertTriangle className=\"w-4 h-4\" />\r\n            <span>Important Warnings:</span>\r\n          </h4>\r\n          <ul className=\"space-y-1\">\r\n            {plan.warnings.map((warning, index) => (\r\n              <li key={index} className=\"text-sm text-warning-300 flex items-start space-x-2\">\r\n                <span className=\"text-warning-500 mt-1\">•</span>\r\n                <span>{warning}</span>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n\r\n      {/* Estimated Duration */}\r\n      {plan.estimatedDuration && (\r\n        <div className=\"text-sm text-gray-400\">\r\n          <Clock className=\"w-4 h-4 inline mr-1\" />\r\n          Estimated completion time: {plan.estimatedDuration}\r\n        </div>\r\n      )}\r\n\r\n      {/* Actions */}\r\n      <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-600\">\r\n        <button\r\n          onClick={handleReject}\r\n          className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-gray-200 rounded-lg transition-colors\"\r\n        >\r\n          <XCircle className=\"w-4 h-4\" />\r\n          <span>Reject</span>\r\n        </button>\r\n        <button\r\n          onClick={handleConfirm}\r\n          className=\"flex items-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors\"\r\n        >\r\n          <CheckCircle className=\"w-4 h-4\" />\r\n          <span>Approve & Execute</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PlanConfirmation;", "import React from 'react';\r\nimport { Bo<PERSON> } from 'lucide-react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\r\nimport { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';\r\n\r\ninterface StreamingMessageProps {\r\n  content: string;\r\n}\r\n\r\nconst StreamingMessage: React.FC<StreamingMessageProps> = ({ content }) => {\r\n  return (\r\n    <div className=\"flex gap-3 flex-row\">\r\n      {/* Avatar */}\r\n      <div className=\"flex-shrink-0 mr-3\">\r\n        <div className=\"w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-pink-600 text-white\">\r\n          <Bot className=\"w-4 h-4\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Message Content */}\r\n      <div className=\"flex-1 max-w-3xl items-start flex flex-col\">\r\n        <div className=\"px-4 py-3 rounded-2xl shadow-sm bg-gray-800 text-gray-100 mr-12 relative\">\r\n          {/* Streaming Content */}\r\n          <div className=\"prose prose-sm max-w-none\">\r\n            {content ? (\r\n              <ReactMarkdown\r\n                components={{\r\n                  code({ node, inline, className, children, ...props }) {\r\n                    const match = /language-(\\w+)/.exec(className || '');\r\n                    const language = match ? match[1] : '';\r\n\r\n                    if (!inline && language) {\r\n                      return (\r\n                        <SyntaxHighlighter\r\n                          style={oneDark}\r\n                          language={language}\r\n                          PreTag=\"div\"\r\n                          className=\"rounded-lg my-2\"\r\n                          {...props}\r\n                        >\r\n                          {String(children).replace(/\\n$/, '')}\r\n                        </SyntaxHighlighter>\r\n                      );\r\n                    }\r\n\r\n                    return (\r\n                      <code className=\"bg-gray-700 px-1 py-0.5 rounded text-sm\" {...props}>\r\n                        {children}\r\n                      </code>\r\n                    );\r\n                  },\r\n                  p: ({ children }) => (\r\n                    <div className=\"mb-2 last:mb-0 leading-relaxed text-sm\">\r\n                      {children}\r\n                    </div>\r\n                  ),\r\n                  ul: ({ children }) => (\r\n                    <ul className=\"list-disc list-inside space-y-1 text-sm mb-2\">\r\n                      {children}\r\n                    </ul>\r\n                  ),\r\n                  ol: ({ children }) => (\r\n                    <ol className=\"list-decimal list-inside space-y-1 text-sm mb-2\">\r\n                      {children}\r\n                    </ol>\r\n                  ),\r\n                  blockquote: ({ children }) => (\r\n                    <blockquote className=\"border-l-2 border-gray-600 pl-3 italic text-gray-300 text-sm\">\r\n                      {children}\r\n                    </blockquote>\r\n                  ),\r\n                }}\r\n              >\r\n                {content}\r\n              </ReactMarkdown>\r\n            ) : (\r\n              <div className=\"text-sm text-gray-400\">\r\n                <span>Thinking</span>\r\n                <span className=\"ml-1 animate-pulse\">...</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Typing Indicator */}\r\n          <div className=\"flex items-center mt-2 text-xs text-gray-500\">\r\n            <div className=\"flex space-x-1\">\r\n              <div className=\"w-1 h-1 bg-gray-400 rounded-full animate-bounce\"></div>\r\n              <div className=\"w-1 h-1 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n              <div className=\"w-1 h-1 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n            </div>\r\n            <span className=\"ml-2\">Assistant is typing...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StreamingMessage;", "import React, { useState } from 'react';\r\nimport { ToolResult } from '@shared/types';\r\nimport { \r\n  Wrench as Too<PERSON>, \r\n  CheckCircle, \r\n  XCircle, \r\n  Clock, \r\n  ChevronDown, \r\n  ChevronRight, \r\n  Copy, \r\n  Check,\r\n  FileText,\r\n  Code,\r\n  Terminal,\r\n  Database,\r\n  Globe\r\n} from 'lucide-react';\r\n\r\ninterface ToolResultDisplayProps {\r\n  result: ToolResult;\r\n}\r\n\r\nconst ToolResultDisplay: React.FC<ToolResultDisplayProps> = ({ result }) => {\r\n  const [isExpanded, setIsExpanded] = useState(true);\r\n  const [copied, setCopied] = useState(false);\r\n\r\n  const getToolIcon = (toolName: string) => {\r\n    switch (toolName.toLowerCase()) {\r\n      case 'file_read':\r\n      case 'file_write':\r\n      case 'file_search':\r\n        return <FileText className=\"w-4 h-4\" />;\r\n      case 'code_execute':\r\n      case 'code_analyze':\r\n        return <Code className=\"w-4 h-4\" />;\r\n      case 'terminal':\r\n      case 'shell':\r\n        return <Terminal className=\"w-4 h-4\" />;\r\n      case 'database':\r\n      case 'sql':\r\n        return <Database className=\"w-4 h-4\" />;\r\n      case 'web_search':\r\n      case 'web_scrape':\r\n        return <Globe className=\"w-4 h-4\" />;\r\n      default:\r\n        return <Tool className=\"w-4 h-4\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = () => {\r\n    switch (result.status) {\r\n      case 'success':\r\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\r\n      case 'error':\r\n        return <XCircle className=\"w-4 h-4 text-red-500\" />;\r\n      case 'running':\r\n        return <Clock className=\"w-4 h-4 text-blue-500 animate-pulse\" />;\r\n      default:\r\n        return <Clock className=\"w-4 h-4 text-gray-500\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusColor = () => {\r\n    switch (result.status) {\r\n      case 'success':\r\n        return 'border-green-600 bg-green-900/20';\r\n      case 'error':\r\n        return 'border-red-600 bg-red-900/20';\r\n      case 'running':\r\n        return 'border-blue-600 bg-blue-900/20';\r\n      default:\r\n        return 'border-gray-600 bg-gray-800';\r\n    }\r\n  };\r\n\r\n  const handleCopy = async () => {\r\n    try {\r\n      const textToCopy = typeof result.output === 'string' \r\n        ? result.output \r\n        : JSON.stringify(result.output, null, 2);\r\n      await navigator.clipboard.writeText(textToCopy);\r\n      setCopied(true);\r\n      setTimeout(() => setCopied(false), 2000);\r\n    } catch (error) {\r\n      console.error('Failed to copy result:', error);\r\n    }\r\n  };\r\n\r\n  const formatOutput = (output: any) => {\r\n    if (typeof output === 'string') {\r\n      return output;\r\n    }\r\n    return JSON.stringify(output, null, 2);\r\n  };\r\n\r\n  const formatDuration = (duration: number) => {\r\n    if (duration < 1000) {\r\n      return `${duration}ms`;\r\n    }\r\n    return `${(duration / 1000).toFixed(2)}s`;\r\n  };\r\n\r\n  return (\r\n    <div className={`border rounded-lg ${getStatusColor()}`}>\r\n      {/* Header */}\r\n      <div className=\"p-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <button\r\n              onClick={() => setIsExpanded(!isExpanded)}\r\n              className=\"flex-shrink-0 text-gray-400 hover:text-gray-300 transition-colors\"\r\n            >\r\n              {isExpanded ? (\r\n                <ChevronDown className=\"w-4 h-4\" />\r\n              ) : (\r\n                <ChevronRight className=\"w-4 h-4\" />\r\n              )}\r\n            </button>\r\n            \r\n            <div className=\"flex items-center space-x-2\">\r\n              {getToolIcon(result.toolName)}\r\n              <span className=\"text-sm font-medium text-gray-200\">\r\n                {result.toolName}\r\n              </span>\r\n            </div>\r\n            \r\n            {getStatusIcon()}\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-2 text-xs text-gray-500\">\r\n            {result.duration && (\r\n              <span>{formatDuration(result.duration)}</span>\r\n            )}\r\n            {result.timestamp && (\r\n              <span>\r\n                {new Date(result.timestamp).toLocaleTimeString()}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tool Input Summary */}\r\n        {result.input && (\r\n          <div className=\"mt-2 text-sm text-gray-400\">\r\n            <span className=\"font-medium\">Input: </span>\r\n            <span className=\"font-mono text-xs\">\r\n              {typeof result.input === 'string' \r\n                ? result.input.length > 100 \r\n                  ? `${result.input.substring(0, 100)}...`\r\n                  : result.input\r\n                : JSON.stringify(result.input).length > 100\r\n                  ? `${JSON.stringify(result.input).substring(0, 100)}...`\r\n                  : JSON.stringify(result.input)\r\n              }\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Expandable Content */}\r\n      {isExpanded && (\r\n        <div className=\"border-t border-gray-600\">\r\n          {/* Error Message */}\r\n          {result.status === 'error' && result.error && (\r\n            <div className=\"p-3 bg-red-900/30\">\r\n              <div className=\"flex items-start space-x-2\">\r\n                <XCircle className=\"w-4 h-4 text-red-400 mt-0.5 flex-shrink-0\" />\r\n                <div className=\"flex-1\">\r\n                  <div className=\"text-sm font-medium text-red-300 mb-1\">\r\n                    Execution Error\r\n                  </div>\r\n                  <div className=\"text-sm text-red-200 font-mono whitespace-pre-wrap\">\r\n                    {result.error}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Output */}\r\n          {result.output && (\r\n            <div className=\"p-3\">\r\n              <div className=\"flex items-center justify-between mb-2\">\r\n                <span className=\"text-sm font-medium text-gray-300\">Output:</span>\r\n                <button\r\n                  onClick={handleCopy}\r\n                  className=\"flex items-center space-x-1 px-2 py-1 text-xs text-gray-400 hover:text-gray-300 hover:bg-gray-700 rounded transition-colors\"\r\n                  title=\"Copy output\"\r\n                >\r\n                  {copied ? (\r\n                    <>\r\n                      <Check className=\"w-3 h-3\" />\r\n                      <span>Copied</span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Copy className=\"w-3 h-3\" />\r\n                      <span>Copy</span>\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n              \r\n              <div className=\"bg-gray-900 border border-gray-600 rounded p-3 max-h-80 overflow-y-auto\">\r\n                <pre className=\"text-sm text-gray-300 whitespace-pre-wrap font-mono\">\r\n                  {formatOutput(result.output)}\r\n                </pre>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Metadata */}\r\n          {(result.metadata && Object.keys(result.metadata).length > 0) && (\r\n            <div className=\"p-3 border-t border-gray-600 bg-gray-800/50\">\r\n              <div className=\"text-sm font-medium text-gray-300 mb-2\">Metadata:</div>\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                {Object.entries(result.metadata).map(([key, value]) => (\r\n                  <div key={key} className=\"text-xs\">\r\n                    <span className=\"text-gray-500\">{key}:</span>\r\n                    <span className=\"text-gray-400 ml-1 font-mono\">\r\n                      {typeof value === 'string' ? value : JSON.stringify(value)}\r\n                    </span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ToolResultDisplay;", "import React, { useEffect } from 'react';\nimport { AlertTriangle, CheckCircle, XCircle, Info, X } from 'lucide-react';\nimport { useFocusTrap, useOutsideClick } from '@renderer/hooks/useUtils';\n\ninterface ConfirmationModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  onCancel?: () => void;\n  title: string;\n  message: string;\n  type?: 'info' | 'warning' | 'error' | 'success';\n  confirmText?: string;\n  cancelText?: string;\n  details?: string;\n  showDetails?: boolean;\n  dangerous?: boolean;\n  loading?: boolean;\n}\n\nconst ConfirmationModal: React.FC<ConfirmationModalProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  onCancel,\n  title,\n  message,\n  type = 'info',\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  details,\n  showDetails = false,\n  dangerous = false,\n  loading = false,\n}) => {\n  const containerRef = useFocusTrap(isOpen);\n  const modalRef = useOutsideClick(() => {\n    if (!loading) onClose();\n  });\n\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  if (!isOpen) return null;\n\n  const getIcon = () => {\n    switch (type) {\n      case 'warning':\n        return <AlertTriangle className=\"w-6 h-6 text-yellow-500\" />;\n      case 'error':\n        return <XCircle className=\"w-6 h-6 text-red-500\" />;\n      case 'success':\n        return <CheckCircle className=\"w-6 h-6 text-green-500\" />;\n      default:\n        return <Info className=\"w-6 h-6 text-blue-500\" />;\n    }\n  };\n\n  const getHeaderColor = () => {\n    switch (type) {\n      case 'warning':\n        return 'border-yellow-500';\n      case 'error':\n        return 'border-red-500';\n      case 'success':\n        return 'border-green-500';\n      default:\n        return 'border-blue-500';\n    }\n  };\n\n  const getConfirmButtonStyle = () => {\n    if (dangerous) {\n      return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';\n    }\n    switch (type) {\n      case 'warning':\n        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';\n      case 'error':\n        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';\n      case 'success':\n        return 'bg-green-600 hover:bg-green-700 focus:ring-green-500';\n      default:\n        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';\n    }\n  };\n\n  const handleConfirm = () => {\n    if (!loading) {\n      onConfirm();\n    }\n  };\n\n  const handleCancel = () => {\n    if (!loading) {\n      if (onCancel) {\n        onCancel();\n      } else {\n        onClose();\n      }\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div\n        ref={modalRef}\n        className=\"bg-gray-800 rounded-lg shadow-2xl w-full max-w-md mx-4\"\n      >\n        <div ref={containerRef}>\n          {/* Header */}\n          <div className={`flex items-center justify-between p-6 border-b border-gray-700 border-t-4 ${getHeaderColor()}`}>\n            <div className=\"flex items-center space-x-3\">\n              {getIcon()}\n              <h3 className=\"text-lg font-semibold text-gray-100\">{title}</h3>\n            </div>\n            {!loading && (\n              <button\n                onClick={onClose}\n                className=\"text-gray-400 hover:text-gray-200 transition-colors\"\n                disabled={loading}\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6\">\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">{message}</p>\n            \n            {details && showDetails && (\n              <div className=\"bg-gray-900 rounded-lg p-4 mb-4\">\n                <h4 className=\"text-sm font-medium text-gray-400 mb-2\">Details:</h4>\n                <pre className=\"text-sm text-gray-300 whitespace-pre-wrap overflow-x-auto\">\n                  {details}\n                </pre>\n              </div>\n            )}\n\n            {dangerous && (\n              <div className=\"bg-red-900 bg-opacity-30 border border-red-500 rounded-lg p-3 mb-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <AlertTriangle className=\"w-4 h-4 text-red-400 flex-shrink-0\" />\n                  <p className=\"text-sm text-red-300\">\n                    This action is potentially dangerous and cannot be undone.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center justify-end space-x-3 p-6 border-t border-gray-700\">\n            <button\n              onClick={handleCancel}\n              disabled={loading}\n              className=\"px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {cancelText}\n            </button>\n            <button\n              onClick={handleConfirm}\n              disabled={loading}\n              className={`px-4 py-2 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${getConfirmButtonStyle()}`}\n            >\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>Processing...</span>\n                </div>\n              ) : (\n                confirmText\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmationModal;\n", "import React, { useState, useEffect } from 'react';\r\nimport { useAppStore } from '@renderer/store/appStore';\r\nimport { Settings, X, Save, RotateCcw, Eye, EyeOff, AlertTriangle, CheckCircle } from 'lucide-react';\r\nimport { useSettings } from '@renderer/hooks/useElectronAPI';\r\nimport { DEFAULT_MODELS } from '@shared/constants';\r\nimport type { AppSettings } from '@shared/types';\r\n\r\nconst SettingsPanel: React.FC = () => {\r\n  const { settings, setSettingsOpen } = useAppStore();\r\n  const { updateSettings, resetSettings, isLoading } = useSettings();\r\n  const [localSettings, setLocalSettings] = useState<AppSettings | null>(null);\r\n  const [showApiKey, setShowApiKey] = useState(false);\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');\r\n\r\n  useEffect(() => {\r\n    if (settings) {\r\n      setLocalSettings({ ...settings });\r\n    }\r\n  }, [settings]);\r\n\r\n  useEffect(() => {\r\n    if (localSettings && settings) {\r\n      const changed = JSON.stringify(localSettings) !== JSON.stringify(settings);\r\n      setHasChanges(changed);\r\n    }\r\n  }, [localSettings, settings]);\r\n\r\n  const handleSave = async () => {\r\n    if (!localSettings || !hasChanges) return;\r\n\r\n    setSaveStatus('saving');\r\n    try {\r\n      await updateSettings(localSettings);\r\n      setSaveStatus('saved');\r\n      setTimeout(() => setSaveStatus('idle'), 2000);\r\n    } catch (error) {\r\n      setSaveStatus('error');\r\n      setTimeout(() => setSaveStatus('idle'), 3000);\r\n    }\r\n  };\r\n\r\n  const handleReset = async () => {\r\n    if (window.confirm('Are you sure you want to reset all settings to defaults?')) {\r\n      setSaveStatus('saving');\r\n      try {\r\n        await resetSettings();\r\n        setSaveStatus('saved');\r\n        setTimeout(() => setSaveStatus('idle'), 2000);\r\n      } catch (error) {\r\n        setSaveStatus('error');\r\n        setTimeout(() => setSaveStatus('idle'), 3000);\r\n      }\r\n    }\r\n  };\r\n\r\n  const updateLocalSetting = (path: string, value: any) => {\r\n    if (!localSettings) return;\r\n\r\n    const keys = path.split('.');\r\n    const newSettings = { ...localSettings };\r\n    let current: any = newSettings;\r\n\r\n    for (let i = 0; i < keys.length - 1; i++) {\r\n      current[keys[i]] = { ...current[keys[i]] };\r\n      current = current[keys[i]];\r\n    }\r\n\r\n    current[keys[keys.length - 1]] = value;\r\n    setLocalSettings(newSettings);\r\n  };\r\n\r\n  if (!localSettings) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex-1 flex flex-col bg-gray-900\">\r\n      {/* Header */}\r\n      <div className=\"h-16 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-6\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <Settings className=\"w-5 h-5 text-gray-400\" />\r\n          <h1 className=\"text-lg font-semibold text-gray-100\">Settings</h1>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3\">\r\n          {saveStatus === 'saved' && (\r\n            <div className=\"flex items-center space-x-2 text-green-400\">\r\n              <CheckCircle className=\"w-4 h-4\" />\r\n              <span className=\"text-sm\">Saved</span>\r\n            </div>\r\n          )}\r\n          {saveStatus === 'error' && (\r\n            <div className=\"flex items-center space-x-2 text-red-400\">\r\n              <AlertTriangle className=\"w-4 h-4\" />\r\n              <span className=\"text-sm\">Error saving</span>\r\n            </div>\r\n          )}\r\n          {hasChanges && (\r\n            <>\r\n              <button\r\n                onClick={handleReset}\r\n                disabled={isLoading || saveStatus === 'saving'}\r\n                className=\"px-3 py-1 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50\"\r\n              >\r\n                <RotateCcw className=\"w-4 h-4 mr-1 inline\" />\r\n                Reset\r\n              </button>\r\n              <button\r\n                onClick={handleSave}\r\n                disabled={isLoading || saveStatus === 'saving'}\r\n                className=\"px-3 py-1 text-sm bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors disabled:opacity-50\"\r\n              >\r\n                {saveStatus === 'saving' ? (\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <div className=\"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"></div>\r\n                    <span>Saving...</span>\r\n                  </div>\r\n                ) : (\r\n                  <>\r\n                    <Save className=\"w-4 h-4 mr-1 inline\" />\r\n                    Save\r\n                  </>\r\n                )}\r\n              </button>\r\n            </>\r\n          )}\r\n          <button\r\n            onClick={() => setSettingsOpen(false)}\r\n            className=\"p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700 rounded-lg transition-colors\"\r\n          >\r\n            <X className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"flex-1 overflow-y-auto p-6\">\r\n        <div className=\"max-w-4xl mx-auto space-y-8\">\r\n          {/* LLM Settings */}\r\n          <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-6\">\r\n            <h2 className=\"text-xl font-semibold text-gray-100 mb-4\">Language Model</h2>\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">Provider</label>\r\n                <select\r\n                  value={localSettings.llm.provider}\r\n                  onChange={(e) => updateLocalSetting('llm.provider', e.target.value)}\r\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                >\r\n                  <option value=\"openai\">OpenAI</option>\r\n                  <option value=\"anthropic\">Anthropic</option>\r\n                  <option value=\"deepseek\">DeepSeek</option>\r\n                </select>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">Model</label>\r\n                <select\r\n                  value={localSettings.llm.model}\r\n                  onChange={(e) => updateLocalSetting('llm.model', e.target.value)}\r\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                >\r\n                  {Object.entries(DEFAULT_MODELS[localSettings.llm.provider as keyof typeof DEFAULT_MODELS] || {}).map(([model, label]) => (\r\n                    <option key={model} value={model}>\r\n                      {label}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">API Key</label>\r\n                <div className=\"relative\">\r\n                  <input\r\n                    type={showApiKey ? 'text' : 'password'}\r\n                    value={localSettings.llm.apiKey}\r\n                    onChange={(e) => updateLocalSetting('llm.apiKey', e.target.value)}\r\n                    className=\"w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                    placeholder=\"Enter your API key\"\r\n                  />\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setShowApiKey(!showApiKey)}\r\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200\"\r\n                  >\r\n                    {showApiKey ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">Max Tokens</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={localSettings.llm.maxTokens}\r\n                    onChange={(e) => updateLocalSetting('llm.maxTokens', parseInt(e.target.value))}\r\n                    min=\"1\"\r\n                    max=\"32000\"\r\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">Temperature</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={localSettings.llm.temperature}\r\n                    onChange={(e) => updateLocalSetting('llm.temperature', parseFloat(e.target.value))}\r\n                    min=\"0\"\r\n                    max=\"2\"\r\n                    step=\"0.1\"\r\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* UI Settings */}\r\n          <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-6\">\r\n            <h2 className=\"text-xl font-semibold text-gray-100 mb-4\">Interface</h2>\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">Theme</label>\r\n                <select\r\n                  value={localSettings.ui.theme}\r\n                  onChange={(e) => updateLocalSetting('ui.theme', e.target.value)}\r\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                >\r\n                  <option value=\"system\">System</option>\r\n                  <option value=\"light\">Light</option>\r\n                  <option value=\"dark\">Dark</option>\r\n                </select>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">Font Size</label>\r\n                <select\r\n                  value={localSettings.ui.fontSize}\r\n                  onChange={(e) => updateLocalSetting('ui.fontSize', e.target.value)}\r\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                >\r\n                  <option value=\"small\">Small</option>\r\n                  <option value=\"medium\">Medium</option>\r\n                  <option value=\"large\">Large</option>\r\n                </select>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  id=\"compactMode\"\r\n                  checked={localSettings.ui.compactMode}\r\n                  onChange={(e) => updateLocalSetting('ui.compactMode', e.target.checked)}\r\n                  className=\"w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500\"\r\n                />\r\n                <label htmlFor=\"compactMode\" className=\"ml-2 text-sm text-gray-300\">\r\n                  Compact mode\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Agent Settings */}\r\n          <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-6\">\r\n            <h2 className=\"text-xl font-semibold text-gray-100 mb-4\">Agent Behavior</h2>\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">Execution Mode</label>\r\n                <select\r\n                  value={localSettings.agent.defaultExecutionMode}\r\n                  onChange={(e) => updateLocalSetting('agent.defaultExecutionMode', e.target.value)}\r\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                >\r\n                  <option value=\"confirm\">Ask for confirmation</option>\r\n                  <option value=\"yolo\">Execute automatically (YOLO mode)</option>\r\n                </select>\r\n                {localSettings.agent.defaultExecutionMode === 'yolo' && (\r\n                  <div className=\"mt-2 p-3 bg-red-900 bg-opacity-30 border border-red-500 rounded-lg\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <AlertTriangle className=\"w-4 h-4 text-red-400 flex-shrink-0\" />\r\n                      <p className=\"text-sm text-red-300\">\r\n                        YOLO mode allows the agent to execute commands without confirmation. Use with caution.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  id=\"autoSave\"\r\n                  checked={localSettings.agent.autoSaveConversations}\r\n                  onChange={(e) => updateLocalSetting('agent.autoSaveConversations', e.target.checked)}\r\n                  className=\"w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500\"\r\n                />\r\n                <label htmlFor=\"autoSave\" className=\"ml-2 text-sm text-gray-300\">\r\n                  Auto-save conversations\r\n                </label>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">Max Context Length</label>\r\n                <input\r\n                  type=\"number\"\r\n                  value={localSettings.agent.maxContextLength}\r\n                  onChange={(e) => updateLocalSetting('agent.maxContextLength', parseInt(e.target.value))}\r\n                  min=\"1000\"\r\n                  max=\"128000\"\r\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-100 focus:border-primary-500 focus:outline-none\"\r\n                />\r\n                <p className=\"text-xs text-gray-500 mt-1\">\r\n                  Maximum number of tokens to keep in conversation context\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsPanel;", "import React, { useEffect, useState } from 'react';\r\nimport { useAppStore } from '@renderer/store/appStore';\r\nimport { AlertCircle, X } from 'lucide-react';\r\n\r\ninterface ErrorToastProps {\r\n  message: string;\r\n  duration?: number;\r\n}\r\n\r\nconst ErrorToast: React.FC<ErrorToastProps> = ({ \r\n  message, \r\n  duration = 5000 \r\n}) => {\r\n  const { clearError } = useAppStore();\r\n  const [isVisible, setIsVisible] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      handleClose();\r\n    }, duration);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [duration]);\r\n\r\n  const handleClose = () => {\r\n    setIsVisible(false);\r\n    setTimeout(() => {\r\n      clearError();\r\n    }, 300); // Wait for animation to complete\r\n  };\r\n\r\n  if (!isVisible) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"fixed top-4 right-4 z-50 slide-down\">\r\n      <div className=\"bg-danger-600 text-white rounded-lg shadow-lg p-4 pr-12 max-w-md relative\">\r\n        <div className=\"flex items-start space-x-3\">\r\n          <AlertCircle className=\"w-5 h-5 flex-shrink-0 mt-0.5\" />\r\n          <div>\r\n            <h4 className=\"font-medium text-sm\">Error</h4>\r\n            <p className=\"text-sm text-danger-100 mt-1\">\r\n              {message}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        \r\n        <button\r\n          onClick={handleClose}\r\n          className=\"absolute top-2 right-2 p-1 rounded-full hover:bg-danger-700 transition-colors\"\r\n        >\r\n          <X className=\"w-4 h-4\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ErrorToast;", "import React from 'react';\r\n\r\ninterface LoadingScreenProps {\r\n  message?: string;\r\n}\r\n\r\nconst LoadingScreen: React.FC<LoadingScreenProps> = ({ \r\n  message = \"Initializing AI Assistant...\" \r\n}) => {\r\n  return (\r\n    <div className=\"h-screen w-screen bg-gray-900 flex items-center justify-center\">\r\n      <div className=\"text-center\">\r\n        {/* App Logo/Icon */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto shadow-2xl\">\r\n            <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Loading Spinner */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"w-12 h-12 mx-auto\">\r\n            <div className=\"loading-spinner w-full h-full\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Loading Message */}\r\n        <div className=\"space-y-2\">\r\n          <h2 className=\"text-xl font-semibold text-gray-100\">\r\n            AI Assistant Desktop\r\n          </h2>\r\n          <p className=\"text-gray-400 text-sm\">\r\n            {message}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Loading Progress Dots */}\r\n        <div className=\"flex justify-center space-x-2 mt-6\">\r\n          <div className=\"w-2 h-2 bg-primary-500 rounded-full animate-pulse\"></div>\r\n          <div className=\"w-2 h-2 bg-primary-500 rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\r\n          <div className=\"w-2 h-2 bg-primary-500 rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingScreen;", "import React, { useState } from 'react';\r\nimport { useAppStore, useExecutionMode } from '@renderer/store/appStore';\r\nimport { useConversations } from '@renderer/hooks/useElectronAPI';\r\nimport { \r\n  Plus, \r\n  MessageSquare, \r\n  Settings, \r\n  FileText, \r\n  Trash2, \r\n  MoreHorizontal,\r\n  Zap,\r\n  Shield\r\n} from 'lucide-react';\r\n\r\nconst Sidebar: React.FC = () => {\r\n  const {\r\n    conversations,\r\n    currentConversationId,\r\n    setCurrentConversation,\r\n    startNewConversation,\r\n    setCurrentView,\r\n    currentView,\r\n  } = useAppStore();\r\n  \r\n  const { deleteConversation } = useConversations();\r\n  const executionMode = useExecutionMode();\r\n  const [activeMenu, setActiveMenu] = useState<string | null>(null);\r\n\r\n  const handleNewChat = () => {\r\n    startNewConversation('New Chat');\r\n  };\r\n\r\n  const handleDeleteConversation = async (id: string, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    if (confirm('Are you sure you want to delete this conversation?')) {\r\n      await deleteConversation(id);\r\n    }\r\n    setActiveMenu(null);\r\n  };\r\n\r\n  const formatTime = (date: Date) => {\r\n    const now = new Date();\r\n    const diff = now.getTime() - date.getTime();\r\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\r\n    \r\n    if (days === 0) {\r\n      return 'Today';\r\n    } else if (days === 1) {\r\n      return 'Yesterday';\r\n    } else if (days < 7) {\r\n      return `${days} days ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col bg-gray-800\">\r\n      {/* Header */}\r\n      <div className=\"p-4 border-b border-gray-700\">\r\n        <button\r\n          onClick={handleNewChat}\r\n          className=\"w-full btn btn-primary btn-md flex items-center justify-center space-x-2\"\r\n        >\r\n          <Plus className=\"w-4 h-4\" />\r\n          <span>New Chat</span>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"px-4 py-3 border-b border-gray-700\">\r\n        <nav className=\"space-y-1\">\r\n          <button\r\n            onClick={() => setCurrentView('chat')}\r\n            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n              currentView === 'chat' \r\n                ? 'bg-primary-600 text-white' \r\n                : 'text-gray-300 hover:bg-gray-700 hover:text-white'\r\n            }`}\r\n          >\r\n            <MessageSquare className=\"w-4 h-4\" />\r\n            <span>Chat</span>\r\n          </button>\r\n          \r\n          <button\r\n            onClick={() => setCurrentView('settings')}\r\n            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n              currentView === 'settings' \r\n                ? 'bg-primary-600 text-white' \r\n                : 'text-gray-300 hover:bg-gray-700 hover:text-white'\r\n            }`}\r\n          >\r\n            <Settings className=\"w-4 h-4\" />\r\n            <span>Settings</span>\r\n          </button>\r\n          \r\n          <button\r\n            onClick={() => setCurrentView('logs')}\r\n            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n              currentView === 'logs' \r\n                ? 'bg-primary-600 text-white' \r\n                : 'text-gray-300 hover:bg-gray-700 hover:text-white'\r\n            }`}\r\n          >\r\n            <FileText className=\"w-4 h-4\" />\r\n            <span>Logs</span>\r\n          </button>\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Execution Mode Indicator */}\r\n      <div className=\"px-4 py-3 border-b border-gray-700\">\r\n        <div className={`flex items-center space-x-2 px-3 py-2 rounded-md ${\r\n          executionMode === 'yolo' \r\n            ? 'bg-danger-900/20 border border-danger-600' \r\n            : 'bg-success-900/20 border border-success-600'\r\n        }`}>\r\n          {executionMode === 'yolo' ? (\r\n            <>\r\n              <Zap className=\"w-4 h-4 text-danger-400\" />\r\n              <span className=\"text-sm text-danger-300 font-medium\">YOLO Mode</span>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Shield className=\"w-4 h-4 text-success-400\" />\r\n              <span className=\"text-sm text-success-300 font-medium\">Confirm Mode</span>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Conversations List */}\r\n      <div className=\"flex-1 overflow-y-auto\">\r\n        <div className=\"px-4 py-3\">\r\n          <h3 className=\"text-xs font-medium text-gray-400 uppercase tracking-wide mb-3\">\r\n            Recent Conversations\r\n          </h3>\r\n          \r\n          {conversations.length === 0 ? (\r\n            <div className=\"text-center py-8\">\r\n              <MessageSquare className=\"w-8 h-8 text-gray-600 mx-auto mb-3\" />\r\n              <p className=\"text-sm text-gray-500\">No conversations yet</p>\r\n              <p className=\"text-xs text-gray-600 mt-1\">Start a new chat to begin</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-1\">\r\n              {conversations.map((conversation) => (\r\n                <div\r\n                  key={conversation.id}\r\n                  className={`group relative flex items-center space-x-3 p-3 rounded-md cursor-pointer transition-colors ${\r\n                    currentConversationId === conversation.id\r\n                      ? 'bg-primary-600 text-white'\r\n                      : 'hover:bg-gray-700 text-gray-300'\r\n                  }`}\r\n                  onClick={() => setCurrentConversation(conversation.id)}\r\n                >\r\n                  <MessageSquare className=\"w-4 h-4 flex-shrink-0\" />\r\n                  \r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <p className=\"text-sm font-medium truncate\">\r\n                      {conversation.title}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-500 mt-0.5\">\r\n                      {formatTime(conversation.updatedAt)} • {conversation.messages.length} messages\r\n                    </p>\r\n                  </div>\r\n                  \r\n                  <div className=\"relative\">\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        setActiveMenu(activeMenu === conversation.id ? null : conversation.id);\r\n                      }}\r\n                      className=\"p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-600 transition-all\"\r\n                    >\r\n                      <MoreHorizontal className=\"w-4 h-4\" />\r\n                    </button>\r\n                    \r\n                    {activeMenu === conversation.id && (\r\n                      <div className=\"absolute right-0 top-8 z-50 context-menu\">\r\n                        <button\r\n                          onClick={(e) => handleDeleteConversation(conversation.id, e)}\r\n                          className=\"context-menu-item text-danger-300 hover:bg-danger-600 hover:text-white\"\r\n                        >\r\n                          <Trash2 className=\"w-4 h-4 mr-2\" />\r\n                          Delete\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      <div className=\"p-4 border-t border-gray-700\">\r\n        <div className=\"text-xs text-gray-500 text-center\">\r\n          <p>AI Assistant Desktop</p>\r\n          <p className=\"mt-1\">v1.0.0</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\nimport { useAppStore } from '@renderer/store/appStore';\r\nimport { useWindowControls, useIsYoloMode } from '@renderer/hooks/useElectronAPI';\r\nimport { Minimize2, Maximize2, X, Menu, Settings, AlertTriangle } from 'lucide-react';\r\n\r\nconst TitleBar: React.FC = () => {\r\n  const {\r\n    sidebarOpen,\r\n    setSidebarOpen,\r\n    setSettingsOpen,\r\n    currentView,\r\n    isProcessing\r\n  } = useAppStore();\r\n\r\n  const { minimize, maximize, close } = useWindowControls();\r\n  const isYoloMode = useIsYoloMode();\r\n\r\n  return (\r\n    <div className=\"h-12 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-4 drag-region select-none\">\r\n      {/* Left Section - App Controls */}\r\n      <div className=\"flex items-center space-x-3 no-drag\">\r\n        <button\r\n          onClick={() => setSidebarOpen(!sidebarOpen)}\r\n          className=\"p-2 rounded-md hover:bg-gray-700 transition-colors\"\r\n          title=\"Toggle Sidebar\"\r\n        >\r\n          <Menu className=\"w-4 h-4 text-gray-300\" />\r\n        </button>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded flex items-center justify-center\">\r\n            <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n          </div>\r\n          <span className=\"text-sm font-medium text-gray-200\">AI Assistant</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Center Section - Current View & Status */}\r\n      <div className=\"flex-1 flex items-center justify-center space-x-4 drag-region\">\r\n        {isYoloMode && (\r\n          <div className=\"flex items-center space-x-2 px-2 py-1 bg-red-900 bg-opacity-40 border border-red-500 rounded text-red-300 text-xs font-medium\">\r\n            <AlertTriangle className=\"w-3 h-3\" />\r\n            <span>YOLO</span>\r\n          </div>\r\n        )}\r\n\r\n        {isProcessing && (\r\n          <div className=\"flex items-center space-x-2 text-sm text-primary-400\">\r\n            <div className=\"w-4 h-4 loading-spinner\"></div>\r\n            <span>Processing...</span>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"text-xs text-gray-500 uppercase tracking-wide\">\r\n          {currentView === 'chat' && 'Chat'}\r\n          {currentView === 'settings' && 'Settings'}\r\n          {currentView === 'logs' && 'System Logs'}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Section - Window Controls */}\r\n      <div className=\"flex items-center space-x-1 no-drag\">\r\n        <button\r\n          onClick={() => setSettingsOpen(true)}\r\n          className=\"p-2 rounded-md hover:bg-gray-700 transition-colors\"\r\n          title=\"Settings\"\r\n        >\r\n          <Settings className=\"w-4 h-4 text-gray-300\" />\r\n        </button>\r\n        \r\n        <div className=\"w-px h-6 bg-gray-600 mx-2\" />\r\n        \r\n        <button\r\n          onClick={minimize}\r\n          className=\"p-2 rounded-md hover:bg-gray-700 transition-colors\"\r\n          title=\"Minimize\"\r\n        >\r\n          <Minimize2 className=\"w-4 h-4 text-gray-300\" />\r\n        </button>\r\n        \r\n        <button\r\n          onClick={maximize}\r\n          className=\"p-2 rounded-md hover:bg-gray-700 transition-colors\"\r\n          title=\"Maximize\"\r\n        >\r\n          <Maximize2 className=\"w-4 h-4 text-gray-300\" />\r\n        </button>\r\n        \r\n        <button\r\n          onClick={close}\r\n          className=\"p-2 rounded-md hover:bg-danger-600 hover:text-white transition-colors\"\r\n          title=\"Close\"\r\n        >\r\n          <X className=\"w-4 h-4 text-gray-300\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TitleBar;", "import React, { useEffect } from 'react';\nimport { CheckCircle, XCircle, AlertTriangle, Info, X } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface Toast {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  message: string;\n  duration?: number;\n}\n\ninterface ToastContainerProps {\n  toasts: Toast[];\n  onRemove: (id: string) => void;\n}\n\nconst ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onRemove }) => {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      <AnimatePresence>\n        {toasts.map((toast) => (\n          <ToastItem key={toast.id} toast={toast} onRemove={onRemove} />\n        ))}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nconst ToastItem: React.FC<{ toast: Toast; onRemove: (id: string) => void }> = ({\n  toast,\n  onRemove,\n}) => {\n  useEffect(() => {\n    if (toast.duration && toast.duration > 0) {\n      const timer = setTimeout(() => {\n        onRemove(toast.id);\n      }, toast.duration);\n\n      return () => clearTimeout(timer);\n    }\n  }, [toast.id, toast.duration, onRemove]);\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"w-5 h-5 text-green-400\" />;\n      case 'error':\n        return <XCircle className=\"w-5 h-5 text-red-400\" />;\n      case 'warning':\n        return <AlertTriangle className=\"w-5 h-5 text-yellow-400\" />;\n      default:\n        return <Info className=\"w-5 h-5 text-blue-400\" />;\n    }\n  };\n\n  const getBackgroundColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-900 border-green-500';\n      case 'error':\n        return 'bg-red-900 border-red-500';\n      case 'warning':\n        return 'bg-yellow-900 border-yellow-500';\n      default:\n        return 'bg-blue-900 border-blue-500';\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: 300, scale: 0.3 }}\n      animate={{ opacity: 1, x: 0, scale: 1 }}\n      exit={{ opacity: 0, x: 300, scale: 0.5, transition: { duration: 0.2 } }}\n      className={`${getBackgroundColor()} bg-opacity-90 backdrop-blur-sm border-l-4 rounded-lg shadow-lg p-4 min-w-80 max-w-md`}\n    >\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"flex-shrink-0\">{getIcon()}</div>\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-sm text-gray-100 leading-relaxed\">{toast.message}</p>\n        </div>\n        <button\n          onClick={() => onRemove(toast.id)}\n          className=\"flex-shrink-0 text-gray-400 hover:text-gray-200 transition-colors\"\n        >\n          <X className=\"w-4 h-4\" />\n        </button>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ToastContainer;\n", "import React, { useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Zap, Shield, Settings } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAppStore } from '@renderer/store/appStore';\nimport { useSettings } from '@renderer/hooks/useElectronAPI';\nimport ConfirmationModal from '@renderer/components/modals/ConfirmationModal';\n\nconst YoloModeIndicator: React.FC = () => {\n  const { settings } = useAppStore();\n  const { updateSettings } = useSettings();\n  const [showModeSwitch, setShowModeSwitch] = useState(false);\n  const [showWarning, setShowWarning] = useState(false);\n\n  const isYoloMode = settings?.agent?.defaultExecutionMode === 'yolo';\n\n  const toggleExecutionMode = async () => {\n    if (!settings) return;\n\n    const newMode = isYoloMode ? 'confirm' : 'yolo';\n    \n    if (newMode === 'yolo') {\n      setShowWarning(true);\n    } else {\n      await updateSettings({\n        ...settings,\n        agent: {\n          ...settings.agent,\n          defaultExecutionMode: 'confirm',\n        },\n      });\n    }\n  };\n\n  const confirmYoloMode = async () => {\n    if (!settings) return;\n\n    await updateSettings({\n      ...settings,\n      agent: {\n        ...settings.agent,\n        defaultExecutionMode: 'yolo',\n      },\n    });\n    setShowWarning(false);\n  };\n\n  if (!isYoloMode) {\n    return (\n      <>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={toggleExecutionMode}\n            className=\"flex items-center space-x-2 px-3 py-1 bg-green-900 bg-opacity-30 border border-green-500 rounded-lg text-green-300 hover:bg-green-800 hover:bg-opacity-40 transition-colors\"\n            title=\"Switch to YOLO mode\"\n          >\n            <Shield className=\"w-4 h-4\" />\n            <span className=\"text-sm font-medium\">Safe Mode</span>\n          </button>\n        </div>\n\n        <ConfirmationModal\n          isOpen={showWarning}\n          onClose={() => setShowWarning(false)}\n          onConfirm={confirmYoloMode}\n          onCancel={() => setShowWarning(false)}\n          title=\"Enable YOLO Mode\"\n          message=\"YOLO mode allows the AI agent to execute commands automatically without asking for confirmation. This can be dangerous and may result in unintended changes to your system.\"\n          type=\"warning\"\n          dangerous={true}\n          confirmText=\"Enable YOLO Mode\"\n          cancelText=\"Keep Safe Mode\"\n          details={`YOLO mode will:\n• Execute shell commands automatically\n• Modify files without confirmation\n• Install software and dependencies\n• Make system configuration changes\n• Perform potentially destructive operations\n\nOnly enable this if you fully trust the AI agent and understand the risks.`}\n          showDetails={true}\n        />\n      </>\n    );\n  }\n\n  return (\n    <>\n      {/* YOLO Mode Active Indicator */}\n      <motion.div\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        className=\"flex items-center space-x-2\"\n      >\n        <motion.button\n          onClick={toggleExecutionMode}\n          className=\"flex items-center space-x-2 px-3 py-1 bg-red-900 bg-opacity-40 border border-red-500 rounded-lg text-red-300 hover:bg-red-800 hover:bg-opacity-50 transition-colors\"\n          title=\"Switch to Safe mode\"\n          animate={{\n            boxShadow: [\n              '0 0 0 0 rgba(239, 68, 68, 0.4)',\n              '0 0 0 4px rgba(239, 68, 68, 0.1)',\n              '0 0 0 0 rgba(239, 68, 68, 0.4)',\n            ],\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n          }}\n        >\n          <motion.div\n            animate={{ rotate: [0, 10, -10, 0] }}\n            transition={{ duration: 0.5, repeat: Infinity, repeatDelay: 3 }}\n          >\n            <AlertTriangle className=\"w-4 h-4\" />\n          </motion.div>\n          <span className=\"text-sm font-bold\">YOLO MODE</span>\n          <Zap className=\"w-4 h-4\" />\n        </motion.button>\n      </motion.div>\n\n      {/* Global YOLO Mode Styles */}\n      <style>{`\n        .yolo-mode {\n          position: relative;\n        }\n        \n        .yolo-mode::before {\n          content: '';\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          border: 3px solid #ef4444;\n          pointer-events: none;\n          z-index: 9999;\n          animation: yolo-pulse 3s infinite;\n        }\n        \n        @keyframes yolo-pulse {\n          0%, 100% {\n            border-color: rgba(239, 68, 68, 0.3);\n            box-shadow: inset 0 0 0 0 rgba(239, 68, 68, 0.1);\n          }\n          50% {\n            border-color: rgba(239, 68, 68, 0.8);\n            box-shadow: inset 0 0 0 3px rgba(239, 68, 68, 0.2);\n          }\n        }\n        \n        .yolo-mode .title-bar {\n          background: linear-gradient(90deg, #1f2937 0%, #7f1d1d 100%);\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default YoloModeIndicator;\n", "import { useCallback, useEffect, useState } from 'react';\r\nimport type { APIResponse, AppSettings, Conversation, SystemInfo, ToolResult } from '@shared/types';\r\nimport { useAppStore } from '@renderer/store/appStore';\r\n\r\n// Hook for accessing the Electron API\r\nexport const useElectronAPI = () => {\r\n  const api = window.electronAPI;\r\n  \r\n  if (!api) {\r\n    throw new Error('Electron API not available. Make sure the app is running in Electron.');\r\n  }\r\n  \r\n  return api;\r\n};\r\n\r\n// Hook for settings management\r\nexport const useSettings = () => {\r\n  const api = useElectronAPI();\r\n  const { setSettings, setError, setLoading } = useAppStore();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const loadSettings = useCallback(async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await api.settings.get();\r\n      if (response.success) {\r\n        setSettings(response.data as AppSettings);\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load settings');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [api.settings, setSettings, setError]);\r\n\r\n  const updateSettings = useCallback(async (updates: Partial<AppSettings>) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await api.settings.set(updates);\r\n      if (response.success) {\r\n        await loadSettings(); // Reload settings after update\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to update settings');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [api.settings, loadSettings, setError]);\r\n\r\n  const resetSettings = useCallback(async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await api.settings.reset();\r\n      if (response.success) {\r\n        await loadSettings(); // Reload settings after reset\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to reset settings');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [api.settings, loadSettings, setError]);\r\n\r\n  return {\r\n    loadSettings,\r\n    updateSettings,\r\n    resetSettings,\r\n    isLoading,\r\n  };\r\n};\r\n\r\n// Hook for conversation management\r\nexport const useConversations = () => {\r\n  const api = useElectronAPI();\r\n  const { setConversations, setError } = useAppStore();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const loadConversations = useCallback(async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await api.conversations.list();\r\n      if (response.success) {\r\n        setConversations(response.data as Conversation[]);\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load conversations');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [api.conversations, setConversations, setError]);\r\n\r\n  const createConversation = useCallback(async (title: string) => {\r\n    try {\r\n      const response = await api.conversations.create({ title });\r\n      if (response.success) {\r\n        await loadConversations(); // Reload conversations\r\n        return response.data as Conversation;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to create conversation');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    }\r\n  }, [api.conversations, loadConversations, setError]);\r\n\r\n  const deleteConversation = useCallback(async (id: string) => {\r\n    try {\r\n      const response = await api.conversations.delete({ id });\r\n      if (response.success) {\r\n        await loadConversations(); // Reload conversations\r\n        return true;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to delete conversation');\r\n        return false;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return false;\r\n    }\r\n  }, [api.conversations, loadConversations, setError]);\r\n\r\n  return {\r\n    loadConversations,\r\n    createConversation,\r\n    deleteConversation,\r\n    isLoading,\r\n  };\r\n};\r\n\r\n// Hook for system information\r\nexport const useSystemInfo = () => {\r\n  const api = useElectronAPI();\r\n  const { setSystemInfo, setError } = useAppStore();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const loadSystemInfo = useCallback(async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await api.system.info();\r\n      if (response.success) {\r\n        setSystemInfo(response.data as SystemInfo);\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load system information');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [api.system, setSystemInfo, setError]);\r\n\r\n  return {\r\n    loadSystemInfo,\r\n    isLoading,\r\n  };\r\n};\r\n\r\n// Hook for LLM communication\r\nexport const useLLM = () => {\r\n  const api = useElectronAPI();\r\n  const { \r\n    setStreaming, \r\n    setStreamingMessage, \r\n    appendStreamingMessage, \r\n    clearStreamingMessage,\r\n    setError \r\n  } = useAppStore();\r\n\r\n  const sendMessage = useCallback(async (\r\n    messages: any[], \r\n    systemPrompt?: string\r\n  ): Promise<string | null> => {\r\n    try {\r\n      const response = await api.llm.chat({ messages, systemPrompt });\r\n      if (response.success) {\r\n        return response.data.content;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to send message');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    }\r\n  }, [api.llm, setError]);\r\n\r\n  const streamMessage = useCallback(async (\r\n    messages: any[], \r\n    systemPrompt?: string,\r\n    onChunk?: (chunk: string) => void\r\n  ): Promise<void> => {\r\n    setStreaming(true);\r\n    clearStreamingMessage();\r\n    \r\n    try {\r\n      const response = await api.llm.stream({ messages, systemPrompt });\r\n      if (response.success) {\r\n        // Handle streaming response\r\n        // Note: In a real implementation, this would need to handle the streaming protocol\r\n        console.log('Streaming response:', response.data);\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to stream message');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    } finally {\r\n      setStreaming(false);\r\n    }\r\n  }, [api.llm, setStreaming, clearStreamingMessage, setError]);\r\n\r\n  return {\r\n    sendMessage,\r\n    streamMessage,\r\n  };\r\n};\r\n\r\n// Hook for agent operations\r\nexport const useAgent = () => {\r\n  const api = useElectronAPI();\r\n  const { \r\n    setCurrentPlan, \r\n    setProcessing, \r\n    addToolResult, \r\n    setError \r\n  } = useAppStore();\r\n\r\n  const createPlan = useCallback(async (conversationId: string, executionMode?: 'confirm' | 'yolo') => {\r\n    setProcessing(true);\r\n    try {\r\n      const response = await api.agent.plan({ conversationId, executionMode });\r\n      if (response.success) {\r\n        setCurrentPlan(response.data.plan);\r\n        return response.data.plan;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to create plan');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    } finally {\r\n      setProcessing(false);\r\n    }\r\n  }, [api.agent, setCurrentPlan, setProcessing, setError]);\r\n\r\n  const executePlan = useCallback(async (conversationId: string, stepId?: string) => {\r\n    setProcessing(true);\r\n    try {\r\n      const response = await api.agent.execute({ conversationId, stepId });\r\n      if (response.success) {\r\n        const results = response.data.results as ToolResult[];\r\n        if (results) {\r\n          results.forEach(result => addToolResult(result));\r\n        }\r\n        return response.data;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to execute plan');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    } finally {\r\n      setProcessing(false);\r\n    }\r\n  }, [api.agent, addToolResult, setProcessing, setError]);\r\n\r\n  const confirmExecution = useCallback(async (conversationId: string, approved: boolean) => {\r\n    try {\r\n      const response = await api.agent.confirm({ conversationId, approved });\r\n      if (response.success) {\r\n        return true;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to confirm execution');\r\n        return false;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return false;\r\n    }\r\n  }, [api.agent, setError]);\r\n\r\n  return {\r\n    createPlan,\r\n    executePlan,\r\n    confirmExecution,\r\n  };\r\n};\r\n\r\n// Hook for tools\r\nexport const useTools = () => {\r\n  const api = useElectronAPI();\r\n  const { setError } = useAppStore();\r\n  const [availableTools, setAvailableTools] = useState<any[]>([]);\r\n\r\n  const loadTools = useCallback(async () => {\r\n    try {\r\n      const response = await api.tools.list();\r\n      if (response.success) {\r\n        setAvailableTools(response.data);\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to load tools');\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n    }\r\n  }, [api.tools, setError]);\r\n\r\n  const executeTool = useCallback(async (name: string, args: Record<string, any>) => {\r\n    try {\r\n      const response = await api.tools.execute({ name, arguments: args });\r\n      if (response.success) {\r\n        return response.data as ToolResult;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Tool execution failed');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    }\r\n  }, [api.tools, setError]);\r\n\r\n  useEffect(() => {\r\n    loadTools();\r\n  }, [loadTools]);\r\n\r\n  return {\r\n    availableTools,\r\n    loadTools,\r\n    executeTool,\r\n  };\r\n};\r\n\r\n// Hook for file system operations\r\nexport const useFileSystem = () => {\r\n  const api = useElectronAPI();\r\n  const { setError } = useAppStore();\r\n\r\n  const readFile = useCallback(async (path: string) => {\r\n    try {\r\n      const response = await api.fs.read({ path });\r\n      if (response.success) {\r\n        return response.data;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to read file');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    }\r\n  }, [api.fs, setError]);\r\n\r\n  const writeFile = useCallback(async (path: string, content: string) => {\r\n    try {\r\n      const response = await api.fs.write({ path, content });\r\n      if (response.success) {\r\n        return true;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to write file');\r\n        return false;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return false;\r\n    }\r\n  }, [api.fs, setError]);\r\n\r\n  const listDirectory = useCallback(async (path: string) => {\r\n    try {\r\n      const response = await api.fs.list({ path });\r\n      if (response.success) {\r\n        return response.data;\r\n      } else {\r\n        setError(typeof response.error === 'string' ? response.error : response.error?.message || 'Failed to list directory');\r\n        return null;\r\n      }\r\n    } catch (error: any) {\r\n      setError(error.message);\r\n      return null;\r\n    }\r\n  }, [api.fs, setError]);\r\n\r\n  return {\r\n    readFile,\r\n    writeFile,\r\n    listDirectory,\r\n  };\r\n};\r\n\r\n// Hook for window controls\r\nexport const useWindowControls = () => {\r\n  const api = useElectronAPI();\r\n\r\n  const minimize = useCallback(() => {\r\n    api.window.minimize().catch(console.error);\r\n  }, [api.window]);\r\n\r\n  const maximize = useCallback(() => {\r\n    api.window.maximize().catch(console.error);\r\n  }, [api.window]);\r\n\r\n  const close = useCallback(() => {\r\n    api.window.close().catch(console.error);\r\n  }, [api.window]);\r\n\r\n  return {\r\n    minimize,\r\n    maximize,\r\n    close,\r\n  };\r\n};\r\n\r\n// Hook for YOLO mode detection\r\nexport const useIsYoloMode = () => {\r\n  const { settings } = useAppStore();\r\n  return settings?.agent?.defaultExecutionMode === 'yolo';\r\n};\r\n\r\n// Hook for keyboard shortcuts\r\nexport const useKeyboardShortcuts = () => {\r\n  const { currentConversation, settingsOpen, sidebarOpen } = useAppStore();\r\n  const { createConversation } = useConversations();\r\n  const { minimize, maximize, close } = useWindowControls();\r\n\r\n  useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      // Ctrl/Cmd + N: New conversation\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {\r\n        event.preventDefault();\r\n        createConversation('New Conversation');\r\n      }\r\n\r\n      // Ctrl/Cmd + ,: Open settings\r\n      if ((event.ctrlKey || event.metaKey) && event.key === ',') {\r\n        event.preventDefault();\r\n        useAppStore.getState().setSettingsOpen(!settingsOpen);\r\n      }\r\n\r\n      // Ctrl/Cmd + B: Toggle sidebar\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {\r\n        event.preventDefault();\r\n        useAppStore.getState().setSidebarOpen(!sidebarOpen);\r\n      }\r\n\r\n      // Ctrl/Cmd + W: Close window\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'w') {\r\n        event.preventDefault();\r\n        close();\r\n      }\r\n\r\n      // Ctrl/Cmd + M: Minimize window\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'm') {\r\n        event.preventDefault();\r\n        minimize();\r\n      }\r\n\r\n      // F11: Toggle maximize\r\n      if (event.key === 'F11') {\r\n        event.preventDefault();\r\n        maximize();\r\n      }\r\n\r\n      // Escape: Close modals/settings\r\n      if (event.key === 'Escape') {\r\n        if (settingsOpen) {\r\n          useAppStore.getState().setSettingsOpen(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    return () => window.removeEventListener('keydown', handleKeyDown);\r\n  }, [createConversation, settingsOpen, sidebarOpen, close, minimize, maximize]);\r\n};\r\n\r\n// Hook for streaming message handling\r\nexport const useStreamingMessage = () => {\r\n  const { isStreaming, streamingMessage } = useAppStore();\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n\r\n  useEffect(() => {\r\n    if (isStreaming && streamingMessage) {\r\n      setCurrentMessage(streamingMessage);\r\n    } else if (!isStreaming) {\r\n      setCurrentMessage('');\r\n    }\r\n  }, [isStreaming, streamingMessage]);\r\n\r\n  return {\r\n    isStreaming,\r\n    currentMessage,\r\n  };\r\n};\r\n\r\n// Hook for diff viewing\r\nexport const useDiffViewer = () => {\r\n  const [diffData, setDiffData] = useState<{\r\n    oldContent: string;\r\n    newContent: string;\r\n    fileName: string;\r\n  } | null>(null);\r\n\r\n  const showDiff = useCallback((oldContent: string, newContent: string, fileName: string) => {\r\n    setDiffData({ oldContent, newContent, fileName });\r\n  }, []);\r\n\r\n  const hideDiff = useCallback(() => {\r\n    setDiffData(null);\r\n  }, []);\r\n\r\n  return {\r\n    diffData,\r\n    showDiff,\r\n    hideDiff,\r\n  };\r\n};\r\n\r\n// Hook for toast notifications\r\nexport const useToast = () => {\r\n  const [toasts, setToasts] = useState<Array<{\r\n    id: string;\r\n    type: 'success' | 'error' | 'warning' | 'info';\r\n    message: string;\r\n    duration?: number;\r\n  }>>([]);\r\n\r\n  const showToast = useCallback((\r\n    type: 'success' | 'error' | 'warning' | 'info',\r\n    message: string,\r\n    duration = 5000\r\n  ) => {\r\n    const id = Math.random().toString(36).substr(2, 9);\r\n    const toast = { id, type, message, duration };\r\n\r\n    setToasts(prev => [...prev, toast]);\r\n\r\n    if (duration > 0) {\r\n      setTimeout(() => {\r\n        setToasts(prev => prev.filter(t => t.id !== id));\r\n      }, duration);\r\n    }\r\n  }, []);\r\n\r\n  const hideToast = useCallback((id: string) => {\r\n    setToasts(prev => prev.filter(t => t.id !== id));\r\n  }, []);\r\n\r\n  const clearToasts = useCallback(() => {\r\n    setToasts([]);\r\n  }, []);\r\n\r\n  return {\r\n    toasts,\r\n    showToast,\r\n    hideToast,\r\n    clearToasts,\r\n  };\r\n};", "import { useState, useEffect, useCallback, useRef } from 'react';\nimport { useAppStore } from '@renderer/store/appStore';\n\n// Hook for debounced values\nexport const useDebounce = <T>(value: T, delay: number): T => {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n};\n\n// Hook for local storage\nexport const useLocalStorage = <T>(\n  key: string,\n  initialValue: T\n): [T, (value: T | ((val: T) => T)) => void] => {\n  const [storedValue, setStoredValue] = useState<T>(() => {\n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? JSON.parse(item) : initialValue;\n    } catch (error) {\n      console.error(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  });\n\n  const setValue = useCallback((value: T | ((val: T) => T)) => {\n    try {\n      const valueToStore = value instanceof Function ? value(storedValue) : value;\n      setStoredValue(valueToStore);\n      window.localStorage.setItem(key, JSON.stringify(valueToStore));\n    } catch (error) {\n      console.error(`Error setting localStorage key \"${key}\":`, error);\n    }\n  }, [key, storedValue]);\n\n  return [storedValue, setValue];\n};\n\n// Hook for clipboard operations\nexport const useClipboard = () => {\n  const [copied, setCopied] = useState(false);\n\n  const copyToClipboard = useCallback(async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n      return true;\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      return false;\n    }\n  }, []);\n\n  const readFromClipboard = useCallback(async () => {\n    try {\n      return await navigator.clipboard.readText();\n    } catch (error) {\n      console.error('Failed to read from clipboard:', error);\n      return null;\n    }\n  }, []);\n\n  return {\n    copied,\n    copyToClipboard,\n    readFromClipboard,\n  };\n};\n\n// Hook for online/offline status\nexport const useOnlineStatus = () => {\n  const [isOnline, setIsOnline] = useState(navigator.onLine);\n\n  useEffect(() => {\n    const handleOnline = () => setIsOnline(true);\n    const handleOffline = () => setIsOnline(false);\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, []);\n\n  return isOnline;\n};\n\n// Hook for window size\nexport const useWindowSize = () => {\n  const [windowSize, setWindowSize] = useState({\n    width: window.innerWidth,\n    height: window.innerHeight,\n  });\n\n  useEffect(() => {\n    const handleResize = () => {\n      setWindowSize({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  return windowSize;\n};\n\n// Hook for previous value\nexport const usePrevious = <T>(value: T): T | undefined => {\n  const ref = useRef<T>();\n  \n  useEffect(() => {\n    ref.current = value;\n  });\n  \n  return ref.current;\n};\n\n// Hook for interval\nexport const useInterval = (callback: () => void, delay: number | null) => {\n  const savedCallback = useRef(callback);\n\n  useEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n\n  useEffect(() => {\n    if (delay === null) return;\n\n    const id = setInterval(() => savedCallback.current(), delay);\n    return () => clearInterval(id);\n  }, [delay]);\n};\n\n// Hook for timeout\nexport const useTimeout = (callback: () => void, delay: number | null) => {\n  const savedCallback = useRef(callback);\n\n  useEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n\n  useEffect(() => {\n    if (delay === null) return;\n\n    const id = setTimeout(() => savedCallback.current(), delay);\n    return () => clearTimeout(id);\n  }, [delay]);\n};\n\n// Hook for async operation\nexport const useAsync = <T, E = string>(\n  asyncFunction: () => Promise<T>,\n  immediate = true\n) => {\n  const [status, setStatus] = useState<'idle' | 'pending' | 'success' | 'error'>('idle');\n  const [value, setValue] = useState<T | null>(null);\n  const [error, setError] = useState<E | null>(null);\n\n  const execute = useCallback(async () => {\n    setStatus('pending');\n    setValue(null);\n    setError(null);\n\n    try {\n      const response = await asyncFunction();\n      setValue(response);\n      setStatus('success');\n    } catch (error: any) {\n      setError(error);\n      setStatus('error');\n    }\n  }, [asyncFunction]);\n\n  useEffect(() => {\n    if (immediate) {\n      execute();\n    }\n  }, [execute, immediate]);\n\n  return { execute, status, value, error };\n};\n\n// Hook for theme detection\nexport const useTheme = () => {\n  const { settings } = useAppStore();\n  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('dark');\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n\n    const handleChange = (e: MediaQueryListEvent) => {\n      setSystemTheme(e.matches ? 'dark' : 'light');\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  const currentTheme = settings?.ui?.theme === 'system' ? systemTheme : settings?.ui?.theme || 'dark';\n\n  return {\n    theme: currentTheme,\n    systemTheme,\n    isDark: currentTheme === 'dark',\n  };\n};\n\n// Hook for focus trap\nexport const useFocusTrap = (isActive: boolean) => {\n  const containerRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    if (!isActive || !containerRef.current) return;\n\n    const container = containerRef.current;\n    const focusableElements = container.querySelectorAll(\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n    );\n    const firstElement = focusableElements[0] as HTMLElement;\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\n\n    const handleTabKey = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab') return;\n\n      if (e.shiftKey) {\n        if (document.activeElement === firstElement) {\n          lastElement?.focus();\n          e.preventDefault();\n        }\n      } else {\n        if (document.activeElement === lastElement) {\n          firstElement?.focus();\n          e.preventDefault();\n        }\n      }\n    };\n\n    container.addEventListener('keydown', handleTabKey);\n    firstElement?.focus();\n\n    return () => {\n      container.removeEventListener('keydown', handleTabKey);\n    };\n  }, [isActive]);\n\n  return containerRef;\n};\n\n// Hook for outside click detection\nexport const useOutsideClick = (callback: () => void) => {\n  const ref = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const handleClick = (event: MouseEvent) => {\n      if (ref.current && !ref.current.contains(event.target as Node)) {\n        callback();\n      }\n    };\n\n    document.addEventListener('mousedown', handleClick);\n    return () => document.removeEventListener('mousedown', handleClick);\n  }, [callback]);\n\n  return ref;\n};\n\n// Hook for scroll position\nexport const useScrollPosition = () => {\n  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrollPosition({\n        x: window.scrollX,\n        y: window.scrollY,\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return scrollPosition;\n};\n", "import React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport App from './App';\r\nimport './styles/globals.css';\r\n\r\n// Error boundary for development\r\nclass ErrorBoundary extends React.Component<\r\n  { children: React.ReactNode },\r\n  { hasError: boolean; error?: Error }\r\n> {\r\n  constructor(props: { children: React.ReactNode }) {\r\n    super(props);\r\n    this.state = { hasError: false };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error) {\r\n    return { hasError: true, error };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\r\n    console.error('React Error Boundary caught an error:', error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      return (\r\n        <div className=\"min-h-screen bg-gray-900 flex items-center justify-center p-4\">\r\n          <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-8 max-w-lg w-full\">\r\n            <div className=\"flex items-center space-x-3 mb-4\">\r\n              <div className=\"w-8 h-8 bg-danger-600 rounded-full flex items-center justify-center\">\r\n                <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h2 className=\"text-xl font-semibold text-gray-100\">Application Error</h2>\r\n            </div>\r\n            \r\n            <p className=\"text-gray-300 mb-4\">\r\n              Something went wrong while loading the application. This is likely a development issue.\r\n            </p>\r\n            \r\n            {this.state.error && (\r\n              <details className=\"bg-gray-900 border border-gray-600 rounded p-3 mb-4\">\r\n                <summary className=\"text-sm font-medium text-gray-200 cursor-pointer\">\r\n                  Error Details\r\n                </summary>\r\n                <pre className=\"text-xs text-gray-400 mt-2 overflow-x-auto\">\r\n                  {this.state.error.message}\r\n                  {'\\n\\n'}\r\n                  {this.state.error.stack}\r\n                </pre>\r\n              </details>\r\n            )}\r\n            \r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                onClick={() => window.location.reload()}\r\n                className=\"btn btn-primary btn-sm\"\r\n              >\r\n                Reload App\r\n              </button>\r\n              <button\r\n                onClick={() => this.setState({ hasError: false, error: undefined })}\r\n                className=\"btn btn-secondary btn-sm\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\n// Initialize the React app\r\nconst container = document.getElementById('root');\r\nif (!container) {\r\n  throw new Error('Root element not found');\r\n}\r\n\r\nconst root = createRoot(container);\r\n\r\nroot.render(\r\n  <React.StrictMode>\r\n    <ErrorBoundary>\r\n      <App />\r\n    </ErrorBoundary>\r\n  </React.StrictMode>\r\n);", "import { create } from 'zustand';\r\nimport { subscribeWithSelector } from 'zustand/middleware';\r\nimport type { \r\n  AppSettings, \r\n  Conversation, \r\n  Message, \r\n  AgentState,\r\n  SystemInfo,\r\n  ToolResult,\r\n  AgentPlan\r\n} from '@shared/types';\r\n\r\nexport interface AppState {\r\n  // UI State\r\n  sidebarOpen: boolean;\r\n  settingsOpen: boolean;\r\n  currentView: 'chat' | 'settings' | 'logs';\r\n  theme: 'light' | 'dark' | 'system';\r\n  loading: boolean;\r\n  \r\n  // Settings\r\n  settings: AppSettings | null;\r\n  \r\n  // Conversations\r\n  conversations: Conversation[];\r\n  currentConversationId: string | null;\r\n  currentConversation: Conversation | null;\r\n  \r\n  // Agent State\r\n  agentState: AgentState | null;\r\n  isProcessing: boolean;\r\n  currentPlan: AgentPlan | null;\r\n  toolResults: ToolResult[];\r\n  \r\n  // System Info\r\n  systemInfo: SystemInfo | null;\r\n  \r\n  // Error State\r\n  error: string | null;\r\n  \r\n  // Streaming State\r\n  streamingMessage: string;\r\n  isStreaming: boolean;\r\n}\r\n\r\nexport interface AppActions {\r\n  // UI Actions\r\n  setSidebarOpen: (open: boolean) => void;\r\n  setSettingsOpen: (open: boolean) => void;\r\n  setCurrentView: (view: AppState['currentView']) => void;\r\n  setTheme: (theme: AppState['theme']) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  \r\n  // Settings Actions\r\n  setSettings: (settings: AppSettings) => void;\r\n  updateSettings: (updates: Partial<AppSettings>) => void;\r\n  \r\n  // Conversation Actions\r\n  setConversations: (conversations: Conversation[]) => void;\r\n  addConversation: (conversation: Conversation) => void;\r\n  updateConversation: (id: string, updates: Partial<Conversation>) => void;\r\n  deleteConversation: (id: string) => void;\r\n  setCurrentConversation: (id: string | null) => void;\r\n  \r\n  // Message Actions\r\n  addMessage: (conversationId: string, message: Message) => void;\r\n  updateMessage: (conversationId: string, messageId: string, updates: Partial<Message>) => void;\r\n  \r\n  // Agent Actions\r\n  setAgentState: (state: AgentState) => void;\r\n  setProcessing: (processing: boolean) => void;\r\n  setCurrentPlan: (plan: AgentPlan | null) => void;\r\n  addToolResult: (result: ToolResult) => void;\r\n  clearToolResults: () => void;\r\n  confirmPlan: (planId: string) => void;\r\n  rejectPlan: (planId: string) => void;\r\n  stopGeneration: () => void;\r\n  \r\n  // System Actions\r\n  setSystemInfo: (info: SystemInfo) => void;\r\n  \r\n  // Error Actions\r\n  setError: (error: string | null) => void;\r\n  clearError: () => void;\r\n  \r\n  // Streaming Actions\r\n  setStreamingMessage: (message: string) => void;\r\n  appendStreamingMessage: (chunk: string) => void;\r\n  setStreaming: (streaming: boolean) => void;\r\n  clearStreamingMessage: () => void;\r\n  \r\n  // Composite Actions\r\n  startNewConversation: (title: string) => void;\r\n  sendMessage: (conversationId: string, content: string) => void;\r\n  reset: () => void;\r\n}\r\n\r\nconst initialState: AppState = {\r\n  sidebarOpen: true,\r\n  settingsOpen: false,\r\n  currentView: 'chat',\r\n  theme: 'system',\r\n  loading: false,\r\n  \r\n  settings: null,\r\n  \r\n  conversations: [],\r\n  currentConversationId: null,\r\n  currentConversation: null,\r\n  \r\n  agentState: null,\r\n  isProcessing: false,\r\n  currentPlan: null,\r\n  toolResults: [],\r\n  \r\n  systemInfo: null,\r\n  \r\n  error: null,\r\n  \r\n  streamingMessage: '',\r\n  isStreaming: false,\r\n};\r\n\r\nexport const useAppStore = create<AppState & AppActions>()(\r\n  subscribeWithSelector((set, get) => ({\r\n    ...initialState,\r\n    \r\n    // UI Actions\r\n    setSidebarOpen: (open) => set({ sidebarOpen: open }),\r\n    setSettingsOpen: (open) => set({ settingsOpen: open }),\r\n    setCurrentView: (view) => set({ currentView: view }),\r\n    setTheme: (theme) => set({ theme }),\r\n    setLoading: (loading) => set({ loading }),\r\n    \r\n    // Settings Actions\r\n    setSettings: (settings) => set({ settings }),\r\n    updateSettings: (updates) => {\r\n      const currentSettings = get().settings;\r\n      if (currentSettings) {\r\n        set({ settings: { ...currentSettings, ...updates } });\r\n      }\r\n    },\r\n    \r\n    // Conversation Actions\r\n    setConversations: (conversations) => set({ conversations }),\r\n    addConversation: (conversation) => {\r\n      const conversations = get().conversations;\r\n      set({ \r\n        conversations: [conversation, ...conversations],\r\n        currentConversationId: conversation.id,\r\n        currentConversation: conversation,\r\n      });\r\n    },\r\n    updateConversation: (id, updates) => {\r\n      const conversations = get().conversations;\r\n      const updatedConversations = conversations.map(conv =>\r\n        conv.id === id ? { ...conv, ...updates } : conv\r\n      );\r\n      set({ conversations: updatedConversations });\r\n      \r\n      // Update current conversation if it's the one being updated\r\n      if (get().currentConversationId === id) {\r\n        const updatedCurrent = updatedConversations.find(conv => conv.id === id);\r\n        set({ currentConversation: updatedCurrent || null });\r\n      }\r\n    },\r\n    deleteConversation: (id) => {\r\n      const conversations = get().conversations;\r\n      const filteredConversations = conversations.filter(conv => conv.id !== id);\r\n      set({ conversations: filteredConversations });\r\n      \r\n      // Clear current conversation if it was deleted\r\n      if (get().currentConversationId === id) {\r\n        const newCurrent = filteredConversations[0] || null;\r\n        set({ \r\n          currentConversationId: newCurrent?.id || null,\r\n          currentConversation: newCurrent,\r\n        });\r\n      }\r\n    },\r\n    setCurrentConversation: (id) => {\r\n      if (id === null) {\r\n        set({ currentConversationId: null, currentConversation: null });\r\n        return;\r\n      }\r\n      \r\n      const conversation = get().conversations.find(conv => conv.id === id);\r\n      set({ \r\n        currentConversationId: id,\r\n        currentConversation: conversation || null,\r\n      });\r\n    },\r\n    \r\n    // Message Actions\r\n    addMessage: (conversationId, message) => {\r\n      const conversations = get().conversations;\r\n      const updatedConversations = conversations.map(conv => {\r\n        if (conv.id === conversationId) {\r\n          return {\r\n            ...conv,\r\n            messages: [...conv.messages, message],\r\n            updatedAt: new Date(),\r\n          };\r\n        }\r\n        return conv;\r\n      });\r\n      \r\n      set({ conversations: updatedConversations });\r\n      \r\n      // Update current conversation if it's the active one\r\n      if (get().currentConversationId === conversationId) {\r\n        const updatedCurrent = updatedConversations.find(conv => conv.id === conversationId);\r\n        set({ currentConversation: updatedCurrent || null });\r\n      }\r\n    },\r\n    updateMessage: (conversationId, messageId, updates) => {\r\n      const conversations = get().conversations;\r\n      const updatedConversations = conversations.map(conv => {\r\n        if (conv.id === conversationId) {\r\n          const updatedMessages = conv.messages.map(msg =>\r\n            msg.id === messageId ? { ...msg, ...updates } : msg\r\n          );\r\n          return { ...conv, messages: updatedMessages };\r\n        }\r\n        return conv;\r\n      });\r\n      \r\n      set({ conversations: updatedConversations });\r\n      \r\n      // Update current conversation if it's the active one\r\n      if (get().currentConversationId === conversationId) {\r\n        const updatedCurrent = updatedConversations.find(conv => conv.id === conversationId);\r\n        set({ currentConversation: updatedCurrent || null });\r\n      }\r\n    },\r\n    \r\n    // Agent Actions\r\n    setAgentState: (state) => set({ agentState: state }),\r\n    setProcessing: (processing) => set({ isProcessing: processing }),\r\n    setCurrentPlan: (plan) => set({ currentPlan: plan }),\r\n    addToolResult: (result) => {\r\n      const toolResults = get().toolResults;\r\n      set({ toolResults: [...toolResults, result] });\r\n    },\r\n    clearToolResults: () => set({ toolResults: [] }),\r\n    confirmPlan: (planId) => {\r\n      // TODO: Implement plan confirmation logic\r\n      console.log('Plan confirmed:', planId);\r\n      set({ currentPlan: null, agentState: { ...get().agentState, isWaitingForConfirmation: false, status: 'idle' } });\r\n    },\r\n    rejectPlan: (planId) => {\r\n      // TODO: Implement plan rejection logic\r\n      console.log('Plan rejected:', planId);\r\n      set({ currentPlan: null, agentState: { ...get().agentState, isWaitingForConfirmation: false, status: 'idle' } });\r\n    },\r\n    stopGeneration: () => {\r\n      // TODO: Implement stop generation logic\r\n      console.log('Generation stopped');\r\n      set({ isStreaming: false, streamingMessage: '' });\r\n    },\r\n    \r\n    // System Actions\r\n    setSystemInfo: (info) => set({ systemInfo: info }),\r\n    \r\n    // Error Actions\r\n    setError: (error) => set({ error }),\r\n    clearError: () => set({ error: null }),\r\n    \r\n    // Streaming Actions\r\n    setStreamingMessage: (message) => set({ streamingMessage: message }),\r\n    appendStreamingMessage: (chunk) => {\r\n      const current = get().streamingMessage;\r\n      set({ streamingMessage: current + chunk });\r\n    },\r\n    setStreaming: (streaming) => set({ isStreaming: streaming }),\r\n    clearStreamingMessage: () => set({ streamingMessage: '' }),\r\n    \r\n    // Composite Actions\r\n    startNewConversation: (title) => {\r\n      const newConversation: Conversation = {\r\n        id: Date.now().toString(),\r\n        title,\r\n        messages: [],\r\n        createdAt: new Date(),\r\n        updatedAt: new Date(),\r\n      };\r\n      \r\n      get().addConversation(newConversation);\r\n    },\r\n    sendMessage: (conversationId, content) => {\r\n      const message: Message = {\r\n        id: Date.now().toString(),\r\n        role: 'user',\r\n        content,\r\n        timestamp: new Date(),\r\n      };\r\n      \r\n      get().addMessage(conversationId, message);\r\n      \r\n      // TODO: Implement actual message sending to agent\r\n      console.log('Message sent:', content);\r\n    },\r\n    reset: () => set(initialState),\r\n  }))\r\n);\r\n\r\n// Selectors for computed values\r\nexport const useCurrentMessages = () => \r\n  useAppStore(state => state.currentConversation?.messages || []);\r\n\r\nexport const useHasActiveConversation = () => \r\n  useAppStore(state => !!state.currentConversation);\r\n\r\nexport const useExecutionMode = () => \r\n  useAppStore(state => state.settings?.agent.defaultExecutionMode || 'confirm');\r\n\r\nexport const useIsYoloMode = () => \r\n  useAppStore(state => state.settings?.agent.defaultExecutionMode === 'yolo');\r\n\r\nexport const useProvider = () => \r\n  useAppStore(state => state.settings?.llm.provider || 'openai');\r\n\r\nexport const useModel = () => \r\n  useAppStore(state => state.settings?.llm.model || 'gpt-4');\r\n\r\n// Subscriptions for side effects\r\nexport const subscribeToThemeChanges = (callback: (theme: string) => void) =>\r\n  useAppStore.subscribe(\r\n    (state) => state.theme,\r\n    callback\r\n  );\r\n\r\nexport const subscribeToConversationChanges = (callback: (conversation: Conversation | null) => void) =>\r\n  useAppStore.subscribe(\r\n    (state) => state.currentConversation,\r\n    callback\r\n  );\r\n\r\nexport const subscribeToSettingsChanges = (callback: (settings: AppSettings | null) => void) =>\r\n  useAppStore.subscribe(\r\n    (state) => state.settings,\r\n    callback\r\n  );", "\n      import API from \"!../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/dist/cjs.js!./globals.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/css-loader/dist/cjs.js!../../../node_modules/postcss-loader/dist/cjs.js!./globals.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import type { AppSettings } from './types';\n\n// Application constants\nexport const APP_NAME = 'AI Assistant';\nexport const APP_VERSION = '1.0.0';\nexport const APP_DESCRIPTION = 'Desktop AI Assistant with Multi-LLM Support';\n\n// Database constants\nexport const DB_CONSTANTS = {\n  DATABASE_NAME: 'ai-assistant.db',\n  BACKUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours in milliseconds\n  CLEANUP_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds\n  MAX_CONVERSATION_AGE: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds\n  MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB in bytes\n  VACUUM_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds\n};\n\n// LLM Provider constants\nexport const LLM_PROVIDERS = {\n  OPENAI: 'openai',\n  ANTHROPIC: 'anthropic',\n  DEEPSEEK: 'deepseek',\n} as const;\n\n// Default models for each provider\nexport const DEFAULT_MODELS = {\n  [LLM_PROVIDERS.OPENAI]: {\n    'gpt-4': 'GPT-4',\n    'gpt-4-turbo': 'GPT-4 Turbo',\n    'gpt-3.5-turbo': 'GPT-3.5 Turbo',\n  },\n  [LLM_PROVIDERS.ANTHROPIC]: {\n    'claude-3-opus-20240229': 'Claude 3 Opus',\n    'claude-3-sonnet-20240229': 'Claude 3 Sonnet',\n    'claude-3-haiku-20240307': 'Claude 3 Haiku',\n  },\n  [LLM_PROVIDERS.DEEPSEEK]: {\n    'deepseek-chat': 'DeepSeek Chat',\n    'deepseek-coder': 'DeepSeek Coder',\n  },\n} as const;\n\n// API endpoints\nexport const API_ENDPOINTS = {\n  [LLM_PROVIDERS.OPENAI]: 'https://api.openai.com/v1',\n  [LLM_PROVIDERS.ANTHROPIC]: 'https://api.anthropic.com/v1',\n  [LLM_PROVIDERS.DEEPSEEK]: 'https://api.deepseek.com/v1',\n} as const;\n\n// Token limits for different models\nexport const TOKEN_LIMITS = {\n  'gpt-4': 8192,\n  'gpt-4-turbo': 128000,\n  'gpt-3.5-turbo': 4096,\n  'claude-3-opus-20240229': 200000,\n  'claude-3-sonnet-20240229': 200000,\n  'claude-3-haiku-20240307': 200000,\n  'deepseek-chat': 32768,\n  'deepseek-coder': 32768,\n} as const;\n\n// UI constants\nexport const UI_CONSTANTS = {\n  SIDEBAR_WIDTH: 280,\n  SIDEBAR_COLLAPSED_WIDTH: 60,\n  HEADER_HEIGHT: 60,\n  MESSAGE_MAX_WIDTH: 800,\n  ANIMATION_DURATION: 200,\n  DEBOUNCE_DELAY: 300,\n  TOAST_DURATION: 5000,\n  TYPING_INDICATOR_DELAY: 1000,\n} as const;\n\n// Keyboard shortcuts\nexport const KEYBOARD_SHORTCUTS = {\n  NEW_CONVERSATION: 'Ctrl+N',\n  TOGGLE_SIDEBAR: 'Ctrl+B',\n  TOGGLE_SETTINGS: 'Ctrl+,',\n  FOCUS_INPUT: 'Ctrl+Enter',\n  CLOSE_MODAL: 'Escape',\n  TOGGLE_FULLSCREEN: 'F11',\n  TOGGLE_DEVTOOLS: 'F12',\n  RELOAD_APP: 'Ctrl+Shift+R',\n} as const;\n\n// Agent execution modes\nexport const EXECUTION_MODES = {\n  CONFIRM: 'confirm',\n  YOLO: 'yolo',\n} as const;\n\n// Tool categories\nexport const TOOL_CATEGORIES = {\n  FILE_SYSTEM: 'file_system',\n  SHELL: 'shell',\n  SEARCH: 'search',\n  NETWORK: 'network',\n  SYSTEM: 'system',\n} as const;\n\n// File type mappings\nexport const FILE_TYPES = {\n  TEXT: ['txt', 'md', 'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'],\n  CODE: ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'swift'],\n  WEB: ['html', 'css', 'scss', 'sass', 'less'],\n  DATA: ['csv', 'tsv', 'json', 'xml', 'sql'],\n  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],\n  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],\n  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],\n} as const;\n\n// Default application settings\nexport const DEFAULT_SETTINGS: AppSettings = {\n  llm: {\n    provider: 'openai' as const,\n    model: 'gpt-4',\n    apiKey: '',\n    temperature: 0.7,\n    maxTokens: 2048,\n  },\n  ui: {\n    theme: 'system' as const,\n    fontSize: 'medium' as const,\n    compactMode: false,\n  },\n  agent: {\n    defaultExecutionMode: 'confirm' as const,\n    autoSaveConversations: true,\n    maxContextLength: 32000,\n  },\n  tools: {\n    enabledTools: [\n      'run_shell_command',\n      'read_file',\n      'write_file',\n      'list_directory',\n      'glob_files',\n      'grep',\n      'replace_in_file',\n    ],\n    toolSettings: {},\n  },\n};\n\n// Error messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',\n  API_KEY_MISSING: 'API key is required for the selected provider.',\n  API_KEY_INVALID: 'Invalid API key. Please check your credentials.',\n  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',\n  MODEL_NOT_AVAILABLE: 'Selected model is not available.',\n  TOOL_EXECUTION_FAILED: 'Tool execution failed. Please try again.',\n  FILE_NOT_FOUND: 'File not found.',\n  PERMISSION_DENIED: 'Permission denied.',\n  INVALID_INPUT: 'Invalid input provided.',\n  UNKNOWN_ERROR: 'An unknown error occurred.',\n} as const;\n\n// Success messages\nexport const SUCCESS_MESSAGES = {\n  SETTINGS_SAVED: 'Settings saved successfully.',\n  CONVERSATION_CREATED: 'New conversation created.',\n  CONVERSATION_DELETED: 'Conversation deleted.',\n  FILE_SAVED: 'File saved successfully.',\n  TOOL_EXECUTED: 'Tool executed successfully.',\n  API_KEY_VALIDATED: 'API key validated successfully.',\n} as const;\n\n// Validation rules\nexport const VALIDATION_RULES = {\n  API_KEY_MIN_LENGTH: 10,\n  CONVERSATION_TITLE_MAX_LENGTH: 100,\n  MESSAGE_MAX_LENGTH: 10000,\n  FILENAME_MAX_LENGTH: 255,\n  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB\n  MAX_CONCURRENT_REQUESTS: 5,\n} as const;\n\n// Feature flags\nexport const FEATURE_FLAGS = {\n  ENABLE_VOICE_INPUT: false,\n  ENABLE_VOICE_OUTPUT: false,\n  ENABLE_PLUGINS: false,\n  ENABLE_CUSTOM_TOOLS: false,\n  ENABLE_COLLABORATION: false,\n  ENABLE_CLOUD_SYNC: false,\n} as const;\n\n// Development constants\nexport const DEV_CONSTANTS = {\n  MOCK_DELAY: 1000,\n  DEBUG_LOGGING: true,\n  HOT_RELOAD: true,\n  DEVTOOLS_ENABLED: true,\n} as const;\n\n// Production constants\nexport const PROD_CONSTANTS = {\n  TELEMETRY_ENDPOINT: 'https://telemetry.example.com',\n  UPDATE_CHECK_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours\n  CRASH_REPORT_ENDPOINT: 'https://crashes.example.com',\n  ANALYTICS_ENDPOINT: 'https://analytics.example.com',\n} as const;\n\n// Regular expressions\nexport const REGEX_PATTERNS = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  URL: /^https?:\\/\\/.+/,\n  API_KEY: /^[a-zA-Z0-9_-]+$/,\n  FILENAME: /^[^<>:\"/\\\\|?*]+$/,\n  VERSION: /^\\d+\\.\\d+\\.\\d+$/,\n} as const;\n\n// Color themes\nexport const COLOR_THEMES = {\n  DARK: {\n    primary: '#0ea5e9',\n    secondary: '#64748b',\n    background: '#0f172a',\n    surface: '#1e293b',\n    text: '#f8fafc',\n    textSecondary: '#cbd5e1',\n    border: '#334155',\n    success: '#22c55e',\n    warning: '#f59e0b',\n    error: '#ef4444',\n  },\n  LIGHT: {\n    primary: '#0ea5e9',\n    secondary: '#64748b',\n    background: '#ffffff',\n    surface: '#f8fafc',\n    text: '#0f172a',\n    textSecondary: '#475569',\n    border: '#e2e8f0',\n    success: '#22c55e',\n    warning: '#f59e0b',\n    error: '#ef4444',\n  },\n} as const;\n\n// Export types for constants\nexport type LLMProvider = typeof LLM_PROVIDERS[keyof typeof LLM_PROVIDERS];\nexport type ExecutionMode = typeof EXECUTION_MODES[keyof typeof EXECUTION_MODES];\nexport type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:process\");", "module.exports = require(\"node:url\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; (typeof current == 'object' || typeof current == 'function') && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".bundle.js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "var inProgress = {};\nvar dataWebpackPrefix = \"ai-assistant-desktop:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/^blob:/, \"\").replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl;", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"main\": 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = global[\"webpackChunkai_assistant_desktop\"] = global[\"webpackChunkai_assistant_desktop\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"vendors-node_modules_css-loader_dist_runtime_api_js-node_modules_css-loader_dist_runtime_getU-107cdf\"], () => (__webpack_require__(\"./src/renderer/index.tsx\")))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": [], "sourceRoot": ""}