export interface Message {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface Conversation {
    id: string;
    title: string;
    messages: Message[];
    createdAt: Date;
    updatedAt: Date;
    metadata?: Record<string, any>;
}
export interface LLMProvider {
    name: string;
    apiKey: string;
    baseUrl?: string;
    models: string[];
}
export interface LLMProviderConfig {
    provider: 'openai' | 'anthropic' | 'deepseek';
    model: string;
    apiKey: string;
    baseUrl?: string;
    maxTokens?: number;
    temperature?: number;
}
export interface LLMResponse {
    content: string;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    finish_reason?: string;
}
export interface StreamingLLMResponse {
    chunk: string;
    done: boolean;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}
export interface ToolParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required?: boolean;
    default?: any;
    enum?: string[];
}
export interface ToolSchema {
    name: string;
    description: string;
    parameters: ToolParameter[];
}
export interface ToolCall {
    id: string;
    name: string;
    arguments: Record<string, any>;
}
export interface ToolResult {
    id: string;
    name: string;
    success: boolean;
    result: any;
    error?: string;
    metadata?: Record<string, any>;
}
export interface AgentPlan {
    id: string;
    steps: PlanStep[];
    estimatedDuration?: number;
    estimated_duration?: number;
    requires_confirmation?: boolean;
}
export interface PlanStep {
    id: string;
    tool: string;
    type: string;
    arguments: Record<string, any>;
    description: string;
    depends_on?: string[];
}
export interface AgentState {
    currentPlan?: AgentPlan;
    executingStep?: string;
    toolResults: ToolResult[];
    isWaitingForConfirmation: boolean;
    executionMode: 'confirm' | 'yolo';
}
export interface UISettings {
    theme: 'light' | 'dark' | 'system';
    fontSize: 'small' | 'medium' | 'large';
    compactMode: boolean;
}
export interface AgentSettings {
    defaultExecutionMode: 'confirm' | 'yolo';
    autoSaveConversations: boolean;
    maxContextLength: number;
}
export interface ToolSettings {
    enabledTools: string[];
    toolSettings: Record<string, any>;
}
export interface AppSettings {
    llm: LLMProviderConfig;
    ui: UISettings;
    agent: AgentSettings;
    tools: ToolSettings;
}
export interface SystemInfo {
    platform: string;
    arch: string;
    nodeVersion: string;
    electronVersion: string;
    appVersion: string;
    memory: {
        total: number;
        free: number;
        used: number;
    };
    cpu: {
        model: string;
        cores: number;
        speed: number;
    };
    network: Record<string, NetworkInterfaceInfo[]>;
}
export interface NetworkInterfaceInfo {
    address: string;
    netmask: string;
    family: string;
    mac: string;
    internal: boolean;
    cidr?: string;
}
export interface DBConversation {
    id: string;
    title: string;
    created_at: string;
    updated_at: string;
    metadata: string;
}
export interface DBMessage {
    id: string;
    conversation_id: string;
    role: string;
    content: string;
    timestamp: string;
    metadata: string;
}
export interface DBSettings {
    key: string;
    value: string;
    updated_at: string;
}
export interface FileInfo {
    path: string;
    name: string;
    type: 'file' | 'directory';
    size?: number;
    modified?: Date;
    extension?: string;
}
export interface DiffResult {
    oldPath: string;
    newPath: string;
    hunks: DiffHunk[];
}
export interface DiffHunk {
    oldStart: number;
    oldLines: number;
    newStart: number;
    newLines: number;
    lines: DiffLine[];
}
export interface DiffLine {
    type: 'normal' | 'add' | 'delete';
    content: string;
    oldNumber?: number;
    newNumber?: number;
}
export interface IPCEvent<T = any> {
    type: string;
    payload: T;
    requestId?: string;
}
export interface AppError {
    code: string;
    message: string;
    details?: Record<string, any>;
    stack?: string;
}
export interface APIRequest<T = any> {
    method: string;
    params: T;
    requestId: string;
}
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: AppError;
    requestId: string;
}
export interface Context {
    files: string[];
    directories: string[];
    codeSymbols: string[];
    pastConversations: string[];
    systemInfo: boolean;
}
export interface StreamEvent {
    type: 'token' | 'tool_call' | 'tool_result' | 'error' | 'done';
    data: any;
}
//# sourceMappingURL=index.d.ts.map