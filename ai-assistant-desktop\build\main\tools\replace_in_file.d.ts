import type { ToolSchema } from '@shared/types';
export declare const category: "file_system";
export declare const schema: ToolSchema;
export declare function execute(args: {
    filePath: string;
    oldText: string;
    newText: string;
    createBackup?: boolean;
}): Promise<{
    success: boolean;
    message: string;
    changes: {
        filePath: string;
        backupPath?: string;
        oldTextHash: string;
        newTextHash: string;
        linesChanged: number;
        diff: string;
    };
    error?: string;
}>;
//# sourceMappingURL=replace_in_file.d.ts.map